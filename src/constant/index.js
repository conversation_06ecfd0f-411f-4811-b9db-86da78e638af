export const MENU_GROUP_LIST = [ '基础应用', '测试资产', '测试工具', '测试实施', '测试中心' ]
export const MENU_GROUP_OBJ = {
  basicApp: '基础应用',
  testAssets: '测试资产',
  testTools: '测试工具',
  testImplement: '测试实施',
  testCenter: '测试中心',
  otherApp: '外部应用'
}
// export const MENU_GROUP_MAP = {
//   // 基础应用
//   permission: MENU_GROUP_OBJ.basicApp, // 权限体系
//   management: MENU_GROUP_OBJ.basicApp, // 后台管理
//   base: MENU_GROUP_OBJ.basicApp, // 平台配置
//   leaderwork: MENU_GROUP_OBJ.basicApp, // 领导工作台
//   workflow: MENU_GROUP_OBJ.basicApp,
//
//   // 测试资产
//   wiki: MENU_GROUP_OBJ.testAssets, // 知识库
//   code: MENU_GROUP_OBJ.testAssets, // 代码库
//   maven_warehouse: MENU_GROUP_OBJ.testAssets, // Maven仓库
//
//   // 测试工具
//
//   // 测试实施
//   project: MENU_GROUP_OBJ.testImplement, // 项目
//   projectm: MENU_GROUP_OBJ.testImplement, // 项目集
//   producm: MENU_GROUP_OBJ.testImplement, // 产品
//   reqm_center: MENU_GROUP_OBJ.testImplement, // 需求
//   productfit: MENU_GROUP_OBJ.testImplement, // 产品集
//   pipeline: MENU_GROUP_OBJ.testImplement, // 流水线
//   package: MENU_GROUP_OBJ.testImplement, // 制品库
//   cmdb: MENU_GROUP_OBJ.testImplement, // 资源中心
//   testm_test_library: MENU_GROUP_OBJ.testImplement, // 测试管理
//   measure: MENU_GROUP_OBJ.testImplement, // 研发度量
//
//   // 测试中心
//   dashboard: MENU_GROUP_OBJ.testCenter, // 工作台
//   man_hour: MENU_GROUP_OBJ.testCenter, // 工时管理
//   si: MENU_GROUP_OBJ.testCenter,
//   release: MENU_GROUP_OBJ.testCenter
// }
export const MENU_GROUP_MAP = {
  // 【测试中心】
  dashboard: MENU_GROUP_OBJ.testCenter, // 工作台
  review_mgmt: MENU_GROUP_OBJ.testCenter, // 评审管理
  risk_mgmt: MENU_GROUP_OBJ.testCenter, // 风险管理
  man_hour: MENU_GROUP_OBJ.testCenter, // 工时管理
  statistics_report: MENU_GROUP_OBJ.testCenter, // 统计报表

  // 【测试实施】
  project: MENU_GROUP_OBJ.testImplement, // 项目
  reqm_center: MENU_GROUP_OBJ.testImplement, // 需求
  testm_test_library: MENU_GROUP_OBJ.testImplement, // 测试管理
  // 【测试工具】：暂无。
  ui_test: MENU_GROUP_OBJ.testTools, // UI自动化测试
  api_test: MENU_GROUP_OBJ.testTools, // 接口自动化测试
  performance_test: MENU_GROUP_OBJ.testTools, // 性能测试
  test_data_service: MENU_GROUP_OBJ.testTools, // 测试数据服务
  // 【测试资产】
  wiki: MENU_GROUP_OBJ.testAssets, // 知识库
  test_case_library: MENU_GROUP_OBJ.testAssets, // 用例库
  defect_library: MENU_GROUP_OBJ.testAssets, // 缺陷库
  tag_mgmt: MENU_GROUP_OBJ.testAssets, // 标签管理
  producm: MENU_GROUP_OBJ.testAssets, // 产品=> 系统交易
  cmdb: MENU_GROUP_OBJ.testAssets, // 资源中心=> 环境资源

  // 【基础应用】
  permission: MENU_GROUP_OBJ.basicApp, // 权限体系
  management: MENU_GROUP_OBJ.basicApp, // 后台管理
  base: MENU_GROUP_OBJ.basicApp, // 平台配置
  workflow: MENU_GROUP_OBJ.basicApp
}
