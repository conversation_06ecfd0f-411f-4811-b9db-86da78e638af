<template>
  <div class="wscn-http404-container">
    <div class="wscn-http404">
      <div class="pic-404">
        <!-- <img :src="srcdata"> -->
        <!-- <svg-icon style="font-size: 600px" :icon-class="$route.path == '/exception/404' ? '404' : $route.path == '/exception/401' ? '401' : '500' " /> -->
        <svg-icon style="font-size: 600px" icon-class="developing" />
      </div>
      <div class="bullshit">
        <div class="bullshit__position">
          <div class="bullshit__oops">OOPS!</div>
          <div class="bullshit__headline">{{ message }}</div>
          <div class="bullshit__info" />
          <a v-if="$route.path != '/exception/401'" class="bullshit__return-home" @click="backHome">返回首页</a>
          <a v-else class="bullshit__return-home" @click="logout">重新登录</a>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import { removeToken } from '@/utils/auth'
import { resetRouter } from '@/router'
import router from '@/router'
export default {
  name: 'Developing',
  data() {
    return {
    }
  },
  computed: {
    message() {
      let message = ''
      if (this.$route.path == '/exception/404') {
        message = '当前页面不存在，请联系管理员！'
      } else if (this.$route.path == '/exception/401') {
        message = '当前用户无权限或登录已失效，请重新登录！'
      } else if (this.$route.path == '/exception/500') {
        message = '当前服务异常，稍后请重新尝试！'
      }
      return message
    }
  },
  ...mapState({
    user: state => state.user.user
  }),
  mounted() {
  },
  methods: {
    backHome() {
      this.$router.push('/')
    },
    async logout() {
      // await this.$store.dispatch('user/logout', null)
      removeToken()
      resetRouter()
      router.push(`/login`)
    }
  }
}
</script>

<style lang="scss" scoped>
.wscn-http404-container{
  transform: translate(-50%,-50%);
  position: absolute;
  top: 50%;
  left: 50%;
}
.wscn-http404 {
  display: flex;
  padding: 0 50px;
  overflow: hidden;
  .pic-404 {
    width: 600px;
    overflow: hidden;
  }
  .bullshit {
    position: relative;
    width: 350px;
    padding: 30px 0;
    overflow: hidden;
    &__position {
      transform: translate(-50%,-50%);
      position: absolute;
      top: 50%;
      left: 50%;
    }
    &__oops {
      font-size: 32px;
      font-weight: bold;
      line-height: 40px;
      color: #1482f0;
      opacity: 0;
      margin-bottom: 20px;
      animation-name: slideUp;
      animation-duration: 0.5s;
      animation-fill-mode: forwards;
    }
    &__headline {
      font-size: 20px;
      line-height: 24px;
      color: #222;
      font-weight: bold;
      opacity: 0;
      margin-bottom: 10px;
      animation-name: slideUp;
      animation-duration: 0.5s;
      animation-delay: 0.1s;
      animation-fill-mode: forwards;
    }
    &__info {
      font-size: 13px;
      line-height: 21px;
      color: grey;
      opacity: 0;
      margin-bottom: 30px;
      animation-name: slideUp;
      animation-duration: 0.5s;
      animation-delay: 0.2s;
      animation-fill-mode: forwards;
    }
    &__return-home {
      display: block;
      width: 110px;
      height: 36px;
      background: #1482f0;
      border-radius: 100px;
      text-align: center;
      color: #ffffff;
      opacity: 0;
      font-size: 14px;
      line-height: 36px;
      cursor: pointer;
      animation-name: slideUp;
      animation-duration: 0.5s;
      animation-delay: 0.3s;
      animation-fill-mode: forwards;
    }
    @keyframes slideUp {
      0% {
        transform: translateY(60px);
        opacity: 0;
      }
      100% {
        transform: translateY(0);
        opacity: 1;
      }
    }
  }
}
.custom-theme-dark {
  .bullshit__headline {
    color: #E6E9F0;
  }
}
</style>
