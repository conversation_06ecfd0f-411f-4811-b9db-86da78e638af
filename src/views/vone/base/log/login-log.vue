<template>
  <page-wrapper>
    <vone-search-wrapper>
      <template slot="search">
        <vone-search-dynamic
          ref="searchForm"
          table-search-key="login-table"
          :model.sync="formData"
          :default-fileds.sync="defaultFileds"
          show-basic
          :extra.sync="extraData"
          :table-ref="$refs['login-table']"
          :show-column="false"
          @getTableData="getLogList"
        />

      </template>
      <template slot="fliter">
        <vone-search-filter
          :extra.sync="extraData"
          :model.sync="formData"
          :default-fileds.sync="defaultFileds"
          @getTableData="getLogList"
        />
      </template>
    </vone-search-wrapper>
    <div :style="{height: tableHeight}">
      <vxe-table
        ref="login-table"
        class="vone-vxe-table"
        border
        height="auto"
        show-overflow="tooltip"
        :loading="tableLoading"
        :empty-render="{ name: 'empty' }"
        :data="tableData.records"
        :column-config="{ resizable: true, minWidth: 120 }"
        :checkbox-config="{ reserve: true }"
        row-id="id"
      >
        <vxe-column title="标题" field="description" min-width="150" />
        <vxe-column title="登录账号" field="account" width="120" />
        <vxe-column title="操作人" field="userName" width="120" />
        <vxe-column title="目标机" field="requestIp" width="120" />
        <vxe-column title="浏览器" field="browser" width="120" />
        <vxe-column title="版本" field="browserVersion" width="120" />
        <vxe-column title="操作系统" field="operatingSystem" />
        <vxe-column title="登录时间" field="createTime" min-width="120" />
      </vxe-table>
    </div>
    <vone-pagination ref="pagination" :total="tableData.total" @update="getLogList" />
  </page-wrapper>
</template>

<script>
import { apiBaseLoginLogList } from '@/api/vone/base/log'

export default {
  data() {
    return {
      formData: {},
      tableLoading: false,
      tableData: {},
      tableOptions: {},
      extraData: {}, defaultFileds: [
        {
          key: 'account',
          name: '登录账号',
          type: {
            code: 'INPUT'
          },
          placeholder: '请输入登录账号'
        },
        {
          key: 'userName',
          name: '姓名',
          type: {
            code: 'INPUT'
          },
          placeholder: '请输入姓名'
        },
        {
          key: 'loginDate',
          name: '登录时间',
          type: {
            code: 'DATE'
          },
          placeholder: '请选择登录时间'
        }
      ]
    }
  },
  computed: {
    tableHeight() {
      const height = (this.extraData?.height || 0) + 'px'
      return `calc(${this.$reduceTableHeight} - ${height})`
    }
  },
  mounted() {
    // this.getLogList()
  },
  methods: {

    async getLogList() {
      console.log(this.formData, '----')
      this.tableLoading = true
      const pageObj = this.$refs.pagination?.pageObj || { current: 1, size: 20 }
      const sortObj = this.$refs.searchForm?.sortObj
      const params = {
        ...pageObj,
        ...sortObj,
        extra: {

          ...this.extraData
        },
        model: { ...this.formData }
      }
      const res = await apiBaseLoginLogList(params)
      this.tableLoading = false
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }

      this.tableData = res.data
    }

  }
}
</script>

<style>
</style>
