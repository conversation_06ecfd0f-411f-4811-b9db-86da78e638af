<template>
  <page-wrapper>
    <vone-search-wrapper>
      <template slot="search">
        <vone-search-dynamic
          ref="searchForm"
          table-search-key="baseTemplateTable"
          :model.sync="formData"
          :default-fileds.sync="defaultFileds"
          show-basic
          :extra.sync="extraData"
          :table-ref="$refs['baseTemplateTable']"
          @getTableData="getTableData"
        />
      </template>
      <template slot="actions">
        <el-button
          type="primary"
          icon="iconfont el-icon-tips-plus-circle"
          size="small"
          :disabled="!$permission('base_template_add')"
          @click="clickAddRow"
        >新增模版</el-button>
      </template>
      <template slot="fliter">
        <vone-search-filter
          :extra.sync="extraData"
          :model.sync="formData"
          :default-fileds.sync="defaultFileds"
          @getTableData="getTableData"
        />
      </template>
    </vone-search-wrapper>
    <div :style="{ height: tableHeight }">
      <vxe-table
        ref="baseTemplateTable"
        class="vone-vxe-table"
        border
        auto-resize
        height="auto"
        show-overflow="tooltip"
        :loading="pageLoading"
        :empty-render="{ name: 'empty' }"
        :data="data.records"
        :column-config="{ minWidth: '120px' }"
        :checkbox-config="{ reserve: true }"
        row-id="id"
      >
        <vxe-column type="checkbox" width="36" fixed="left" align="center" />
        <vxe-column title="图标" field="account" width="90" fixed="left">
          <template #default="{ row }">
            <svg class="icon" aria-hidden="true">
              <use :xlink:href="'#' + `${row.icon}`" />
            </svg>
          </template>
        </vxe-column>
        <vxe-column title="名称" field="name" width="200" />
        <vxe-column title="项目类型" field="code" width="150" />
        <vxe-column title="描述" field="description">
          <template #default="{ row }">
            {{ row.description }}
          </template>
        </vxe-column>
        <vxe-column title="状态" field="state" width="100">
          <template #default="{ row }">
            <el-switch
              v-model="row.state"
              class="openSwitch"
              :disabled="row.readonly || !$permission('base_template_edit')"
              active-text="启用"
              inactive-text="禁用"
              @change="editStatus(row)"
            />
          </template>
        </vxe-column>
        <vxe-column title="内置模版" field="readonly" width="100">
          <template #default="{ row }">
            <span>
              {{ row.readonly ? "是" : "否" }}
            </span>
          </template>
        </vxe-column>
        <vxe-column title="创建人" field="type" width="130">
          <template #default="{ row }">
            <vone-user-avatar v-if="row.echoMap&&row.echoMap.createdBy" :avatar-path="row.echoMap.createdBy.avatarPath" :avatar-type="row.echoMap.createdBy.avatarType" :name="row.echoMap.createdBy.name" />
          </template>
        </vxe-column>
        <vxe-column field="createTime" show-overflow="tooltip" title="创建时间" width="180" />
        <vxe-column title="操作" fixed="right" align="left" width="120">
          <template #default="{ row }">
            <template>
              <el-tooltip class="item" content="编辑" placement="top">
                <el-button
                  type="text"
                  :disabled="row.readonly || !$permission('base_template_edit')"
                  icon="iconfont el-icon-application-edit icon_click"
                  @click="editClickRow(row)"
                />
              </el-tooltip>
              <el-divider direction="vertical" />
              <el-tooltip class="item" content="复制" placement="top">
                <el-button
                  type="text"
                  :disabled="!$permission('base_engine_delete')"
                  icon="iconfont el-icon-application-copy icon_click"
                  @click="copyRow(row)"
                />
              </el-tooltip>
              <el-divider direction="vertical" />
              <el-dropdown trigger="click" :hide-on-click="false" @command="e => e && e()">
                <el-button type="text" icon="iconfont el-icon-application-more" />
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item icon="iconfont el-icon-application-step-setting" :disabled="row.readonly || !$permission('base_template_edit')" :command="() =>editMenuIdsFn(row)">
                    <span>分配面板</span>
                  </el-dropdown-item>
                  <el-dropdown-item icon="iconfont el-icon-application-setting" :disabled="row.readonly||!$permission('base_template_edit')" :command="() =>editRoleFn(row)">
                    <span>配置权限</span>
                  </el-dropdown-item>
                  <el-dropdown-item icon="iconfont el-icon-application-delete" :disabled="row.readonly||row.isRelevancy||!$permission('base_template_delete')" :command="() =>deleteRow(row)">
                    <span>删除</span>
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </template>
          </template>
        </vxe-column>
      </vxe-table>
    </div>
    <vone-pagination ref="pagination" :total="data.total" @update="getTableData" />
    <!-- 编辑弹窗 -->
    <EditTemplate
      v-if="editParam.visible"
      ref="editDialog"
      :now-row="editParam.row"
      :type="editParam.type"
      :title="editParam.title"
      :dialog-visible.sync="editParam.visible"
      @success="getTableData"
    />
    <!-- 配置面板 -->
    <EditMenuIds
      v-if="editMenuParam.visible"
      :id="editMenuParam.id"
      ref="editMenuDialog"
      :dialog-visible.sync="editMenuParam.visible"
      @success="getTableData"
    />
    <!-- 配置角色 -->
    <EditRole
      v-if="editRoleParam.visible"
      :id="editRoleParam.id"
      ref="editMenuDialog"
      :code="editRoleParam.code"
      :dialog-visible.sync="editRoleParam.visible"
      @success="getTableData"
    />
  </page-wrapper>
</template>

<script>
import {
  getProjectTemplate,
  deleteProjectTemplate,
  updateProjectTemplate
} from '@/api/vone/base/template'
import { gainTreeList } from '@/utils'
import { orgList } from '@/api/vone/base/org'
import { getUser } from '@/utils/auth'
import EditTemplate from './editTemplate.vue'
import EditMenuIds from './editMenuIds.vue'
import EditRole from './editRole.vue'

export default {
  components: {
    EditTemplate,
    EditMenuIds,
    EditRole
  },
  data() {
    return {
      extraData: {},
      defaultFileds: [
        {
          key: 'name',
          name: '模板名称',
          type: {
            code: 'INPUT'
          },
          placeholder: '请输入模板名称'
        },
        {
          key: 'name',
          name: '模板标识',
          type: {
            code: 'INPUT'
          },
          placeholder: '请输入模板标识'
        }
      ],
      formData: {
        name: '',
        code: ''
      },
      tableList: [],
      exportLoading: false,
      pageLoading: false,
      actions: [
      ],
      data: {},
      editParam: { visible: false },
      editMenuParam: { visible: false },
      editRoleParam: { visible: false },
      loginUser: getUser()
    }
  },
  computed: {
    userTypeName() {
      return function(row) {
        return row.echoMap?.type?.name || ''
      }
    },
    tableHeight() {
      const height = (this.extraData?.height || 0) + 'px'
      return `calc(${this.$reduceTableHeight} - ${height})`
    }
  },
  mounted() {
    this.getOrgList()
  },
  methods: {
    editRoleFn(row) {
      this.editRoleParam = {
        visible: true,
        id: row.id,
        code: row.code
      }
    },
    editMenuIdsFn(row) {
      this.editMenuParam = {
        visible: true,
        id: row.id
      }
    },
    // 数据源接口返回数据以后,把值塞到表单模板里
    setData(list, key, data) {
      list.forEach((e, index) => {
        if (e.key == key) {
          this.$set(e, 'optionList', data)
        }
      })
    },
    // 查询所有机构
    getOrgList() {
      orgList().then((res) => {
        const orgTree = gainTreeList(res.data)

        this.setData(this.defaultFileds, 'orgId', orgTree)
      })
    },
    async editStatus(row) {
      const param = {
        id: row.id,
        state: row.state,
        name: row.name,
        code: row.code
      }
      const { isSuccess, msg } = await updateProjectTemplate(param)
      if (!isSuccess) {
        this.$message.error(msg)
        return
      }
      this.$message.success('修改成功')
      this.getTableData()
    },

    // 获取表格数据
    getTableData() {
      let params = {}
      this.pageLoading = true
      const pageObj = this.$refs.pagination?.pageObj || {
        current: 1,
        size: 20
      }
      const sortObj = this.$refs.searchForm?.sortObj
      params = {
        ...pageObj,
        ...sortObj,
        extra: {
          ...this.extraData
        },
        model: { ...this.formData }
      }
      getProjectTemplate(params).then((res) => {
        if (!res.isSuccess) {
          this.$message.warning(res.msg)
          return
        }
        this.pageLoading = false
        this.data = res.data
        this.tableList = res.data.records
      })
    },
    async copyRow(row) {
      this.editParam = {
        visible: true,
        title: '复制模版',
        type: 'copy',
        row
      }
    },
    // 删除当前行
    deleteRow(row) {
      this.$confirm(`是否删除模板【${row.name}】?`, '删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        customClass: 'delConfirm',
        showClose: false,
        type: 'warning'
      })
        .then(() => {
          deleteProjectTemplate([row.id]).then((res) => {
            if (res.isSuccess) {
              this.$message.success('删除成功')
              this.getTableData()
            } else {
              this.$message.warning(res.msg)
            }
          })
        })
        .catch(() => {})
    },
    clickAddRow() {
      this.$router.push({
        name: 'base_template_add'
      })
    },
    // 编辑当前行
    editClickRow(row) {
      this.editParam = {
        visible: true,
        title: '编辑模版',
        type: 'edit',
        row
      }
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-col-12 {
  height: 80px;
}
</style>
