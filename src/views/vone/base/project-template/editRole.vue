<template>
  <div>
    <el-dialog class="dialogContainer" title="配置面板" :visible="dialogVisible" width="40%" :close-on-click-modal="false" :close-on-press-escape="false" @close="close">
      <!-- 表单部分 -->
      <div class="form-title">
        <div class="form-title-name"> 角色配置</div>
        <div class="form-operate">
          <el-button size="mini" type="text" icon="el-icon-plus" @click="addRole">新增</el-button>
        </div>
      </div>
      <el-alert
        style="margin-bottom: 16px;"
        title="系统默认创建“项目经理”角色，拥有所选功能面板的完整权限"
        type="info"
        show-icon
        :closable="false"
      />
      <div class="config-list">
        <div v-for="(item, index) in projectRoles" :key="item.code" class="config-list-item">
          <div>
            <span class="list-item-title">{{ item.name }}</span>
            <span class="list-item-des">{{ item.description }}</span>
          </div>

          <div v-if="item.code != code +'_PM'">
            <i class="iconfont el-icon-application-edit" style="color:var(--main-theme-color);margin-right: 12px;" @click="editRore(item, index)" />
            <i class="iconfont el-icon-application-delete" style="color:var(--main-theme-color)" @click="deleteRole(item,index)" />
          </div>
        </div>
      </div>
      <!-- 表单结束，按钮操作部分 -->
      <span slot="footer" class="dialog-footer">
        <el-button @click="$emit('update:dialogVisible', false)">取消</el-button>
        <el-button type="primary" :loading="loading" @click="editConfirm">确定</el-button>
      </span>
    </el-dialog>
    <el-dialog width="30%" height="100px" title="添加菜单" :visible.sync="menuIdsVisible">
      <el-select v-model="menuValue" value-key="id" multiple collapse-tags placeholder="请选择菜单">
        <el-option
          v-for="item in menuOptions"
          :key="item.code"
          :label="item.name"
          :value="item"
        />
      </el-select>
      <div slot="footer" class="dialog-footer">
        <el-button @click="menuIdsVisible = false">取 消</el-button>
        <el-button type="primary" @click="addMenuIdsFn">添加</el-button>
      </div>
    </el-dialog>
    <AddRole
      v-if="editParam.visible"
      ref="editDialog"
      :now-row="editParam.row"
      :type="editParam.type"
      :title="editParam.title"
      :menus="menuIds"
      :dialog-visible.sync="editParam.visible"
      @success="roleSuccess"
    />
  </div>
</template>
<script>
import { getOneMenu, getProjectRole, updateProjectRoles, getProjectMenus } from '@/api/vone/base/template'
import AddRole from './addRole.vue'

export default {
  components: {
    AddRole
  },
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    id: {
      type: String,
      default: ''
    },
    code: {
      type: String,
      default: ''
    },
    type: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      loading: false,
      orgData: [],
      projectRoles: [],
      projectTypes: [],
      menuIdsVisible: false,
      menuOptions: [],
      menuValue: [],
      editParam: { visible: false },
      menuIds: []
    }
  },

  mounted() {
    // 编辑时数据回显
    if (this.code) {
      this.getProjectRoleFn()
    }
    this.getOneMenuFn()
    this.getProjectMenusFn()
  },
  methods: {
    async getProjectMenusFn() {
      const res = await getProjectMenus(this.id)
      if (res.isSuccess) {
        this.menuIds = res.data.map(e => e.id)
      }
    },
    editRore(row, index) {
      this.editParam = {
        visible: true,
        title: '编辑角色',
        type: 'edit',
        index,
        row
      }
    },
    deleteRole(row, index) {
      this.projectRoles.splice(index, 1)[0]
    },
    addRole() {
      this.editParam = {
        visible: true,
        title: '新增角色',
        type: 'add'
      }
    },
    roleSuccess(row) {
      if (this.editParam.type == 'add') {
        this.projectRoles.push(row)
      } else {
        this.projectRoles[this.editParam.index] = row
      }
    },
    async getProjectRoleFn() {
      const param = {
        readonly: true,
        typeCode: this.code
      }

      const res = await getProjectRole(param)
      if (res.isSuccess) {
        this.projectRoles = res.data
      }
    },
    addMenuIdsFn() {
      this.menuValue.forEach(item => {
        if (!this.projectRoles.includes(item)) {
          this.projectRoles.push(item)
        }
      })
      this.menuIdsVisible = false
    },
    async getOneMenuFn() {
      const res = await getOneMenu()
      if (res.isSuccess) {
        this.menuOptions = res.data
      }
    },
    addMenuIds() {
      this.menuIdsVisible = true
    },
    // 保存
    async editConfirm() {
      if (this.projectRoles.length == 0) {
        this.$message.warning('请添加角色')
        return
      }
      const param = this.projectRoles
      this.loading = true
      updateProjectRoles(param).then(res => {
        this.loading = false
        if (res.isSuccess) {
          this.$message.success('操作成功')
          this.close()
          this.$emit('success')
        } else {
          this.$message.warning(res.msg)
        }
      }).catch(() => { this.loading = false })
    },
    close() {
      this.$emit('update:dialogVisible', false)
      this.$refs.editForm.resetFields()
    }
  }
}
</script>
<style lang="scss" scoped >
::v-deep .el-alert--info.is-light {
  background-color: #d8f1ff;
  color: var(--main-theme-color,#3e7bfa);
}
.form-title {
  line-height: 22px;
  height: 30px;
  border-bottom: 1px solid #EBEBEB;
  position: relative;
  padding-left: 12px;
  margin-bottom: 12px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  &-name {
    font-size: 16px;
    font-weight: 500;
  }
  &:before {
    display: block;
    content: "";
    background: var(--main-theme-color,#3e7bfa);
    position: absolute;
    width: 2px;
    height: 14px;
    top: 7px;
    left: 0;
    border-radius: 1px;
  }

}
.config-list {
    width: 100%;
    .config-list-item {
      width: 100%;
      padding: 0 12px;
      height: 38px;
      line-height: 38px;
      font-size: 14px;
      margin-bottom: 12px;
      border: 1px solid #D9D9D9;
      border-radius: 4px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      &:hover {
        color: var(--main-theme-color);
        cursor: grabbing;
        box-shadow: var(--main-bg-shadow);
      }
      .handle {
        margin-right: 12px;
        display: inline-block;
        color: #6B7385;
        cursor: grab;
        margin-right: 4px;
        &:hover {
          color: var(--main-theme-color);
        }
      }
      .list-item-title {
        display: inline-block;
        min-width: 100px;
        margin-right: 16px;
      }
      .list-item-des {
        font-size: 12px;
        color: #A5A5A5;
      }
    }
  }
</style>
