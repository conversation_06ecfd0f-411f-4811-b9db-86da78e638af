<template>
  <el-dialog :title="title" :visible="dialogVisible" width="45%" :close-on-click-modal="false" :close-on-press-escape="false" @close="close">
    <el-form ref="editForm" :model="editForm" :rules="rules" label-position="top">
      <el-row>
        <el-col :span="12">
          <el-form-item label="角色名称" prop="name">
            <el-input v-model="editForm.name" placeholder="请输入角色名称" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="角色标识" prop="code">
            <el-input v-model="editForm.code" placeholder="请输入角色标识" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item label="角色描述" prop="description">
        <el-input v-model="editForm.description" placeholder="请输入角色描述" type="textarea" />
      </el-form-item>
      <el-form-item label="角色配置" prop="description">
        <div class="treeActions">
          <el-input v-model="functionName" style="width: 100%;margin-bottom: 5px;" placeholder="输入关键字过滤" />
          <el-checkbox v-model="checkedMenu" class="all" :indeterminate="false" @change="val =>checkedAll(val,true)">全选/反选</el-checkbox>
        </div>
        <div style="height: 150px; overflow-y: auto;">
          <el-tree
            ref="tree"
            v-model="editForm.menuIds"
            v-loading="treeLoading"
            :check-strictly="true"
            :default-checked-keys="editForm.menuIds"
            :data="treeData"
            :props="defaultProps"
            default-expand-all
            show-checkbox
            :filter-node-method="filterNode"
            node-key="id"
            @check="checkMenu"
          >
            <template v-slot="{ node, data }">
              <span class="custom-node">
                <el-tag v-if="data.isButton" size="mini" class="menuTag">功能</el-tag>
                <el-tag v-else size="mini" class="funcTag">菜单</el-tag>
                <span>{{ node.label }}</span>
              </span>
            </template>
          </el-tree>
        </div>

      </el-form-item>
    </el-form>
    <!-- 表单结束，按钮操作部分 -->
    <span slot="footer" class="dialog-footer">
      <el-button @click="$emit('update:dialogVisible', false)">取消</el-button>
      <el-button type="primary" :loading="loading" @click="editConfirm">确定</el-button>
    </span>
  </el-dialog>
</template>
<script>
import { iconList } from './icon.json'

// import { apiAlmProjectRoleFunction } from '@/api/vone/project/setting'
import { getMenuRole, queryMenuByMenuIds } from '@/api/vone/base/template'

import { list2Tree } from '@/utils/list2Tree'

const triggerCheckId = (list, v, flag) => {
  let index = list.indexOf(v)
  // 全选且默认菜单不包括当前项
  if (flag && index === -1) {
    list.push(v)
  } else if (!flag) {
    // 取消全选且默认菜单包括当前项
    while (index !== -1) {
      list.splice(index, 1)
      index = list.indexOf(v)
    }
  }
}
export default {
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    nowRow: {
      type: Object,
      default: null
    },
    title: {
      type: String,
      default: ''
    },
    type: {
      type: String,
      default: ''
    },
    menus: {
      type: Object,
      default: () => []
    }
  },
  data() {
    return {
      iconList,
      loading: false,
      orgData: [],
      rules: {
        name: [
          {
            required: true,
            message: '请输入角色名称',
            trigger: 'change'
          }
        ],
        code: [
          {
            required: true,
            message: '请输入角色标识',
            trigger: 'change'
          }
        ]
      },
      editForm: {
        name: '',
        code: '',
        description: '',
        typeCode: '',
        menuIds: []
      },

      checkedMenu: false,
      checkData: [],
      treeData: [],
      treeLoading: false,
      allData: [],
      functionName: '',
      defaultProps: {
        children: 'children',
        label: 'name'
      }
    }
  },
  watch: {
    functionName(val) {
      // 过滤节点树数据
      clearTimeout(this.timer)
      this.timer = setTimeout(() => {
        this.$refs.tree.filter(val)
      }, 1000)
    }
  },
  mounted() {
    if (this.type == 'edit') {
      this.editForm = JSON.parse(JSON.stringify(this.nowRow))
    }
    this.getAllRoleFunction()
    if (this.type == 'edit' && this.nowRow?.id) {
      this.getMenuRoleFn()
    }
  },
  methods: {
    async getMenuRoleFn() {
      const res = await getMenuRole(this.nowRow.id)
      if (res.isSuccess) {
        this.editForm.menuIds = res.data.map(r => r.id)
      }
    },
    checkedAll(val) {
      if (val) {
        this.$refs.tree.setCheckedKeys(this.allData.map(r => r.id))
      } else {
        this.$refs.tree.setCheckedKeys([])
      }
    },
    async getAllRoleFunction() {
      console.log(this.nowRow)
      this.treeLoading = true
      const res = await queryMenuByMenuIds(this.menus)
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.allData = res.data
      this.treeData = list2Tree(res.data, { parentKey: 'parentId' })
      this.treeLoading = false
    },
    checkMenu(data, node) {
      // 当前节点是否被选中
      const selected = node.checkedKeys.indexOf(data.id) > -1 // -1未选中
      if (data.parentId) {
        this.$refs.tree.setChecked(data.parentId, true)
      }
      //  处理当前节点子节点选中状态
      this.uniteChildSame(data, selected)
    },
    uniteChildSame(data, isSelected) {
      // 设置当前节点选中
      this.$refs.tree.setChecked(data.id, isSelected)
      const list = []
      // 设置节点id选中状态
      triggerCheckId(list, data.id, isSelected)
      // 是否存在子数据
      if (data.children?.length > 0) {
        for (let i = 0; i < data.children.length; i++) {
          this.uniteChildSame(data.children[i], isSelected)
        }
      }
    },
    filterNode(value, data) {
      if (!value) return true
      return data.name?.indexOf(value) !== -1
    },

    // 保存
    async editConfirm() {
      try {
        await this.$refs.editForm.validate()
      } catch (e) {
        return
      }
      const checkData = this.$refs.tree.getCheckedNodes()
      const halfCheckedKeys = this.$refs.tree.getHalfCheckedKeys()
      const Data = [...halfCheckedKeys, ...checkData.filter(r => r.id != undefined).map(r => r.id)]
      this.editForm.menuIds = Data
      await this.$emit('success', JSON.parse(JSON.stringify(this.editForm)))
      await this.close()
      // if (this.type == 'add') {
      //   await this.$emit('success', JSON.parse(JSON.stringify(this.editForm)))
      //   await this.close()
      // } else {
      //   this.editForm.menuIds = Data
      //   await this.$emit('success', [JSON.parse(JSON.stringify(this.editForm)), this.index])
      //   await this.close()
      // }
    },
    close() {
      // this.$emit('success')
      this.$emit('update:dialogVisible', false)
      this.$refs.editForm.resetFields()
    }
  }
}
</script>
<style lang="scss" scoped >
.custom-node {
  .menuTag {
    margin-right: 8px;
    color: #bd7ffa;
    border-color: #bd7ffa;
    background-color: var(--main-bg-color,#fff);
  }
  .funcTag {
    margin-right: 8px;
    color: #ffbf47;
    border-color: #ffbf47;
    background-color: var(--main-bg-color,#fff);
  }
}
</style>
