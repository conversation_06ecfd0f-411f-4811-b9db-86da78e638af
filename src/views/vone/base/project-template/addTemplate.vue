<template>
  <vone-edit-wrapper show-content show-footer title="新增模版">
    <div slot="content">
      <div class="form-content">
        <div class="form-title">
          <div class="form-title-name">基础信息</div>
        </div>

        <el-form ref="ruleForm" :model="ruleForm" :rules="rules" label-width="100px" class="demo-ruleForm">
          <el-row>
            <el-col :span="12">
              <el-form-item label="模板名称" prop="name">
                <el-input v-model="ruleForm.name" placeholder="请输入模版名称" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="模版标识" prop="code">
                <el-input v-model="ruleForm.code" placeholder="请输入模版标识" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-form-item label="模版描述" prop="description">
            <el-input v-model="ruleForm.description" placeholder="请输入模版描述" type="textarea" />
          </el-form-item>
          <el-form-item label="模版图标" prop="icon" class="avatarPath">
            <el-popover
              ref="popoverRef"
              placement="bottom"
              trigger="click"
            >
              <el-row class="popover">
                <template>
                  <el-col v-for="item in iconList" :key="item" :span="6">
                    <div class="userhead item">
                      <a @click="changeIcon(item)">
                        <svg class="icon" aria-hidden="true">
                          <use :xlink:href="'#' + `${item}`" />
                        </svg>
                      </a>
                    </div>
                  </el-col>
                </template>
              </el-row>
              <span slot="reference">
                <div class="form-icon">
                  <svg v-if="ruleForm.icon" class="icon" aria-hidden="true">
                    <use :xlink:href="'#' + `${ruleForm.icon}`" />
                  </svg>
                  <i v-else class="el-icon-plus avatar-uploader-icon" />
                </div>
              </span>
            </el-popover>
          </el-form-item>
          <div class="form-title">
            <div class="form-title-name"> 面板配置</div>
            <div class="form-operate">
              <el-button size="mini" type="text" icon="el-icon-plus" @click="addMenuIds">新增</el-button>
            </div>
          </div>
          <el-alert
            style="margin-bottom: 16px;"
            title="请选择需要的功能面板，可拖动调整顺序"
            type="info"
            show-icon
            :closable="false"
          />
          <div class="config-list">
            <div v-for="(item, index) in ruleForm.menuIds" :key="item.id" class="config-list-item">
              <div>
                <i class="iconfont el-icon-application-drag handle" style="color:var(--main-theme-color)" />
                <span>{{ item.name }}</span>
              </div>
              <div @click="deleteMenu(item,index)">
                <i class="iconfont el-icon-application-delete" style="color:var(--main-theme-color)" />
              </div>
            </div>

          </div>
          <div class="form-title">
            <div class="form-title-name"> 角色配置</div>
            <div class="form-operate">
              <el-button size="mini" type="text" icon="el-icon-plus" @click="addRole">新增</el-button>
            </div>
          </div>
          <el-alert
            style="margin-bottom: 16px;"
            title="系统默认创建“项目经理”角色，拥有所选功能面板的完整权限"
            type="info"
            show-icon
            :closable="false"
          />
          <div class="config-list">
            <div v-for="(item, index) in ruleForm.projectRoles" :key="item.code" class="config-list-item">
              <div>
                <span class="list-item-title">{{ item.name }}</span>
                <span class="list-item-des">{{ item.description }}</span>
              </div>
              <div>
                <i class="iconfont el-icon-application-edit" style="color:var(--main-theme-color);margin-right: 12px;" @click="editRore(item, index)" />
                <i class="iconfont el-icon-application-delete" style="color:var(--main-theme-color)" @click="deleteRole(item,index)" />
              </div>
            </div>
          </div>
        </el-form></div>
    </div>
    <el-row slot="footer">
      <el-button @click="cancle">取消</el-button>
      <el-button type="primary" :loading="saveLoading" @click="saveForm">
        保存</el-button>
    </el-row>
    <el-dialog width="30%" height="100px" title="添加菜单" :visible.sync="menuIdsVisible">
      <el-select v-model="menuValue" value-key="id" multiple collapse-tags placeholder="请选择菜单">
        <el-option
          v-for="item in menuOptions"
          :key="item.code"
          :label="item.name"
          :value="item"
        />
      </el-select>
      <div slot="footer" class="dialog-footer">
        <el-button @click="menuIdsVisible = false">取 消</el-button>
        <el-button type="primary" @click="addMenuIdsFn">添加</el-button>
      </div>
    </el-dialog>
    <AddRole
      v-if="editParam.visible"
      ref="editDialog"
      :now-row="editParam.row"
      :type="editParam.type"
      :title="editParam.title"
      :menus="ruleForm.menuIds.map(e=>e.id)"
      :dialog-visible.sync="editParam.visible"
      @success="roleSuccess"
    />

  </vone-edit-wrapper>
</template>

<script>
import Sortable from 'sortablejs'
import { iconList } from './icon.json'
import { getOneMenu, addAllProjectType } from '@/api/vone/base/template'
import AddRole from './addRole.vue'

export default {
  components: {
    AddRole
  },
  props: {
    id: {
      type: String,
      default: null
    }
  },
  data() {
    return {
      iconList,
      loading: false,
      saveLoading: false,
      menuIdsVisible: false,
      roleVisible: false,
      menuValue: [],
      menuOptions: [],
      rules: {
        name: [
          {
            required: true,
            message: '请输入模版名称',
            trigger: 'change'
          }
        ],
        code: [
          {
            required: true,
            message: '请输入模版标识',
            trigger: 'change'
          }
        ]
      },
      ruleForm: {
        name: '',
        code: '',
        description: '',
        icon: '',
        menuIds: [],
        projectRoles: []
      },
      roleForm: {
        name: '',
        code: '',
        description: '',
        typeCode: '',
        menuIds: []
      },
      modes: [],
      orgData: [],
      userType: [],
      isIndeterminate: false,
      checkedMenu: false,
      checkData: [],
      treeData: [],
      treeLoading: false,
      allData: [],
      functionName: '',
      editParam: { visible: false }

    }
  },

  mounted() {
    if (this.id) {
      this.getUserDetail()
      this.getRole(this.orgId) // 角色
    }
    this.getOneMenuFn()
    this.$nextTick(() => {
      this.rowDrop()
    })
  },
  methods: {
    roleSuccess(row) {
      if (this.editParam.type == 'add') {
        this.ruleForm.projectRoles.push(row)
      } else {
        this.ruleForm.projectRoles[this.editParam.index] = row
      }
    },
    deleteMenu(row, index) {
      this.ruleForm.menuIds.splice(index, 1)[0]
    },
    deleteRole(row, index) {
      this.ruleForm.projectRoles.splice(index, 1)[0]
    },
    async getOneMenuFn() {
      const res = await getOneMenu()
      if (res.isSuccess) {
        this.menuOptions = res.data
      }
    },
    addMenuIds() {
      this.menuIdsVisible = true
    },
    addRole() {
      this.editParam = {
        visible: true,
        title: '新增角色',
        type: 'add'
      }
    },
    editRore(row, index) {
      this.editParam = {
        visible: true,
        title: '编辑角色',
        type: 'edit',
        index,
        row
      }
    },
    addMenuIdsFn() {
      this.menuValue.forEach(item => {
        if (!this.ruleForm.menuIds.includes(item)) {
          this.ruleForm.menuIds.push(item)
        }
      })
      this.menuIdsVisible = false
    },
    changeIcon(item) {
      this.$set(this.ruleForm, 'icon', item)
      this.$refs.popoverRef.doClose()
    },
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          alert('submit!')
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    resetForm(formName) {
      this.$refs[formName].resetFields()
    },
    rowDrop() {
      const listbox = document.querySelector('.config-list')
      Sortable.create(listbox, {
        group: 'list',
        handle: '.config-list-item',
        animation: 50,
        avoidImplicitDeselect: true,
        removeCloneOnHide: true,
        dragClass: '.draggingRow',
        scroll: true,
        onEnd: (evt) => {
          const { newIndex, oldIndex } = evt
          if (newIndex === oldIndex) return
          const currRow = this.ruleForm.menuIds.splice(oldIndex, 1)[0]
          this.ruleForm.menuIds.splice(newIndex, 0, currRow)
        }
      })
    },
    async saveForm() {
      const param = this.ruleForm
      param.menuIds = param.menuIds.map(r => r.id)
      const res = await addAllProjectType(param)
      if (res.isSuccess) {
        this.$message.success('新增成功')
        this.cancle()
      } else {
        this.$message.warning(res.msg)
      }
    },
    cancle() {
      this.$router.go(-1)
    }
  }
}
</script>

<style  lang="scss" scoped >

::v-deep .el-alert--info.is-light {
  background-color: #d8f1ff;
  color: var(--main-theme-color,#3e7bfa);
}
.form-title {
  line-height: 22px;
  height: 30px;
  border-bottom: 1px solid #EBEBEB;
  position: relative;
  padding-left: 12px;
  margin-bottom: 12px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 16px;
  &-name {
    font-size: 16px;
    font-weight: 500;
  }
  &:before {
    display: block;
    content: "";
    background: var(--main-theme-color,#3e7bfa);
    position: absolute;
    width: 2px;
    height: 14px;
    top: 7px;
    left: 0;
    border-radius: 1px;
  }

}
.avatarPath{
  display: flex;
  align-items: center;
   ::v-deep .el-form-item__content{
     margin-left: 0 !important;
     width: 68px;
   }
}
.popover {
  // width: 300px;
  .item {
    padding: 6px 0;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s;
    color: var(--main-theme-color);
    text-align: center;
    background-color: var(--main-bg-color,#fff);
    &:hover {
      background-color: var(--hover-bg-color,#f5f6fa);
    }
    .icon {
      font-size: 24px;
    }
  }
}
.form-icon {
  width: 68px;
  height: 68px;
  margin-left: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #D9D9D9;
  border-radius: 4px;
  font-size: 24px;
  color: #D9D9D9;
}
.form-content {
  width: 100%;
  padding: 0 16%;
  .config-list {
    width: 100%;
    .config-list-item {
      width: 100%;
      padding: 0 12px;
      height: 38px;
      line-height: 38px;
      font-size: 14px;
      margin-bottom: 12px;
      border: 1px solid #D9D9D9;
      border-radius: 4px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      &:hover {
        color: var(--main-theme-color);
        cursor: grabbing;
        box-shadow: var(--main-bg-shadow);
      }
      .handle {
        margin-right: 12px;
        display: inline-block;
        color: #6B7385;
        cursor: grab;
        margin-right: 4px;
        &:hover {
          color: var(--main-theme-color);
        }
      }
      .list-item-title {
        display: inline-block;
        min-width: 100px;
        margin-right: 16px;
      }
      .list-item-des {
        font-size: 12px;
        color: #A5A5A5;
      }
    }
  }
}
</style>

