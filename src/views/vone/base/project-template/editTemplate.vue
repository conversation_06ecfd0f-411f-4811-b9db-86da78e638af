<template>
  <el-dialog class="dialogContainer" :title="title" :visible="dialogVisible" width="40%" :close-on-click-modal="false" :close-on-press-escape="false" @close="close">
    <!-- 表单部分 -->
    <el-form ref="editForm" :model="editForm" :rules="rules" label-position="top">
      <el-form-item label="模板名称" :disables="nowRow.isRelevancy" prop="name">
        <el-input v-model="editForm.name" placeholder="请输入模版名称" />
      </el-form-item>
      <el-form-item label="模版标识" prop="code">
        <el-input v-model="editForm.code" :disabled="type=='edit'" placeholder="请输入模版标识" />
      </el-form-item>
      <el-form-item label="模版描述" prop="description">
        <el-input v-model="editForm.description" placeholder="请输入模版描述" type="textarea" />
      </el-form-item>
      <el-row>
        <el-col :span="12">
          <el-form-item label="模版图标" prop="icon" class="avatarPath">
            <el-popover
              ref="popoverRef"
              placement="bottom"
              trigger="click"
              width="68px"
            >
              <el-row class="popover">
                <template>
                  <el-col v-for="item in iconList" :key="item" :span="6">
                    <div class="userhead item">
                      <a @click="changeIcon(item)">
                        <svg class="icon" aria-hidden="true">
                          <use :xlink:href="'#' + `${item}`" />
                        </svg>
                      </a>
                    </div>
                  </el-col>
                </template>
              </el-row>
              <span slot="reference">
                <div class="form-icon">
                  <svg v-if="editForm.icon" class="icon" aria-hidden="true">
                    <use :xlink:href="'#' + `${editForm.icon}`" />
                  </svg>
                  <i v-else class="el-icon-plus avatar-uploader-icon" />
                </div>
              </span>
            </el-popover>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="颜色" prop="color">
            <el-color-picker v-model="editForm.color" />
          </el-form-item>
        </el-col>
      </el-row>

    </el-form>
    <!-- 表单结束，按钮操作部分 -->
    <span slot="footer" class="dialog-footer">
      <el-button @click="$emit('update:dialogVisible', false)">取消</el-button>
      <el-button type="primary" :loading="loading" @click="editConfirm">确定</el-button>
    </span>
  </el-dialog>
</template>
<script>
import { orgList } from '@/api/vone/base/org'
import { gainTreeList } from '@/utils'
import { updateProjectTemplate, copyProjectTemplate } from '@/api/vone/base/template'
import { String } from 'core-js'
import { iconList } from './icon.json'

export default {
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    nowRow: {
      type: Object,
      default: null
    },
    title: {
      type: String,
      default: ''
    },
    type: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      iconList,
      loading: false,
      orgData: [],
      rules: {
        name: [
          {
            required: true,
            message: '请输入模版名称',
            trigger: 'change'
          }
        ],
        code: [
          {
            required: true,
            message: '请输入模版标识',
            trigger: 'change'
          }
        ]
      },
      editForm: {
        id: this.nowRow.id,
        name: this.nowRow.name,
        code: this.nowRow.code,
        description: this.nowRow.description,
        icon: this.nowRow.icon,
        state: this.nowRow.state,
        color: this.nowRow.color
      },
      projectTypes: []
    }
  },

  mounted() {
    // 编辑时数据回显
    if (this.nowRow.id) {
      // this.editForm = this.nowRow
    }
  },
  methods: {
    changeIcon(item) {
      this.$set(this.editForm, 'icon', item)
      this.$refs.popoverRef.doClose()
    },

    // 查询所有机构
    async getOrgList() {
      await orgList().then((res) => {
        const orgTree = gainTreeList(res.data)
        this.orgData = orgTree
      })
    },
    // 保存
    async editConfirm() {
      try {
        await this.$refs.editForm.validate()
      } catch (e) {
        return
      }
      // const param = { id: this.nowRow.id, state: this.nowRow.state, ...this.editForm }
      const param = this.editForm
      this.loading = true
      if (this.type == 'edit') {
        updateProjectTemplate(param).then(res => {
          this.loading = false
          if (res.isSuccess) {
            this.$message.success('操作成功')
            this.close()
            this.$emit('success')
          } else {
            this.$message.warning(res.msg)
          }
        }).catch(() => { this.loading = false })
      } else {
        copyProjectTemplate(param).then(res => {
          this.loading = false
          if (res.isSuccess) {
            this.$message.success('复制成功')
            this.close()
            this.$emit('success')
          } else {
            this.$message.warning(res.msg)
          }
        }).catch(() => { this.loading = false })
      }
    },
    close() {
      // this.$emit('success')
      this.$emit('update:dialogVisible', false)
      this.$refs.editForm.resetFields()
    }
  }
}
</script>
<style lang="scss" scoped >
.avatarPath{
  display: flex;
  flex-direction: column;
   ::v-deep .el-form-item__content{
    width: 68px;
     margin-left: 0 !important;
   }
}
.popover {
  .item {
    padding: 6px 0;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s;
    color: var(--main-theme-color);
    text-align: center;
    background-color: var(--main-bg-color,#fff);
    &:hover {
      background-color: var(--hover-bg-color,#f5f6fa);
    }
    .icon {
      font-size: 24px;
    }
  }
}
.form-icon {
  width: 68px;
  height: 68px;
  margin-left: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #D9D9D9;
  border-radius: 4px;
  font-size: 24px;
  color: #D9D9D9;
}
::v-deep .el-color-picker__trigger {
  width: 68px;
  height: 68px;
}
</style>
