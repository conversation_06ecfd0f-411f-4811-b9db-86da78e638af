<template>
  <div class="sectionPageContent">
    <div class="leftSection">
      <div class="header">
        <span>字典管理</span>
      </div>
      <div class="cardContent">
        <div v-for="(item,index) in cardData" :key="index">
          <el-card shadow="hover" :class="['left-small-card', item.type == dictKey ?'is-active' : '']" style="margin-top: 16px" @click.native="toDict(item)">
            <div class="small-card-title">
              <div class="small-card-title-left">
                <div class="svg-icon">
                  <svg-icon icon-class="dict" />
                </div>
                <span>
                  <el-tooltip v-if="item.label && item.label.length > 7" :content="item.label">
                    <span>{{ item.label.slice(0, 7) + "..." }}</span>
                  </el-tooltip>
                  <span v-else style="display:contents">{{ item.label }}</span>
                  <span class="count" />{{ item.dictionaryNum }}</span>
              </div>
            </div>
            <div class="small-card-desc">
              <el-tooltip v-if="item.desc && item.desc.length > 13" :content="item.desc">
                <span>{{ item.desc.slice(0, 13) + "..." }}</span>
              </el-tooltip>
              <span v-else>{{ item.desc }}</span>
            </div>
          </el-card>
        </div>
      </div>
    </div>
    <div class="rightSection">
      <dictList v-if="dictParams" ref="dictList" v-bind="dictParams" :all-dict="allDict" :dict-key="dictKey" :dict-value="dictValue" />
    </div>
  </div>
</template>
<script>
const descMap = {
  'RELEASE_TYPE': '发布类型',
  'ENVIRONMENT_TYPE': '环境类型用于区分环境的阶段',
  'ENVIRONMENT': '环境标签包括开发环境、测试环境等',
  'ENGINE_CLASSIFY': '',
  'ENGINE_INSTANCE': '平台集成的引擎类型',
  'APP_COMPONENTS_TYPE': '平台集成的应用组件类型',
  'DB_COMPONENTS_TYPE': '平台集成的数据库组件类型',
  'BRANCHING_STRATEGY': '分支策略保证研发过程互不干扰',
  'BRANCH': '代码库分支',
  'SCRIPT_TYPE': '区分脚本类型，包括部署脚本、构建脚本等',
  'SCRIPT_FUNCTION': '区分脚本实现的功能',
  'PACK': '用于制品库判断制品晋级情况',
  'PACK_CLASSIFY': '用于制品库判断制品晋级情况'
}

import dictList from './dictList.vue'
// import { apiBaseDictEnumList } from '@/api/vone/ouath/ouath'
import { apiBaseAllDict } from '@/api/vone/base/dict'
export default {
  components: {
    dictList
  },
  data() {
    return {
      pageLoading: false,
      cardData: [],
      actions: [],
      dictParams: {},
      flagN: undefined,
      descMap,
      allDict: [],
      dictKey: undefined,
      dictValue: undefined
    }
  },
  mounted() {
    this.getDIcPage()
  },
  methods: {

    async getDIcPage() {
      this.pageLoading = true
      const res = await apiBaseAllDict({ platform: '0' })

      if (!res.isSuccess) {
        return
      }

      if (!res.data.length) return

      this.allDict = res.data

      res.data.forEach(element => {
        element.desc = this.descMap[element.type] ? this.descMap[element.type] : ''
      })

      this.cardData = res.data.filter(r => r.type != 'ENGINE_CLASSIFY')

      // if (!this.flagN) {
      //   this.toDict(this.cardData[0])
      // }
      this.flagN = res.data[0]?.type
      this.dictKey = res.data[0]?.type
      this.dictValue = res.data[0]?.label
      this.pageLoading = false
    },
    toDict(row) {
      this.dictValue = row.label
      this.dictKey = row.type
      this.dictParams = { dictKey: row.type }
      // this.$refs.dictList.getDicList(row.type)
      this.flagN = row.type
    }

  }
}
</script>
<style lang="scss" scoped>
@import "~@/styles/variables.scss";
.leftSection {
	width: 240px;
	.header {
		padding: 0px 16px;
		height: 48px;
		line-height: 48px;
		border-bottom: 1px solid var(--solid-border-color);
		display:flex;
		span {
			color: var(--font-main-color);
			font-size: 16px;
			font-weight: 500;
			flex: 1;
		}
		.search {
			display: inline-block;
			.iconfont {
				cursor: pointer;
				color: var(--font-second-color);
				margin-left: 8px;
			}
			.iconfont:hover {
				color: var(--main-theme-color);
			}
			.iconfont.active {
				color: var(--main-theme-color);
			}
		}
	}
	.cardContent {
		padding: 0px 16px;
		height: calc(100vh - #{$nav-top-height} - #{$main-margin} - #{$main-margin} - 57px);
		overflow-y: overlay;
    span {
      display: flex;
      align-items: center;
      }
    .count {
      height: 12px;
      width: 1px;
      background: var(--solid-border-color);
      margin: 0px 4px;
      display: inline-block;
    }
  }
}
</style>

