<template>
  <page-wrapper>
    <vone-search-wrapper>
      <template slot="search">
        <vone-search-dynamic
          v-if="defaultFileds.length"
          ref="searchForm"
          table-search-key="defect-table"
          :model.sync="formData"
          :table-ref="$refs['defect-table']"
          show-grouping
          :grouping-options="tableOptions.groupingOptions"
          :hide-columns="tableOptions.hideColumns"
          :default-fileds.sync="defaultFileds"
          show-basic
          :show-column-sort="true"
          :extra.sync="extraData"
          @getTableData="getInitTableData"
          @onTypeChange="onTypeChange"
        />
      </template>
      <template slot="actions">
        <el-row type="flex" justify="space-between">
          <simpleAddIssue
            v-if="createSimple"
            :type-code="'BUG'"
            :biz-type="'BUG_FILE_UPLOAD'"
            :defect-type="typeCodeList"
            :env-list="envList"
            @success="getInitTableData"
            @cancel="createSimple = false"
          />

          <div>
            <el-button-group class="ml-16">
              <el-tooltip content="快速新增" placement="top">
                <el-button
                  :disabled="!$permission('project_defect_add')"
                  class="subBtton"
                  :icon="`iconfont  ${
                    createSimple ? 'el-icon-direction-double-left' : 'el-icon-direction-double-down'
                  }`"
                  type="primary"
                  @click.stop="createSimple = !createSimple"
                />
              </el-tooltip>
              <el-button
                icon="iconfont el-icon-tips-plus-circle"
                type="primary"
                :disabled="!$permission('project_defect_add')"
                @click.stop="newDefect"
              >新增</el-button>
            </el-button-group>
            <el-dropdown trigger="click" @command="e => e && e()">
              <el-button class="btnMore"><i class="iconfont el-icon-application-more" /></el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item
                  v-for="(item, index) in actions"
                  :key="index"
                  :icon="item.icon"
                  :command="item.fn"
                  :disabled="item.disabled"
                >
                  {{ item.name }}
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </div>
        </el-row>
      </template>
      <template slot="fliter">
        <vone-search-filter
          v-if="defaultFileds.length"
          :extra.sync="extraData"
          :model.sync="formData"
          :default-fileds.sync="defaultFileds"
          @getTableData="getInitTableData"
        />
      </template>
    </vone-search-wrapper>
    <main :style="{ height: tableHeight }">
      <vxe-table
        ref="defect-table"
        class="vone-vxe-table"
        border
        resizable
        height="auto"
        show-overflow="tooltip"
        :loading="tableLoading"
        :empty-render="{ name: 'empty' }"
        :data="tableData.records"
        :column-config="{ minWidth: '120px' }"
        :cell-class-name="cellShow"
        :tree-config="{
          transform: false,
          rowField: 'id',
          parentField: 'parentId',
          hasChild: 'hasChildren',
          lazy: true,
          loadMethod: ({ row }) => getClidData(row, 1),
        }"
        :checkbox-config="{ reserve: true }"
        row-id="id"
        @checkbox-all="selectAllEvent"
        @checkbox-change="selectChangeEvent"
        @resizable-change="({ column }) => resizableChangeEvent(column, 'defect-table')"
      >
        <vxe-column type="checkbox" width="36" fixed="left" align="center" />
        <vxe-column
          title="标题"
          field="name"
          min-width="480"
          fixed="left"
          class-name="name_col  custom-title-style"
          show-overflow="ellipsis"
          tree-node
        >
          <template #default="{ row }">
            <span v-if="row.delay && !row.groupType" style="position: absolute; left: 0">
              <el-tooltip :open-delay="500" content="当前工作项已延期" placement="top" :visible-arrow="false">
                <i class="el-icon-warning-outline color-danger ml-2" />
              </el-tooltip>
            </span>
            <el-tooltip
              v-if="!row.groupType"
              v-showWorkItemTooltips
              :content="row.code + ' ' + row.name"
              placement="top-start"
              :visible-arrow="false"
            >
              <span class="custom-title-main" @click="showInfo(row)">
                <i
                  v-if="row.typeCode && row.echoMap && row.echoMap.typeCode"
                  :class="`iconfont ${row.echoMap.typeCode.icon} custom-title-style-icon `"
                  :style="{ color: `${row.echoMap.typeCode ? row.echoMap.typeCode.color : '#ccc'}` }"
                />
                <span class="custom-title-style-text">{{ row.code + ' ' + row.name }}</span>
              </span>
            </el-tooltip>
            <span v-else>
              {{ row.name }}
              <span class="count-num">
                {{ row.count }}
              </span>
            </span>
            <span
              v-if="!row.groupType"
              class="custom-title-style-copy"
              :style="{
                position: 'absolute',
                top: ' -4px',
                right: '-40px',
                display: copyRow && copyRow.id == row.id ? 'block' : '',
              }"
            >
              <el-dropdown
                trigger="click"
                :hide-on-click="true"
                @visible-change="e => visibleChange(e, row)"
                @command="customCopy"
              >
                <el-button type="text" icon="iconfont el-icon-application-more" />
                <el-dropdown-menu slot="dropdown" class="custom-title-copy-dropdown">
                  <el-dropdown-item icon="iconfont el-icon-edit-character-b" command="title">
                    <span>复制编号</span>
                  </el-dropdown-item>
                  <el-dropdown-item icon="iconfont el-icon-application-copy-content" command="code">
                    <span>复制标题</span>
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </span>
          </template>
        </vxe-column>
        <vxe-column title="状态" field="stateCode" width="120">
          <template #default="{ row }">
            <defectStatus
              v-if="row && !row.groupType"
              :key="Date.now()"
              :workitem="row"
              :no-permission="!$permission('project_defect_flow')"
              @changeFlow="getInitTableData"
            />
          </template>
        </vxe-column>
        <vxe-column title="关联需求" field="requirementId" width="120">
          <template #default="{ row }">
            <span v-if="row.requirementId && row.echoMap && row.echoMap.requirementId">
              {{ `${row.echoMap.requirementId.code}   ${row.echoMap.requirementId.name}` }}
            </span>
            <span v-else>{{ row.requirementId }}</span>
          </template>
        </vxe-column>
        <vxe-column title="处理人" field="handleBy" width="120">
          <template #default="{ row }">
            <div v-if="!row.groupType">
              <span>
                <vone-remote-user
                  v-model="row.handleBy"
                  :project-id="projectId"
                  class="remoteuser"
                  :default-data="[row.echoMap.handleBy]"
                  :disabled="row.stateCode == 'CLOSE' || !$permission('project_defect_edit')"
                  @change="workitemChange(row, $event, 'handleBy')"
                />
              </span>
            </div>
          </template>
        </vxe-column>
        <vxe-column field="leadingBy" title="负责人" width="120">
          <template #default="{ row }">
            <div v-if="!row.groupType">
              <span>
                <vone-remote-user
                  v-model="row.leadingBy"
                  :project-id="projectId"
                  class="remoteuser"
                  :default-data="[row.echoMap.leadingBy]"
                  :disabled="row.stateCode == 'CLOSE' || !$permission('project_defect_edit')"
                  @change="workitemChange(row, $event, 'leadingBy')"
                />
              </span>
            </div>
          </template>
        </vxe-column>
        <vxe-column field="putBy" title="提出人" width="120">
          <template #default="{ row }">
            <div v-if="!row.groupType">
              <span>
                <vone-remote-user
                  v-model="row.putBy"
                  :project-id="projectId"
                  class="remoteuser"
                  :default-data="[row.echoMap.putBy]"
                  :disabled="row.stateCode == 'CLOSE' || !$permission('project_defect_edit')"
                  @change="workitemChange(row, $event, 'putBy')"
                />
              </span>
            </div>
          </template>
        </vxe-column>
        <vxe-column title="创建时间" field="createTime" width="120">
          <template #default="{ row }">
            <span v-if="row.createTime">
              {{ dayjs(row.createTime).format('YYYY-MM-DD') }}
            </span>
            <span v-else>{{ row.createTime }}</span>
          </template>
        </vxe-column>

        <vxe-column title="计划开始时间" field="planStime" width="135">
          <template #default="{ row }">
            <span v-if="row.planStime">
              {{ dayjs(row.planStime).format('YYYY-MM-DD HH:mm') }}
            </span>
            <span v-else>{{ row.planStime }}</span>
          </template>
        </vxe-column>
        <vxe-column title="计划完成时间" field="planEtime" width="135">
          <template #default="{ row }">
            <span v-if="row.planEtime">
              {{ dayjs(row.planEtime).format('YYYY-MM-DD HH:mm') }}
            </span>
            <span v-else>{{ row.planEtime }}</span>
          </template>
        </vxe-column>
        <vxe-column title="进度" field="rateProgress" width="80">
          <template #default="{ row }">
            <el-tooltip placement="top" :content="` ${row.rateProgress ? row.rateProgress : 0}%`">
              <el-progress
                :percentage="row.rateProgress ? parseInt(row.rateProgress) : 0"
                color="var(--main-theme-color,#3e7bfa)"
                :show-text="false"
              />
            </el-tooltip>
          </template>
        </vxe-column>
        <vxe-column title="优先级" field="priorityCode" width="100">
          <template #default="{ row }">
            <vone-icon-select
              v-if="!row.groupType"
              v-model="row.priorityCode"
              :data="prioritList"
              filterable
              clearable
              style="width: 100%"
              class="userList"
              :no-permission="!$permission('project_issue_priority_update')"
              @change="workitemChange(row, $event, 'priorityCode')"
            >
              <el-option v-for="item in prioritList" :key="item.key" :label="item.name" :value="item.code">
                <i
                  :class="`iconfont ${item.icon}`"
                  :style="{ color: item.color, fontSize: '16px', paddingRight: '6px' }"
                />
                {{ item.name }}
              </el-option>
            </vone-icon-select>
          </template>
        </vxe-column>

        <vxe-column field="planId" :title="$route.params.projectTypeCode == 'AGILE' ? '归属计划' : '归属里程碑'">
          <template #header>
            <span>{{ $route.params.projectTypeCode == 'AGILE' ? '归属计划' : '归属里程碑' }}</span>
          </template>
          <template #default="{ row }">
            <span v-if="row.planId && row.echoMap && row.echoMap.planId">
              {{ row.echoMap.planId.name }}
            </span>
            <span v-else>{{ row.planId }}</span>
          </template>
        </vxe-column>
        <vxe-column title="操作" fixed="right" align="left" width="120">
          <template #default="{ row }">
            <el-tooltip class="item" content="编辑" placement="top">
              <el-button
                type="text"
                :disabled="row.stateCode == 'CLOSE' || !$permission('project_defect_edit')"
                icon="iconfont el-icon-application-edit"
                @click="editBug(row)"
              />
            </el-tooltip>
            <el-divider direction="vertical" />
            <el-tooltip class="item" content="删除" placement="top">
              <el-button
                type="text"
                :disabled="!$permission('project_defect_del')"
                icon="iconfont el-icon-application-delete"
                @click="deleteBug(row)"
              />
            </el-tooltip>
            <el-divider direction="vertical" />
            <el-dropdown trigger="click" :hide-on-click="false" @command="e => e && e(row)">
              <el-button type="text" icon="iconfont el-icon-application-more" class="operation-dropdown" />
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item icon="iconfont el-icon-edit-character-b" :command="() => titleCopy(row, 'code')">
                  <span>复制编号</span>
                </el-dropdown-item>
                <el-dropdown-item
                  icon="iconfont el-icon-application-copy-content"
                  :command="() => titleCopy(row, 'title')"
                >
                  <span>复制标题</span>
                </el-dropdown-item>
                <el-dropdown-item
                  :disabled="!$permission('project_defect_add')"
                  icon="iconfont el-icon-icon-fuzhi"
                  :command="() => workItemCopy(row)"
                >
                  <span>复制工作项</span>
                </el-dropdown-item>
                <el-dropdown-item
                  :disabled="row.stateCode == 'CLOSE' || !$permission('project_defect_edit')"
                  icon="iconfont el-icon-application-type"
                  :command="() => typeCodeChangeFn(row)"
                >
                  <span>变更类型</span>
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </template>
        </vxe-column>
      </vxe-table>
    </main>
    <vone-pagination ref="pagination" :total="tableData.total" @update="getInitTableData" />

    <!-- 新增 -->
    <vone-custom-add
      v-if="defectParamAdd.visible"
      :key="defectParamAdd.key"
      :type-code="'BUG'"
      :visible.sync="defectParamAdd.visible"
      v-bind="defectParamAdd"
      :is-tooltip="true"
      :title="defectParamAdd.title"
      @success="getInitTableData({})"
    />

    <!-- 编辑完整缺陷 -->
    <vone-custom-edit
      v-if="defectParam.visible"
      :key="defectParam.key"
      :visible.sync="defectParam.visible"
      v-bind="defectParam"
      :type-code="'BUG'"
      :left-tabs="leftTabs"
      :right-tabs="rightTabs"
      @success="getInitTableData({})"
    />

    <!-- 缺陷详情 -->
    <vone-custom-info
      v-if="defectInfoParam.visible"
      :key="defectInfoParam.key"
      :visible.sync="defectInfoParam.visible"
      v-bind="defectInfoParam"
      :type-code="'BUG'"
      :left-tabs="leftTabs"
      :right-tabs="rightTabs"
      @success="getInitTableData({}, 'edit')"
    />

    <!-- 导入 -->
    <vone-import-file
      v-if="importParam.visible"
      v-bind="importParam"
      :visible.sync="importParam.visible"
      :type="'BUG'"
      @success="getInitTableData({}, 'edit')"
    />

    <!-- 批量编辑 -->
    <editAll
      v-if="editAllParam.visible"
      v-bind="editAllParam"
      :visible.sync="editAllParam.visible"
      :type-code="'BUG'"
      @success="getInitTableData({}, 'edit')"
    />

    <type-code-change
      v-if="typeCodeChangeParam.visible"
      v-bind="typeCodeChangeParam"
      :visible.sync="typeCodeChangeParam.visible"
      @success="getInitTableData({}, 'edit')"
    />
  </page-wrapper>
</template>

<script>
import { apiAlmGetDefectData, apiAlmBugDel, apiAlmBugInfo, getGroup } from '@/api/vone/project/defect'
import { apiAlmPriorityNoPage, apiAlmGetTypeNoPage } from '@/api/vone/alm/index'

import { apiBaseFileLoad } from '@/api/vone/base/file'
import { catchErr, download } from '@/utils'

import defectStatus from '@/views/vone/project/common/change-status/index.vue'
import simpleAddIssue from './function/add-defect.vue'

import editAll from '../common/edit-all'

import { apiBaseDictNoPage } from '@/api/vone/package'
import typeCodeChange from '@/components/CustomEdit/components/type-code-change'
import { editById, getWorkItemState } from '@/api/vone/project/index'
import { queryFieldList } from '@/api/common'
import { apiAlmSourceNoPage, requirementListByCondition } from '@/api/vone/project/issue'
import { apiAlmProjectPlanNoPage } from '@/api/vone/project/iteration'

import { productListByCondition } from '@/api/vone/project/index'

import dayjs from 'dayjs'
import setDataMixin from '@/mixin/set-data'

export default {
  components: {
    defectStatus,
    simpleAddIssue,
    editAll,
    typeCodeChange
  },
  mixins: [setDataMixin],
  data() {
    return {
      extraData: {},
      defaultFileds: [],
      tableList: [], // 用于编辑时切换上一个下一个
      formData: {
        projectId: [this.$route.params.id]
      },
      createSimple: false,
      selecteTableData: [],
      tableData: {
        records: []
      },
      tableLoading: false,
      tableOptions: {
        isOperation: true,
        isSelection: true,
        hideColumns: [
          'files',
          'code',
          'description',
          'delay',
          'estimatePoint',
          'planStime',
          'projectId',
          'ideaId',
          'sourceCode',
          'typeCode',
          'putBy',
          'leadingBy',
          'testPlanId',
          'rateProgress'
        ], // 默认隐藏列
        groupingOptions: [
          { name: '按处理人', value: 'handle_by', key: 'handleBy' },
          { name: '按负责人', value: 'leading_by', key: 'leadingBy' },
          { name: '按产品', value: 'product_id', key: 'productId' },
          { name: '按迭代', value: 'plan_id', key: 'planId' },
          { name: '按计划完成时间', value: 'date(plan_etime)', key: 'planEtime' }
        ]
      },
      defectParamAdd: { visible: false }, // 新增
      defectParam: { visible: false }, // 编辑
      actions: [
        {
          name: '批量删除',
          // icon: 'iconfont el-icon-application-delete',
          fn: this.deleteAll,
          disabled: !this.$permission('project_defect_del')
        },
        {
          name: '批量编辑',
          // icon: 'iconfont el-icon-application-edit',
          fn: this.editAll,
          disabled: !this.$permission('project_defect_edit')
        },
        {
          name: '导入',
          icon: 'iconfont el-icon-edit-import',
          fn: this.imPort,
          disabled: !this.$permission('project_bug_import')
        },
        {
          name: '导出',
          icon: 'iconfont el-icon-edit-export',
          fn: this.exportFlie,
          disabled: !this.$permission('project_bug_export')
        }
      ],
      envList: [],
      typeCodeList: [],
      prioritList: [],

      importParam: { visible: false }, // 用户导入
      editAllParam: { visible: false }, // 批量编辑
      defectInfoParam: { visible: false }, // 详情
      rightTabs: [
        {
          active: true,
          label: '评论',
          name: 'comment'
        },
        {
          label: '活动',
          name: 'active'
        },
        {
          label: '变更记录',
          name: 'activityRecord'
        },
        {
          label: '工时',
          name: 'workTime'
        }
      ],
      leftTabs: [
        {
          label: '关联缺陷',
          name: 'DefectToDefect'
        }
      ],
      exportParam: { visible: false }, // 导出
      columnKeysCheck: {},
      groupItem: {
        value: 'none'
      },
      pageData: {
        // 分页配置
        current: 1,
        size: 10,
        total: 0
      },
      rowData: {},
      formContainer: {},
      timeStamp: '',

      typeCodeChangeParam: { visible: false },
      copyRow: null,
      projectId: this.$route.params.id
    }
  },
  computed: {
    tableHeight() {
      const height = (this.extraData?.height || 0) + 'px'
      return `calc(${this.$reduceTableHeight} - ${height})`
    }
  },
  watch: {
    $route: {
      handler(val) {
        if (val.query && val.query.queryId) {
          this.showInfo({
            id: val.query.queryId,
            typeCode: val.query.rowTypeCode,
            stateCode: val.query.stateCode
          })
        }
      },
      // 一进页面就执行
      immediate: true,
      // 深度观察监听
      deep: true
    }
  },
  // 路由离开生命周期函数
  beforeRouteLeave(to, from, next) {
    // 即将跳转的路由地址
    if (to.path != 'project_defect_view') {
      this.$store.state.project.itemName = undefined
      next()
    }
  },

  mounted() {
    // this.getInitTableData()
    this.getEnvList()

    this.$nextTick(() => {
      this.columnKeysCheck = this.$refs['defect-table'].columnKeysCheck
    })
  },
  created() {
    this.getQueryFieldList()
    const params = this.$route.params
    if (params) {
      if (params.type == 'comment') {
        this.showInfo({ id: params.businessId })
      }
    }
  },
  methods: {
    resizableChangeEvent(column, refName) {
      if (column.field == 'name') {
        this.$refs[refName].refreshColumn()
      }
    },
    customCopy(command) {
      const _this = this
      const message = command == 'title' ? this.copyRow.code : this.copyRow.name
      this.$copyText(message).then(
        function(e) {
          _this.$message.success(' 已复制到剪贴板！')
        },
        function(e) {
          _this.$message.warning(' 该浏览器不支持自动复制')
        }
      )
      this.copyRow = null
    },
    visibleChange(e, row) {
      if (e) {
        this.copyRow = row
      } else {
        this.copyRow = null
      }
    },
    typeCodeChangeFn(e) {
      this.typeCodeChangeParam = {
        visible: true,
        dataId: e.id,
        typeClassfiy: 'BUG'
      }
    },
    workItemCopy(e) {
      this.createSimple = false
      this.defectParamAdd = {
        visible: true,
        key: Date.now(),
        infoDisabled: false,
        rowTypeCode: e.typeCode,
        title: '复制缺陷',
        id: e.id
      }
    },
    async workitemChange(row, e, t) {
      const params = {
        id: row.id
      }
      params[t] = e
      const res = await editById('bug', params)
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.$set(row, t, e)
      this.$message.success('修改成功')
    },
    handleSelectionChange(e, row) {
      this.selecteTableData = Array.from(new Set([...this.selecteTableData, ...e]))
    },
    handlePageChange(e, row) {
      row.load = false
      this.getClidData(row, e)
    },
    getClidData(row, page) {
      if (row.load) return
      row.loading = true
      const tableAttr = this.$refs?.pagination?.pageObj || { current: 1, size: 20 }
      this.$set(this.formData, 'projectId', [this.$route.params.id])
      const groupMap = this.tableOptions.groupingOptions.reduce((r, v) => (r[v.value] = v.key) && r, {})

      if (groupMap[row.groupType] == 'planEtime') {
        this.$set(this.formData, 'planEtime', { start: row.id + ' 00:00:00', end: row.id + ' 23:59:59' })
      } else {
        this.$set(this.formData, groupMap[row.groupType], [row.id])
      }

      const params = {
        ...tableAttr,
        current: page || 1,
        order: 'descending',
        size: 99999,
        sort: 'createTime',
        extra: {},
        model: { ...this.formData }
      }

      // if (this.groupItem?.type == 'time') {
      //   params.model.planEtime = {
      //     start: row.id + ' 00:00:00',
      //     end: row.id + ' 23:59:59'
      //   }
      // } else {
      //   params.model[this.groupItem.form] = [row.id]
      // }

      return apiAlmGetDefectData(params)
        .then(res => {
          if (!res.isSuccess) {
            this.$message.warning(res.msg)
            return
          }
          res.data.records.forEach(element => {
            element.tag =
              element.tagId && element.tagId.length && element.echoMap && element.echoMap.tagId
                ? element.echoMap.tagId.map(r => r.name)
                : []
          })
          row.load = true
          row.loading = false
          this.$nextTick(() => {
            row.childrenData = res.data.records || []
            row.count = res.data.total
            row.pageData = res.data
            this.timeStamp = new Date().valueOf()
          })
          return res.data.records
        })
        .catch(() => {
          this.$set(row, 'loading', false)
        })
    },
    handleRow(index, row, item, table) {
      if (typeof item.handler === 'function') {
        item.handler(row, index)
      }
      this.rowData = table
    },
    isDisabled(scope, { disabled }) {
      if (typeof disabled === 'function') {
        disabled = disabled(scope.$index, scope.row)
      }
      return !!disabled
    },
    cellShow(row) {
      if (
        (row.row.groupType && row.columnIndex === 0) ||
        (!row.row.groupType && row.columnIndex === 1) ||
        (row.row.groupType && row.column.label === '操作')
      ) {
        return 'hide-node'
      }
    },
    childCellShow(row) {
      if (!row.row.groupType && row.columnIndex === 1) {
        return 'hide-node'
      }
    },
    expandChange(row) {
      this.getClidData(row, 1)
    },
    getStatus(index, row) {
      return row?.stateCode == 'CLOSE' || !this.$permission('project_defect_edit')
    },
    // 查询环境
    async getEnvList() {
      const res = await apiBaseDictNoPage({
        type: 'ENVIRONMENT',
        state: true
      })
      if (!res.isSuccess) {
        return
      }
      this.envList = res.data
    },

    selectAllEvent({ checked }) {
      this.selecteTableData = this.$refs['defect-table'].getCheckboxRecords()
    },
    selectChangeEvent({ checked }) {
      this.selecteTableData = this.$refs['defect-table'].getCheckboxRecords()
    },
    async getInitTableData(e, type) {
      if (this.extraData && this.extraData.groupView && this.extraData.groupView != 'none') {
        this.groupingCommand(this.extraData.groupView)
        return
      }
      this.$set(this.formData, 'projectId', [this.$route.params.id])
      const tableAttr = this.$refs?.pagination?.pageObj || { current: 1, size: 20 }
      const sortObj = this.$refs.searchForm?.sortObj
      const params = {
        ...tableAttr,
        ...sortObj,
        extra: {
          ...this.extraData
        },
        model: { ...this.formData }
      }

      if (e && Object.keys(e).length !== 0) {
        this.formContainer = e
      }
      if (!this.formContainer.grouping || this.formContainer.grouping == 'none') {
        this.tableLoading = true
        // this.$set(this.formData, 'projectId', this.$route.params.id)

        const res = await apiAlmGetDefectData(params)
        this.tableLoading = false
        if (!res.isSuccess) {
          this.$message.warning(res.msg)
          return
        }
        res.data.records.forEach(element => {
          element.tag =
            element.tagId && element.tagId.length && element.echoMap && element.echoMap.tagId
              ? element.echoMap.tagId.map(r => r.name)
              : []
        })
        this.tableData = res.data
        this.tableList = res.data.records // 用于编辑时切换上一个下一个
      } else {
        if (type == 'edit') {
          this.rowData.load = false
          this.getClidData(this.rowData, this.rowData?.pageData?.current * 1)
        }
      }
    },
    // 更新表格缺陷状态
    async editRowStatus(row, index, table) {
      // 查询当前表格项数据
      const [res, err] = await catchErr(apiAlmBugInfo(row.id))
      if (err) return
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      if (res.data.tagId && res.data.tagId.length) {
        this.$set(res.data, 'tag', res.data.echoMap.tagId ? res.data.echoMap.tagId.map(r => r.name) : [])
      }
      this.$set(row, 'stateCode', res.data.stateCode)
      this.$set(row, 'echoMap', res.data.echoMap)
      // if (table) {
      //   table.childrenData.splice(index, 1, res.data)
      // } else {
      //   this.tableData.records.splice(index, 1, res.data)
      // }
    },
    // 复制标题到剪贴板
    titleCopy(row, type) {
      const _this = this
      const message = type == 'code' ? row.code : row.name
      this.$copyText(message).then(
        function(e) {
          _this.$message.success(' 已复制到剪贴板！')
        },
        function(e) {
          _this.$message.warning(' 该浏览器不支持自动复制')
        }
      )
    },
    newDefect() {
      this.createSimple = false
      this.defectParamAdd = {
        visible: true,
        title: '新增缺陷',
        key: Date.now(),
        infoDisabled: false
      }
    },
    editBug(row) {
      this.rowData = row
      this.defectParam = {
        visible: true,
        title: '编辑缺陷',
        key: Date.now(),
        id: row.id,
        infoDisabled: false,
        tableList: this.tableList,
        rowTypeCode: row.typeCode,
        stateCode: row.stateCode
      }
    },
    showInfo(row) {
      this.rowData = row
      this.defectInfoParam = {
        visible: true,
        title: '缺陷详情',
        key: Date.now(),
        id: row.id,
        infoDisabled: true,
        tableList: this.tableList,
        rowTypeCode: row.typeCode,
        stateCode: row.stateCode
      }
    },

    async deleteBug(row) {
      await this.$confirm(`确定删除【${row.name}】吗?`, '删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        customClass: 'delConfirm',
        showClose: false,
        type: 'warning'
      })

      const { isSuccess, msg } = await apiAlmBugDel([row.id])
      if (!isSuccess) {
        this.loading = false
        this.$message.error(msg)
        return
      }
      this.$message.success('删除成功')
      this.getInitTableData()
    },
    // 批量删除
    async deleteAll() {
      if (!this.selecteTableData.length) {
        this.$message.warning('请选择要删除的数据')
        return
      }

      try {
        await this.$confirm(`是否删除当前数据?`, '删除', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          customClass: 'delConfirm',
          showClose: false,
          type: 'warning'
        })
        this.tableLoading = true
        const selectId = this.selecteTableData.map(r => r.id)
        const res = await apiAlmBugDel(selectId)
        this.tableLoading = false
        if (!res.isSuccess) {
          this.$message.error(res.msg)
          return
        }
        this.$message.success('删除成功')
        this.getInitTableData()
      } catch (e) {
        this.tableLoading = false
      }
    },
    // 导入
    imPort() {
      this.importParam = {
        visible: true,
        title: '项目-缺陷',
        url: `/api/alm/alm/bug/excel/downloadImportTemplate/${this.$route.params.id}`,
        importUrl: `/api/alm/alm/bug/excel/import?projectId=${this.$route.params.id}`
      }
    },

    // 导出
    async exportFlie() {
      try {
        this.tableLoading = true
        download(
          `缺陷信息.xls`,
          await apiBaseFileLoad('/api/alm/alm/bug/excel/export', {
            projectId: [this.$route.params.id]
          })
        )

        this.tableLoading = false
      } catch (e) {
        this.tableLoading = false
        return
      }
    },
    // 批量编辑
    editAll() {
      if (!this.selecteTableData.length) {
        this.$message.warning('请至少选择一条数据')
        return
      }
      this.editAllParam = { visible: true, tableSelect: this.selecteTableData }
    },
    async getQueryFieldList(typeCodes) {
      const fixedField = ['name', 'handleBy', 'stateCode', 'tagId', 'createTime', 'typeCode']
      const form = {
        projectId: this.$route.params.id,
        typeClassify: 'BUG',
        typeCodes: typeCodes || []
      }
      const res = await queryFieldList(form)
      if (!res.isSuccess) {
        return
      }
      const vId = ['productId', 'planId']
      const filter = res.data.filter(r => r.isSearch && r.key != 'projectId')
      filter.forEach(element => {
        element.isBasicFilter = !fixedField.includes(element.key)
        element.multiple = element.type.code != 'ICON'

        element.valueType = vId.includes(element.key) ? 'id' : null
      })
      this.defaultFileds = filter
      this.getOptions()
    },
    getOptions() {
      this.getPrioritList()
      this.getIssueType()
      this.getAllStatus()
      this.getDaley()
      this.getsourceCode()
      this.productList()
      this.getplanId()
    },
    getDaley() {
      const delay = [
        { name: '是', code: true },
        { name: '否', code: false }
      ]
      this.setData(this.defaultFileds, 'delay', delay)
    },
    // 查询来源
    async getsourceCode() {
      const res = await apiAlmSourceNoPage({
        typeClassify: 'BUG'
      })
      if (!res.isSuccess) {
        return
      }
      this.setData(this.defaultFileds, 'sourceCode', res.data)
    },
    // 归属产品
    async productList() {
      const res = await productListByCondition()
      if (!res.isSuccess) {
        return
      }
      this.setData(this.defaultFileds, 'productId', res.data)
    },
    // 查询全部工作流状态
    async getAllStatus() {
      const res = await getWorkItemState({
        projectId: this.$route.params.id,
        typeClassify: 'BUG'
      })
      if (!res.isSuccess) {
        return
      }
      this.setData(this.defaultFileds, 'stateCode', res.data)
    },
    // 查优先级
    async getPrioritList() {
      const res = await apiAlmPriorityNoPage()
      if (!res.isSuccess) {
        return
      }
      this.prioritList = res.data
      this.setData(this.defaultFileds, 'priorityCode', res.data)
    },
    // 查询需求类型
    async getIssueType() {
      const res = await apiAlmGetTypeNoPage(this.$route.params.id, 'BUG')
      if (!res.isSuccess) {
        return
      }
      this.typeCodeList = res.data
      this.setData(this.defaultFileds, 'typeCode', res.data)
    },
    // 迭代计划
    async getplanId() {
      // if (this.maps['planId'].length > 0) return
      const res = await apiAlmProjectPlanNoPage({
        projectId: this.$route.params.id || '0'
      })
      if (!res.isSuccess) {
        return
      }
      this.setData(this.defaultFileds, 'planId', res.data)
    },
    // 查项目下需求
    async getRequirementList() {
      // if (this.maps['requirementId']?.length > 0) return
      const res = await requirementListByCondition({
        projectId: this.$route.params.id || '0'
      })
      if (!res.isSuccess) {
        return
      }
      this.setData(this.defaultFileds, 'requirementId', res.data)
    },
    onTypeChange(item) {
      this.getQueryFieldList(item)
    },
    // 分组切换
    async groupingCommand(val) {
      this.$set(this.formData, 'projectId', [this.$route.params.id])
      const params = {
        extra: {
          ...this.extraData
        },
        model: { ...this.formData }
      }
      const res = await getGroup(params)
      this.tableLoading = false
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      res.data.records.forEach(e => {
        if (this.groupItem?.type === 'time') {
          e.bizId = e.bizId ? dayjs(e.bizId).format('YYYY-MM-DD') : '未规划时间'
        }
        e.name = e.data?.name || e.bizId
        e.id = e.bizId
        e.hasChildren = e.count > 0
      })
      this.tableData = res.data
    }
  }
}
</script>

<style lang="scss" scoped>
.remoteuser {
  ::v-deep .el-select .el-input .el-input__inner {
    border: none;
  }
  ::v-deep .el-input--small .el-input__inner {
    border: none;
  }
  ::v-deep .el-input__suffix {
    display: none;
  }
}
::v-deep .el-collapse-item__wrap {
  border-bottom: none;
}
::v-deep .el-tree-node__content {
  height: 40px;
}

.table_title {
  display: inline-block;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.count-num {
  display: inline-block;
  height: 18px;
  line-height: 18px;
  font-size: 12px;
  color: #838a99;
  padding: 0px 6px;
  background: #f2f3f5;
  border-radius: 9px;
  margin-left: 8px;
}
.child-table {
  font-size: 14px;
}
</style>
<style>
.userList .el-input__inner {
  border: 0;
}
.userList .el-input__icon {
  display: none;
}
</style>
