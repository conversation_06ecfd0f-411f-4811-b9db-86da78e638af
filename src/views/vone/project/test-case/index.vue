<template>
  <page-wrapper>
    <vone-search-wrapper>
      <template slot="actions">
        <el-button type="primary" icon="iconfont el-icon-tips-plus-circle" @click="addTestScheme">
          创建测试方案
        </el-button>
      </template>
    </vone-search-wrapper>
    <main style="height:calc(100vh - 185rem);">
      <vxe-table
        ref="test-scheme-table"
        class="vone-vxe-table"
        border
        height="auto"
        show-overflow="tooltip"
        :loading="pageLoading"
        :empty-render="{ name: 'empty' }"
        :data="tableData.records"
        :column-config="{ minWidth:'120px' }"
        :checkbox-config="{ reserve: true }"
        :row-config="{keyField: 'id'}"
      >
        <vxe-column title="方案名称" field="name">
          <template #default="{ row }">
            <span>{{ row.name }}</span>
          </template>
        </vxe-column>
        <vxe-column width="200" title="所属需求" field="testreqId">
          <template #default="{ row }">
            <template v-if="row.testreqId && row.echoMap.testreqId">
              <span>{{ row.echoMap.testreqId.name }} </span>
            </template>
          </template>
        </vxe-column>
        <vxe-column title="负责人" field="createdBy" width="160">
          <template #default="{ row }">
            <template v-if="row.leadingBy && row.echoMap.leadingBy">
              <vone-user-avatar :avatar-path="row.echoMap.leadingBy.avatarPath" :name="row.echoMap.leadingBy.name" />
            </template>
            <span v-else> -- </span>
          </template>
        </vxe-column>
        <vxe-column title="状态" field="stateCode" width="120">
          <template #default="{ row }">
            <el-dropdown trigger="click" placement="bottom" @command="(cd) => chapterFlowStatus(cd, row)">
              <span class="el-dropdown-link">
                <el-tag
                  effect="dark"
                  :type="row.stateCode === 'DONE' ? 'success' : ''"
                  @click="getFlowData(row)"
                >
                  {{ stateTag(row) }}
                </el-tag>
              </span>
              <el-dropdown-menu slot="dropdown" placement="bottom">
                <el-dropdown-item
                  v-for="item in flowStatus"
                  :key="item.stateCode"
                  :command="item.stateCode"
                >
                  {{ item.name }}
                </el-dropdown-item>
                <el-dropdown-item v-if="!flowStatus.length" command="NO">
                  暂无可流转状态
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </template>
        </vxe-column>
        <vxe-column title="描述" field="description">
          <template #default="{ row }">
            <span>{{ row.description || "" }}</span>
          </template>
        </vxe-column>
        <vxe-column title="操作" fixed="right" align="left" width="120">
          <template #default="{ row }">
            <el-tooltip class="item" content="编辑" placement="top">
              <el-button type="text" icon="iconfont el-icon-application-edit" @click="editScheme(row)" />
            </el-tooltip>
            <el-divider direction="vertical" />
            <el-tooltip class="item" content="添加为模版" placement="top">
              <el-button type="text" icon="iconfont el-icon-application-copy-content" @click="addTemplate(row)" />
            </el-tooltip>
            <el-divider direction="vertical" />
            <el-tooltip class="item" content="删除" placement="top">
              <el-button type="text" icon="iconfont el-icon-application-delete" @click="delRow(row)" />
            </el-tooltip>
          </template>
        </vxe-column>
      </vxe-table>
    </main>
    <vone-pagination ref="pagination" :total="tableData.total" @update="getSchemeList" />
    <add-template-modal
      v-if="templateVisible"
      :visible.sync="templateVisible"
      :scheme-id="schemeId"
    />
  </page-wrapper>
</template>

<script>
import { getTestSchemeList, delTestScheme, getFlowStatus, flowSchemeStatus } from '@/api/vone/project/scheme'
import AddTemplateModal from './components/add-template-modal.vue'
export default {
  components: {
    AddTemplateModal
  },
  data() {
    return {
      formData: {
        // projectId: this.$route.params.id
      },
      pageLoading: false,
      tableData: { records: [], total: 0 },
      flowStatus: [],
      templateVisible: false,
      schemeId: ''
    }
  },
  computed: {
    loginUser() {
      return this.$store.state.user.user.id
    },
    routePs() {
      const { id, projectKey, projectTypeCode } = this.$route.params
      return { id, projectKey, projectTypeCode }
    },
    stateTag() {
      return function(row) {
        return row.echoMap?.stateCode?.name || ''
      }
    }
  },
  mounted() {
    this.getSchemeList()
  },
  methods: {
    async addTestScheme() {
      const { id, projectKey, projectTypeCode } = this.$route.params
      this.$router.push(`/project/testCase/add/${projectKey}/${projectTypeCode}/${id}?step=1`)
    },
    // 查询报告列表
    async getSchemeList() {
      try {
        this.pageLoading = true
        const tableAttr = this.$refs?.pagination?.pageObj || { current: 1, size: 20 }
        const params = {
          ...tableAttr,
          extra: {},
          model: { ...this.formData }
        }
        const res = await getTestSchemeList(params)
        this.pageLoading = false
        if (!res.isSuccess) {
          this.$message.warning(res.msg)
          return
        }
        this.tableData = res.data
      } catch (e) {
        this.pageLoading = false
      }
    },
    preview(row) {

    },
    // 删除报告
    async delRow(row) {
      try {
        await this.$confirm(`确定删除测试方案【${row.name}】？`, '删除', {
          type: 'warning',
          customClass: 'delConfirm'
        })
        const res = await delTestScheme([row.id])
        if (res.isSuccess) {
          this.$message.success('删除成功')
          this.getSchemeList()
        } else {
          this.$message.error(res.msg)
        }
      } catch (e) {
        if (e === 'cancel') return
      }
    },
    editScheme(row) {
      this.$router.push(`/project/testCase/edit/${this.routePs.projectKey}/${this.routePs.projectTypeCode}/${this.routePs.id}/${row.id}`)
    },
    async chapterFlowStatus(cd, row) {
      if (cd && cd !== 'NO') {
        const { isSuccess, msg } = await flowSchemeStatus(row.id, row.stateCode, cd)
        if (!isSuccess) return this.$message(msg)
        this.getSchemeList()
      }
    },
    async getFlowData(row) {
      try {
        const { data, isSuccess, msg } = await getFlowStatus(row.id)
        if (!isSuccess) return this.$message.warning(msg)
        this.flowStatus = data || []
      } catch {
        return
      }
    },
    // 添加为模板
    addTemplate(row) {
      this.schemeId = row.id
      this.templateVisible = true
    }
  }
}
</script>
<style lang='scss' scoped>
.operation-icon-main {
  display: flex;
  align-items: center;
  .el-button {
    padding: 0px;
    height: unset;
    line-height: unset;
    min-width: unset;
    font-size: 16px;
  }
}

</style>

