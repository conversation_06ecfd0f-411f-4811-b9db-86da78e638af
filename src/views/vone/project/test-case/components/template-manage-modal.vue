<template>
  <div>
    <el-dialog
      class="dialogContainer"
      title="选择模板"
      width="456px"
      :visible="visible"
      :close-on-click-modal="false"
      :before-close="close"
    >
      <div class="template-wrapper">
        <el-select v-model="templateId" clearable filterable placeholder="请选择模板">
          <el-option v-for="item in templateData" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
        <el-tooltip v-if="templateId" effect="dark" content="预览" placement="top">
          <i class="iconfont el-icon-application-yulan" @click="preview" />
        </el-tooltip>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="close">取消</el-button>
        <el-button type="primary" :loading="loading" @click="save">使用</el-button>
      </span>
    </el-dialog>
    <el-dialog
      v-if="tempVisible"
      class="template-dialog"
      :title="templateName"
      :visible="tempVisible"
      :close-on-click-modal="false"
      :before-close="tempClose"
    >
      <tiptap-editor
        v-if="content"
        height="calc(90vh - 135px)"
        :content="content"
        :show-toolbar="false"
        :extensions="extensions"
        @created="editorCreated"
      />
      <vxe-loading v-model="tempLoading" text="正在加载..." />
      <span slot="footer" class="dialog-footer">
        <el-button @click="tempClose">取消</el-button>
        <el-button type="primary" :loading="loading" @click="save">使用</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getAllTemplate, getAllDocument, useTmplate } from '@/api/vone/knowledge'
import { CustomTableExtension, TradeTableExtension } from 'tiptap-vue-package'
import CustomTableView from '../tiptap/custom-table-view.vue'
import TradeTableView from '../tiptap/trade-table-view.vue'
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    spaceId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      loading: false,
      templateId: '',
      templateData: [],
      templateName: '',
      tempVisible: false,
      content: null,
      tempLoading: false,
      editor: null,
      extensions: [
        CustomTableExtension.configure({ nodeViewComponents: CustomTableView }),
        TradeTableExtension.configure({ nodeViewComponents: TradeTableView })
      ]
    }
  },
  async mounted() {
    this.getTemplateData()
  },
  methods: {
    async getTemplateData() {
      try {
        const { data, isSuccess, msg } = await getAllTemplate({})
        if (!isSuccess) return this.$message.warning(msg)
        this.templateData = data
      } catch { return }
    },
    async save() {
      const { projectKey, projectTypeCode, id } = this.$route.params
      try {
        this.loading = true
        const params = {
          bizId: this.$route.query?.schemeId,
          bizType: 'TEST_SCHEME',
          id: this.templateId,
          name: this.templateName,
          spaceId: this.spaceId
        }
        const { isSuccess, msg } = await useTmplate(params)
        if (!isSuccess) return this.$message.warning(msg)
        this.tempClose()
        this.close()
        this.$router.push(`/project/testCase/edit/${projectKey}/${projectTypeCode}/${id}/${this.$route.query?.schemeId}`)
      } catch (e) {
        this.loading = false
        return
      } finally {
        this.loading = false
      }
    },
    close() {
      this.$emit('update:visible', false)
    },
    async preview() {
      this.templateName = this.templateData.find(ele => ele.id === this.templateId)?.name || ''
      this.tempVisible = true
      try {
        this.tempLoading = true
        const { data, isSuccess, msg } = await getAllDocument({ bizId: this.templateId })
        if (!isSuccess) return this.$message.warning(msg)
        let allContent = []
        data.forEach(ele => {
          const content = JSON.parse(ele.content) || null
          if (content) {
            allContent = [...allContent, ...content]
          }
        })
        this.content = {
          type: 'doc',
          content: allContent
        }
      } catch {
        this.tempLoading = false
        return
      } finally {
        this.tempLoading = false
      }
    },
    tempClose() {
      this.tempVisible = false
    },
    editorCreated(editor) {
      this.editor = editor
      editor.setEditable(false)
    }
  }
}
</script>

<style lang="scss" scoped>
.template-wrapper {
	display: flex;
	align-items: center;
	flex-direction: row;
	gap: 12px;
	.iconfont {
		font-size: 24px;
		cursor: pointer;
		&:hover {
			color: #3E7BFA;
		}
	}
}
.template-dialog {
  ::v-deep.el-dialog {
    width: 80%;
  }
  ::v-deep.el-dialog__body {
    min-height: 200px;
    position: relative;
    color: #000000;
  }
}

</style>
