<template>
  <el-dialog
    class="dialogContainer"
    title="添加章节"
    :visible="visible"
    width="456px"
    :close-on-click-modal="false"
    :before-close="close"
  >
    <el-form ref="form" :model="form" :rules="rules">
      <el-form-item label="章节名称" prop="name">
        <el-input v-model="form.name" placeholder="请输入章节名称" />
      </el-form-item>
      <el-form-item label="负责人" prop="leadingBy">
        <vone-remote-user v-model="form.leadingBy" :project-id="$route.params.id" />
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="close">取消</el-button>
      <el-button type="primary" :loading="loading" @click="save">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { addChapter } from '@/api/vone/knowledge'
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: ''
    },
    chapterNode: {
      type: Object,
      default: () => {}
    },
    spaceId: {
      type: String,
      default: ''
    },
    isChild: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      form: {
        name: '',
        leadingBy: this.$store.state.user.user.id,
        parentId: null,
        level: 1,
        sort: this.chapterNode ? this.chapterNode.sort + 1 : 1,
        bizId: this.chapterNode ? this.chapterNode.bizId : this.$route.params.schemeId,
        bizType: 'TEST_SCHEME',
        type: 'CHAPTER',
        spaceId: this.spaceId,
        templated: false
      },
      rules: {
        name: [{ required: true, message: '请输入章节名称', trigger: 'change' }],
        leadingBy: [{ required: true, message: '请选择负责人', trigger: 'change' }]
      },
      loading: false
    }
  },
  mounted() {
  },
  methods: {
    async save() {
      try {
        await this.$refs.form.validate()
      } catch (e) {
        return
      }
      try {
        this.loading = true
        if (this.isChild) {
          this.form.parentId = this.chapterNode.id
          this.form.level = this.chapterNode.level + 1
          this.form.sort = this.chapterNode?.children?.length ? this.chapterNode?.children?.length + 1 : 1
        }
        const content = [
          {
            type: 'heading',
            attrs: { level: this.form.level, lock: true },
            content: [
              { type: 'text', text: this.form.name }
            ]
          }
        ]
        this.form.content = JSON.stringify(content)
        const { isSuccess, msg } = await addChapter(this.form)
        if (!isSuccess) return this.$message.warning(msg)
        this.$emit('refresh')
        this.close()
      } catch (e) {
        this.loading = false
        return
      } finally {
        this.loading = false
      }
    },
    close() {
      this.$emit('update:visible', false)
      this.$emit('update:isChild', false)
      this.$refs['form'].resetFields()
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
