<template>
  <el-dialog
    class="dialogContainer"
    title="设置编写负责人"
    :visible="visible"
    width="456px"
    :close-on-click-modal="false"
    :before-close="close"
  >
    <el-form ref="userform" :model="form" :rules="rules">
      <el-form-item label="编写负责人" prop="leadingBy">
        <vone-remote-user v-model="form.leadingBy" :project-id="$route.params.id" />
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="close">取消</el-button>
      <el-button type="primary" :loading="loading" @click="save">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { addChapterLeader } from '@/api/vone/knowledge'
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: ''
    },
    chapterNode: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      form: {
        leadingBy: '',
        id: this.chapterNode.id
      },
      rules: {
        leadingBy: [{ required: true, message: '请选择编写负责人', trigger: 'change' }]
      },
      loading: false
    }
  },
  mounted() {
  },
  methods: {
    async save() {
      try {
        await this.$refs.userform.validate()
      } catch (e) {
        return
      }
      try {
        this.loading = true
        const { isSuccess, msg } = await addChapterLeader([this.form])
        if (!isSuccess) return this.$message.warning(msg)
        this.$emit('refresh')
        this.close()
      } catch (e) {
        this.loading = false
        return
      } finally {
        this.loading = false
      }
    },
    close() {
      this.$emit('update:visible', false)
      this.$refs['userform'].resetFields()
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
