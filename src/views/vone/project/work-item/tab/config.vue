<template>
  <overview v-if="type == 'overview'" :work-id="workId" />
  <testCase v-else-if="type == 'case'" />
  <el-card v-else-if="type == 'defect'" class="workitem-card">
    <defect
      :type-code="customInfo.typeCode"
      :issue-id="customInfo.id"
      :issue-info="customInfo"
      :product-id="customInfo.productId"
      tab-name="缺陷"
      :show-popup-for-split="false"
      :is-show-create-btn="true"
      :jump-to="false"
      @initList="initList"
    />
  </el-card>
  <analysis v-else-if="type === 'analysis'" />
  <execution v-else-if="type === 'execution'" />
  <report v-else-if="type === 'report'" />
  <el-card v-else-if="type == 'task'" class="workitem-card">
    <task
      :type-code="customInfo.typeCode"
      :issue-id="customInfo.id"
      :issue-info="customInfo"
      :product-id="customInfo.productId"
      tab-name="任务"
      :show-popup-for-split="false"
      :is-show-create-btn="true"
      :jump-to="false"
      @initList="initList"
    />
  </el-card>
  <testPlan v-else-if="type === 'testPlan'" />
  <planTask v-else-if="type === 'planTask'" />
  <el-card v-else-if="type === 'require'" class="workitem-card">
    <issueToIssue
      :type-code="customInfo.typeCode"
      :issue-id="customInfo.id"
      :issue-info="customInfo"
      :product-id="customInfo.productId"
      tab-name="需求"
      :show-popup-for-split="false"
      :is-show-create-btn="true"
      @initList="initList"
    />
  </el-card>
  <el-card v-else-if="type === 'requireTestCase'" class="workitem-card">
    <requireTestCase
      :type-code="customInfo.typeCode"
      :issue-id="customInfo.id"
      :issue-info="customInfo"
      :product-id="customInfo.productId"
      tab-name="测试用例"
      :show-popup-for-split="false"
      :is-show-create-btn="true"
      @initList="initList"
    />
  </el-card>

  <config v-else :type="type" />
</template>

<script>
// import overview from './overview'
import overview from '@/views/vone/project/components/work-item/custom-info'

import testCase from './testtree/index'
import defect from '@/components/CustomEdit/commonTab/bug-tab'
import analysis from './analysis/index'
import execution from './execution'
import report from './report'
import task from '@/components/CustomEdit/commonTab/taskTab'
import testPlan from './test-case/index'
import planTask from './plan/index'
import issueToIssue from '@/components/CustomEdit/commonTab/issue-to-issue'
import requireTestCase from '@/components/CustomEdit/commonTab/test-case'
import storage from 'store'

export default {
  components: {
    overview,
    testCase,
    defect,
    analysis,
    execution,
    report,
    task,
    testPlan,
    planTask,
    issueToIssue,
    requireTestCase
  },
  props: {
    type: {
      default: undefined,
      type: String
    },
    name: {
      default: undefined,
      type: String
    },
    workId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      customInfos: {}
    }
  },
  created() {
    this.customInfo = storage.get('customInfo')
      ? JSON.parse(storage.get('customInfo'))
      : {}
  },
  methods: {
    initList() {
      this.$emit('initList')
    }
  }
}
</script>

<style>
.workitem-card {
  height: calc(100vh - 20px - 48px - 58px);
}
</style>
