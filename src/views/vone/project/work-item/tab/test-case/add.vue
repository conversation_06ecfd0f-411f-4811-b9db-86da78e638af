<template>
  <page-wrapper>
    <div class="scheme-wrapper">
      <div class="scheme-step-wrappe">
        <div class="scheme-step">
          <div class="scheme-step-item">
            <div :class="['scheme-step-item-num', currentStep === 1 ? 'num-is-active' : '']">1</div>
            <div :class="['scheme-step-item-title', currentStep === 1 ? 'title-is-active' : '']">填写基本信息</div>
          </div>
          <div class="scheme-step-item">
            <div :class="['scheme-step-item-num', currentStep === 2 ? 'num-is-active' : '']">2</div>
            <div :class="['scheme-step-item-title', currentStep === 2 ? 'title-is-active' : '']">编写方案章节</div>
          </div>
          <div class="scheme-step-item">
            <div :class="['scheme-step-item-num', currentStep === 3 ? 'num-is-active' : '']">3</div>
            <div :class="['scheme-step-item-title', currentStep === 3 ? 'title-is-active' : '']">完成方案创建</div>
          </div>
        </div>
        <div class="scheme-form">
          <div v-if="currentStep === 1" class="scheme-form-basic">
            <el-form ref="form" :model="form" :rules="rules" label-width="80px">
              <el-form-item label="方案名称" prop="name">
                <el-input v-model="form.name" placeholder="请输入方案名称" />
              </el-form-item>
              <el-form-item label="所属需求" prop="testreqId">
                <el-select v-model="form.testreqId" clearable filterable placeholder="请选择测试需求">
                  <el-option v-for="item in issueData" :key="item.id" :label="item.name" :value="item.id" />
                </el-select>
              </el-form-item>
              <el-form-item label="负责人">
                <vone-remote-user v-model="form.leadingBy" style="width: 300px" :project-id="$route.params.id" />
              </el-form-item>
              <el-form-item label="描述">
                <el-input v-model="form.description" placeholder="请输入描述" />
              </el-form-item>
            </el-form>
          </div>
          <add-chapter v-if="currentStep === 2" ref="chapter" />
          <div v-if="currentStep === 3" class="scheme-form-done">
            <el-result icon="success">
              <template slot="extra">
                <el-button type="primary" @click="goEdit">立即编写测试方案</el-button>
              </template>
            </el-result>
          </div>
        </div>
      </div>
      <div v-if="currentStep !== 3" class="scheme-footer">
        <el-button @click="$router.go(-1)">返回</el-button>
        <el-button v-if="currentStep !== 1" @click="prev">上一步</el-button>
        <el-button type="primary" :loading="nextLoading" @click="next">下一步</el-button>
      </div>
    </div>

  </page-wrapper>
</template>

<script>
import AddChapter from './components/add-chapter.vue'
import { addTestScheme, editTestScheme, getTestSchemeDetail, getTestReq } from '@/api/vone/project/scheme'
export default {
  components: {
    AddChapter
  },
  data() {
    return {
      currentStep: 1,
      form: {
        name: '',
        requirementId: '',
        testreqId: '',
        leadingBy: this.$store.state.user.user.id,
        description: '',
        projectId: this.$route.params.id,
        stateCode: ''
      },
      rules: {
        name: [
          {
            required: true,
            message: '请输入方案名称',
            trigger: 'change'
          }
        ],
        testreqId: [
          {
            required: true,
            message: '请选择测试需求',
            trigger: 'change'
          }
        ]
      },
      nextLoading: false,
      issueData: []
    }
  },
  computed: {
    routePs() {
      const { id, projectKey, projectTypeCode } = this.$route.params
      const { step, schemeId } = this.$route.query
      return { id, projectKey, projectTypeCode, step, schemeId }
    }
  },
  watch: {
    '$route.query.step': {
      handler(value) {
        if (value) {
          this.currentStep = Number(this.$route.query?.step)
        }
      },
      immediate: true
    }
  },
  mounted() {
    this.getAllTestReq()
    if (this.routePs.schemeId) {
      getTestSchemeDetail(this.routePs.schemeId).then(res => {
        if (res.isSuccess) {
          this.form = res.data
        }
      })
    }
  },
  methods: {
    async prev() {
      const prevStep = Number(this.routePs.step) - 1
      this.$router.replace(`/project/testCase/add/${this.routePs.projectKey}/${this.routePs.projectTypeCode}/${this.routePs.id}?step=${prevStep}&schemeId=${this.routePs.schemeId}`)
    },
    async next() {
      const step = Number(this.routePs.step)
      if (step === 1) {
        try {
          await this.$refs.form.validate()
        } catch (e) {
          return
        }
        try {
          this.nextLoading = true
          let res = null
          if (this.form.id) {
            res = await editTestScheme(this.form)
          } else {
            res = await addTestScheme(this.form)
          }
          if (res.isSuccess) {
            this.$router.replace(`/project/testCase/add/${this.routePs.projectKey}/${this.routePs.projectTypeCode}/${this.routePs.id}?step=2&schemeId=${res.data.id}`)
          } else {
            this.$message.warning(res.msg)
          }
          this.nextLoading = false
        } catch (e) {
          this.nextLoading = false
          return
        }
      } else {
        this.$refs.chapter.updataTable()
      }
    },
    // 查询测试需求
    async getAllTestReq() {
      try {
        const res = await getTestReq({})
        this.issueData = res.data || []
      } catch (e) {
        return
      }
    },
    goEdit() {
      this.$router.push(`/project/testCase/edit/${this.routePs.projectKey}/${this.routePs.projectTypeCode}/${this.routePs.id}/${this.routePs.schemeId}`)
    }
  }
}
</script>
<style lang='scss' scoped>
.scheme-wrapper {
  width: 860px;
  margin: 0 auto;
  display: flex;
  flex-flow: column;
  justify-content: space-between;
  min-height: calc(100vh - 100px);
}
.scheme-step {
  display: flex;
  flex-direction: row;
  gap: 40px;
  width: 100%;
  justify-content: center;
  &-item {
    display: flex;
    align-items: center;
    cursor: pointer;
    &-num {
      width: 30px;
      height: 30px;
      border-radius: 30px;
      background: var(--step-bg-color);
      line-height: 30px;
      text-align: center;
    }
    &-title {
      font-size: 16px;
      font-weight: 500;
      padding-left: 10px;
      color: #75798D;
    }
    .num-is-active {
      background: var(--step-active-bg-color);
      color: #FFFFFF;
    }
    .title-is-active {
      color: var(--step-active-bg-color);
    }
  }
}
.scheme-form {
  margin-top: 30px;
  .scheme-form-basic {
    ::v-deep.el-form .el-form-item__label  {
      line-height: 32px;
    }
  }
  .scheme-form-done {
    display: flex;
    justify-content: center;
    align-items: center;
  }
}
.scheme-footer {
  display: flex;
  justify-content: flex-end;
}
</style>

