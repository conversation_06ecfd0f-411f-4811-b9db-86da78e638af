<template>
  <el-dialog class="trade-dialog" title="添加交易" :visible="visible" :close-on-click-modal="false" :before-close="close">
    <div class="trade-content">
      <div class="trade-tree">
        <el-input v-model="filterText" placeholder="输入模块名称搜索" />
        <div class="trade-tree-wrapper">
          <el-tree
            ref="tradeTree"
            class="tree tree-icon"
            :props="{
              label: 'name',
              children: 'children'
            }"
            :data="treeData"
            :highlight-current="true"
            node-key="id"
            default-expand-all
            :icon-class="'iconfont el-icon-direction-right'"
            :expand-on-click-node="false"
            :filter-node-method="filterNode"
            @node-click="selectModule"
          >
            <div slot-scope="{ node, data }" class="custom-tree-node">
              <el-tooltip effect="dark" :content="node.label" placement="top">
                <div class="node-label">
                  <i v-if="data.nodeType === 1" class="iconfont el-icon-mokuai-weixuanzhong" style="padding-right: 4px" />
                  <div class="label" :data-node="node.level">{{ node.label }}</div>
                </div>
              </el-tooltip>
            </div>
          </el-tree>
          <vxe-loading v-model="treeLoading" text="正在加载..." />
        </div>
      </div>
      <div class="trade-table">
        <div class="no-selected-trade">
          <div class="table-title">未添加交易</div>
          <div class="table-wrapper">
            <vxe-table
              ref="no-selected-trade-table"
              class="vone-vxe-table"
              border
              height="auto"
              show-overflow="tooltip"
              :loading="tableLoading"
              :empty-render="{ name: 'empty' }"
              :data="noSelectedData"
              :column-config="{ minWidth: '120px' }"
              :checkbox-config="{ trigger: 'row' }"
              :row-config="{ keyField: 'id' }"
            >
              <vxe-column type="checkbox" width="40" />
              <vxe-column title="交易编码" field="code" width="120">
                <template #default="{ row }">
                  <span>{{ row.code }}</span>
                </template>
              </vxe-column>
              <vxe-column title="交易名称" field="name">
                <template #default="{ row }">
                  <span>{{ row.name }}</span>
                </template>
              </vxe-column>
            </vxe-table>
          </div>
        </div>
        <div class="selected-tool">
          <i class="iconfont el-icon-direction-double-left" @click="removeTrade" />
          <i class="iconfont el-icon-direction-double-right" @click="addTrade" />
        </div>
        <div class="selected-trade">
          <div class="table-title">已添加交易</div>
          <div class="table-wrapper">
            <vxe-table
              ref="selected-trade-table"
              class="vone-vxe-table"
              border
              height="auto"
              show-overflow="tooltip"
              :empty-render="{ name: 'empty' }"
              :data="selectedTableData"
              :column-config="{ minWidth: '120px' }"
              :checkbox-config="{ trigger: 'row' }"
              :row-config="{ keyField: 'id' }"
            >
              <vxe-column type="checkbox" width="40" />
              <vxe-column title="交易编码" field="code" width="120">
                <template #default="{ row }">
                  <span>{{ row.code }}</span>
                </template>
              </vxe-column>
              <vxe-column title="交易名称" field="name">
                <template #default="{ row }">
                  <span>{{ row.name }}</span>
                </template>
              </vxe-column>
            </vxe-table>
          </div>
        </div>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="close">取消</el-button>
      <el-button type="primary" :loading="loading" @click="save">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { getTradeTree, getTradeForModule } from '@/api/vone/project/scheme'
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    node: {
      type: Object,
      default: () => { }
    }
  },
  data() {
    return {
      filterText: '',
      treeLoading: false,
      treeData: [],
      loading: false,
      tableLoading: false,
      noSelectedData: [],
      selectedTableData: []
    }
  },
  watch: {
    filterText(val) {
      this.$refs.tradeTree.filter(val)
    }
  },
  mounted() {
    this.getTradeTreeData()
    if (this.node.attrs?.dataOptions?.selectData?.length) {
      this.selectedTableData = this.node.attrs?.dataOptions?.selectData
    }
  },
  methods: {
    filterNode(value, data) {
      if (!value) return true
      return data.name.indexOf(value) !== -1
    },
    async getTradeTreeData() {
      try {
        this.treeLoading = true
        const { data, isSuccess, msg } = await getTradeTree({})
        if (!isSuccess) return this.$message.warning(msg)
        this.treeData = data
      } catch (e) {
        return
      } finally {
        this.treeLoading = false
      }
    },
    async selectModule(node) {
      const params = node.nodeType ? {
        nodeType: 2,
        parentId: node.id
      } : {
        nodeType: 2,
        productId: node.id
      }
      try {
        this.tableLoading = true
        const { data, isSuccess, msg } = await getTradeForModule(params)
        if (!isSuccess) return this.$message.warning(msg)
        this.noSelectedData = data
      } catch {
        return
      } finally {
        this.tableLoading = false
      }
    },
    removeTrade() {
      const selectedData = this.getVxeTableSelectData('selected-trade-table') || []
      const removeIds = selectedData.map(ele => ele.id) || []
      this.selectedTableData = this.selectedTableData.filter(ele => !removeIds.includes(ele.id))
    },
    addTrade() {
      // 校验是否已选择交易
      const addData = this.getVxeTableSelectData('no-selected-trade-table') || []
      const selectedIds = this.selectedTableData?.map(ele => ele.id) || []
      const hasData = addData?.filter(ele => selectedIds.includes(ele.id)) || []
      if (hasData.length) {
        const tradeNames = hasData.map(ele => ele.name)?.join('、')
        this.$message.warning(`【${tradeNames}】已存在，请重新选择`)
        return
      }
      this.selectedTableData = [...this.selectedTableData, ...addData]
      this.$refs['no-selected-trade-table'].clearCheckboxRow()
    },
    async save() {
      try {
        this.$emit('update', {
          selectData: this.selectedTableData
        })
        this.close()
      } catch (e) {
        return
      }
    },
    close() {
      this.$emit('update:visible', false)
    }
  }
}
</script>

<style lang="scss" scoped>
.trade-dialog {
  ::v-deep.el-dialog {
    width: 80%;
  }

  ::v-deep.el-pagination {
    position: inherit;
  }

  ::v-deep.el-input__suffix {
    display: none;
  }
  .trade-content {
    display: flex;
    flex-direction: row;
    gap: 12px;
  }
  .trade-tree {
    border: 1px solid #e8eaec;
    width: 240px;
    padding: 12px;
    border-radius: 4px;
    .el-input {
      margin-bottom: 12px;
    }
    .trade-tree-wrapper {
      height: calc(90vh - 270px);
      overflow-y: auto;
      position: relative;
    }
    .tree-icon {
      ::v-deep.el-tree-node__expand-icon.is-leaf {
        color: transparent;
        cursor: default;
      }
    }
    .custom-tree-node {
      flex: 1
    }
    .node-label {
      display: inline-block;
      width: calc(100% - 10px);
      display: flex;
      align-items: center;
      .label {
        width: 125px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow:ellipsis;
      }
    }
  }
  .trade-table {
    flex: 1;
    height: calc(90vh - 200px);
    overflow-y: auto;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    gap: 8px;
    .no-selected-trade, .selected-trade {
      flex: 1;
      height: 100%;
      display: flex;
      flex-direction: column;
      gap: 10px;
    }
    .selected-tool {
      width: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      .iconfont {
        font-size: 26px;
        cursor: pointer;
        &:hover {
          color: #3E7BFA;
        }
      }
    }
    .table-title {
      font-weight: 500;
      color: #000000;
    }
    .table-wrapper {
      padding: 12px;
      border: 1px solid #e8eaec;
      border-radius: 4px;
      flex: 1;
      height: calc(100% - 32px);
    }
  }
}
</style>
