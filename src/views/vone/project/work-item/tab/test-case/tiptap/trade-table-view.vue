<template>
  <NodeViewWrapper
    :class="[editable ? 'custom-table-wrapper' : '', editable && selected ? 'custom-table-active' : '']"
  >
    <div v-if="editable" class="custom-table-header">
      <div class="table-title">
        <el-button icon="iconfont el-icon-tips-plus-circle" type="primary" @click="showTradeTable">
          添加
        </el-button>
      </div>
      <el-tooltip effect="dark" content="删除当前列表" placement="top">
        <el-button size="mini" type="text" class="del-btn" icon="iconfont el-icon-application-delete" @click="deleteNode" />
      </el-tooltip>
    </div>
    <div class="custom-table-content" style="height: 100%;">
      <vxe-table
        ref="selected-trade-table"
        class="vone-vxe-table"
        border
        min-height="100"
        show-overflow="tooltip"
        :empty-render="{ name: 'empty' }"
        :data="tableData"
        :column-config="{ minWidth: '120px' }"
        :row-config="{ keyField: 'id' }"
      >
        <vxe-column title="交易编码" field="code">
          <template #default="{ row }">
            <span>{{ row.code }}</span>
          </template>
        </vxe-column>
        <vxe-column title="交易名称" field="name">
          <template #default="{ row }">
            <span>{{ row.name }}</span>
          </template>
        </vxe-column>
      </vxe-table>
    </div>
    <select-trade-modal
      v-if="visible"
      :node="node"
      :visible.sync="visible"
      @update="updateData"
    />
  </NodeViewWrapper>
</template>

<script>
import { NodeViewWrapper } from '@tiptap/vue-2'
import SelectTradeModal from './select-trade-modal.vue'

export default {
  name: 'CustomTableView',
  components: {
    NodeViewWrapper,
    SelectTradeModal
  },
  props: {
    editor: {
      type: Object,
      default: () => { }
    },
    node: {
      type: Object,
      required: true
    },
    updateAttributes: {
      type: Function,
      required: true
    },
    deleteNode: {
      type: Function,
      required: true
    },
    selected: {
      type: Boolean,
      default: false
    },
    focused: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      visible: false,
      tableData: []
    }
  },
  computed: {
    editable() {
      return this.editor.options.editable
    }
  },
  watch: {
    'node.attrs.dataOptions': {
      handler(value) {
        if (value?.selectData?.length) {
          this.tableData = value.selectData || []
        }
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    updateData(data) {
      console.log(data, 123)
      this.updateAttributes({
        ...this.node.attrs.dataOptions,
        dataOptions: data
      })
    },
    showTradeTable() {
      this.visible = true
    }
  }
}
</script>

<style lang="scss" scoped>
.custom-table-wrapper {
  margin: 16px 0;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 0px 12px 12px;
}

.custom-table-active {
  border-color: #4290f7;
}

.custom-table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 0;

  .del-btn {
    color: #919399;

    &:hover {
      color: #ff0000;
    }
  }
}
</style>
