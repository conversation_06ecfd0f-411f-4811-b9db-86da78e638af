<template>
  <el-dialog
    class="task-dialog"
    title="添加任务"
    :visible="visible"
    :close-on-click-modal="false"
    :before-close="close"
  >
    <main :style="{ height: '350px' }">
      <vxe-table
        ref="task-table"
        class="vone-vxe-table"
        border
        height="auto"
        show-overflow="tooltip"
        :loading="tableLoading"
        :empty-render="{ name: 'empty' }"
        :data="tableData.records"
        :column-config="{ minWidth: '120px' }"
        :checkbox-config="{ reserve: true, checkRowKeys: defaultRowKeys }"
        :row-config="{
          keyField: 'id'
        }"
      >
        <vxe-column type="checkbox" width="36" fixed="left" align="center" />
        <vxe-column
          title="标题"
          field="name"
          min-width="200"
          fixed="left"
        >
          <template #default="{ row }">
            <span>{{ row.code + ' ' + row.name }}</span>
          </template>
        </vxe-column>
        <vxe-column title="类型" field="typeCode" width="120">
          <template #default="{ row }">
            <span v-if="row.typeCode && row.echoMap && row.echoMap.typeCode">
              {{ `${row.echoMap.typeCode.name}` }}
            </span>
            <span v-else>{{ row.typeCode }}</span>
          </template>
        </vxe-column>
        <vxe-column title="计划开始时间" field="planStime" width="135">
          <template #default="{ row }">
            <span v-if="row.planStime">
              {{ dayjs(row.planStime).format('YYYY-MM-DD HH:mm') }}
            </span>
            <span v-else>{{ row.planStime }}</span>
          </template>
        </vxe-column>
        <vxe-column title="计划完成时间" field="planEtime" width="135">
          <template #default="{ row }">
            <span v-if="row.planEtime">
              {{ dayjs(row.planEtime).format('YYYY-MM-DD HH:mm') }}
            </span>
            <span v-else>{{ row.planEtime }}</span>
          </template>
        </vxe-column>
        <vxe-column title="状态" field="stateCode" width="120">
          <template #default="{ row }">
            <span v-if="row.stateCode && row.echoMap && row.echoMap.stateCode">
              {{ `${row.echoMap.stateCode.name}` }}
            </span>
            <span v-else>{{ row.stateCode }}</span>
          </template>
        </vxe-column>
        <vxe-column title="处理人" field="handleBy" width="120">
          <template #default="{ row }">
            <span>
              <vone-remote-user
                v-model="row.handleBy"
                class="remoteuser"
                :default-data="[row.echoMap.handleBy]"
                :disabled="true"
              />
            </span>
          </template>
        </vxe-column>
        <vxe-column title="提出人" field="createdBy" width="120">
          <template #default="{ row }">
            <span>
              <vone-remote-user
                v-model="row.createdBy"
                class="remoteuser"
                :default-data="[row.echoMap.createdBy]"
                disabled
              />
            </span>
          </template>
        </vxe-column>
        <vxe-column title="负责人" field="leadingBy" width="120">
          <template #default="{ row }">
            <span>
              <vone-remote-user
                v-model="row.leadingBy"
                class="remoteuser"
                :default-data="[row.echoMap.leadingBy]"
                :disabled="true"
              />
            </span>
          </template>
        </vxe-column>
        <vxe-column title="关联需求" field="requirementId" width="100">
          <template #default="{ row }">
            <span v-if="row.requirementId && row.echoMap && row.echoMap.requirementId">
              {{ `${row.echoMap.requirementId.code} ${row.echoMap.requirementId.name}` }}
            </span>
            <span v-else>{{ row.requirementId }}</span>
          </template>
        </vxe-column>
        <vxe-column title="关联缺陷" field="bugId" width="100">
          <template #default="{ row }">
            <span v-if="row.bugId && row.echoMap && row.echoMap.bugId">
              {{ `${row.echoMap.bugId.code} ${row.echoMap.bugId.name}` }}
            </span>
            <span v-else>{{ row.bugId }}</span>
          </template>
        </vxe-column>
        <vxe-column title="进度" field="rateProgress" width="80">
          <template #default="{ row }">
            <el-tooltip placement="top" :content="` ${row.rateProgress ? row.rateProgress : 0}%`">
              <el-progress
                :percentage="row.rateProgress ? parseInt(row.rateProgress) : 0"
                color="var(--main-theme-color,#3e7bfa)"
                :show-text="false"
              />
            </el-tooltip>
          </template>
        </vxe-column>
        <vxe-column title="优先级" field="priorityCode" width="100">
          <template #default="{ row }">
            <span v-if="row.priorityCode && row.echoMap && row.echoMap.priorityCode">
              {{ `${row.echoMap.priorityCode.name}` }}
            </span>
            <span v-else>{{ row.priorityCode }}</span>
          </template>
        </vxe-column>
        <vxe-column field="planId" :title="$route.params.projectTypeCode == 'AGILE' ? '归属计划' : '归属里程碑'">
          <template #header>
            <span>{{ $route.params.projectTypeCode == 'AGILE' ? '归属计划' : '归属里程碑' }}</span>
          </template>
          <template #default="{ row }">
            <span v-if="row.planId && row.echoMap && row.echoMap.planId">
              {{ row.echoMap.planId.name }}
            </span>
            <span v-else>{{ row.planId }}</span>
          </template>
        </vxe-column>
        <vxe-column title="创建时间" field="createTime" width="120">
          <template #default="{ row }">
            <span v-if="row.createTime">
              {{ dayjs(row.createTime).format('YYYY-MM-DD') }}
            </span>
            <span v-else>{{ row.createTime }}</span>
          </template>
        </vxe-column>
      </vxe-table>
    </main>
    <vone-pagination ref="pagination" :total="tableData.total" @update="getInitTableData" />
    <span slot="footer" class="dialog-footer">
      <el-button @click="close">取消</el-button>
      <el-button type="primary" :loading="loading" @click="save">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { catchErr } from '@/utils'
import { apiGetTaskList } from '@/api/vone/project/task'
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    node: {
      type: Object,
      default: () => { }
    }
  },
  data() {
    return {
      loading: false,
      tableLoading: false,
      tableData: {
        records: []
      },
      formData: {
        projectId: [this.$route.params.id]
      },
      defaultRowKeys: []
    }
  },
  mounted() {
    if (this.node.attrs?.dataOptions?.selectData?.length) {
      this.defaultRowKeys = this.node.attrs?.dataOptions?.selectData.map(ele => ele.id) || []
    }
    this.getInitTableData()
  },
  methods: {
    async getInitTableData() {
      const tableAttr = this.$refs?.pagination?.pageObj || {
        current: 1,
        size: 20
      }
      if (this.$route.params.id) {
        this.formData.projectId = [this.$route.params.id]
      }
      const params = {
        ...tableAttr,
        extra: {},
        model: { ...this.formData }
      }
      this.tableLoading = true
      const [res, err] = await catchErr(apiGetTaskList(params))
      this.tableLoading = false
      if (err) return
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      res.data.records.forEach(element => {
        element.tag =
          element.tagId && element.tagId.length && element.echoMap && element.echoMap.tagId
            ? element.echoMap.tagId.map(r => r.name)
            : []
      })
      this.tableData = res.data
    },
    async save() {
      const selectData = this.getVxeTableSelectData('task-table')
      try {
        this.$emit('update', {
          selectData
        })
        this.close()
      } catch (e) {
        return
      }
    },
    close() {
      this.$emit('update:visible', false)
    }
  }
}
</script>

<style lang="scss" scoped>
.task-dialog {
	::v-deep.el-dialog {
		width: 70%;
	}
	::v-deep.el-pagination {
		position: inherit;
	}
	::v-deep.el-input__suffix {
		display: none;
	}
}
</style>
