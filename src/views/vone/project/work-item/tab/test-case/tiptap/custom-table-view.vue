<template>
  <NodeViewWrapper
    :class="[editable ? 'custom-table-wrapper' : '', editable && selected ? 'custom-table-active' : '']"
  >
    <div v-if="editable" class="custom-table-header" :style="{paddingBottom: createSimple ? '16px' : '6px'}">
      <div class="table-title" :style="{}">
        <div v-if="node.attrs.tableType === 'taskTable'" class="task-action">
          <el-button-group style="margin-right: 8px">
            <el-button icon="iconfont el-icon-tips-plus-circle" type="primary" @click="showTaskTable">
              添加
            </el-button>
            <el-tooltip content="快速新增" placement="top">
              <el-button
                style="min-width: 32px; padding: 0"
                :icon="`iconfont  ${
                  createSimple ? 'el-icon-direction-double-right' : 'el-icon-direction-double-down'
                }`"
                type="primary"
                @click.stop="createSimple = !createSimple"
              />
            </el-tooltip>
          </el-button-group>
          <simpleAddIssue
            v-if="createSimple"
            :type-code="'TASK'"
            :no-file="true"
            @getTaskForSuccess="crateTask"
            @cancel="createSimple = false"
          />
        </div>
        <!-- <span v-else>{{ getTableTitle(node.attrs.tableType) }}</span> -->
      </div>
      <el-tooltip v-if="!createSimple" effect="dark" content="删除当前列表" placement="top">
        <el-button size="mini" type="text" class="del-btn" icon="iconfont el-icon-application-delete" @click="deleteNode" />
      </el-tooltip>
    </div>
    <div class="custom-table-content">
      <task-table
        v-if="node.attrs.tableType === 'taskTable'"
        :node="node"
        :data-options="node.attrs.dataOptions"
      />
    </div>
    <select-table-modal
      v-if="node.attrs.tableType === 'taskTable' && visible"
      :node="node"
      :visible.sync="visible"
      @update="updateData"
    />
  </NodeViewWrapper>
</template>

<script>
import { NodeViewWrapper } from '@tiptap/vue-2'
import SelectTableModal from './select-table-modal.vue'
import TaskTable from './task-table.vue'
import simpleAddIssue from '@/views/vone/project/issue/function/simple-add-issue.vue'

export default {
  name: 'CustomTableView',
  components: {
    NodeViewWrapper,
    TaskTable,
    SelectTableModal,
    simpleAddIssue
  },
  props: {
    editor: {
      type: Object,
      default: () => { }
    },
    node: {
      type: Object,
      required: true
    },
    updateAttributes: {
      type: Function,
      required: true
    },
    deleteNode: {
      type: Function,
      required: true
    },
    selected: {
      type: Boolean,
      default: false
    },
    focused: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      visible: false,
      taskTableData: [],
      createSimple: false
    }
  },
  computed: {
    editable() {
      return this.editor.options.editable
    }
  },
  mounted() {
  },
  methods: {
    updateData(data) {
      this.updateAttributes({
        ...this.node.attrs.dataOptions,
        dataOptions: data
      })
    },
    getTableTitle(type) {
      const typeNames = {
        taskTable: '我的任务',
        default: '自定义组件'
      }
      return typeNames[type] || ''
    },
    showTaskTable() {
      this.visible = true
    },
    crateTask(res) {
      if (res.isSuccess) {
        const selectData = this.node.attrs?.dataOptions?.selectData || []
        this.updateAttributes({
          ...this.node.attrs.dataOptions,
          dataOptions: {
            selectData: [...selectData, res.data]
          }
        })
        this.createSimple = false
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.custom-table-wrapper {
  margin: 16px 0;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 0px 12px 12px;
}

.custom-table-active {
  border-color: #4290f7;
}

.custom-table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 0;

  .del-btn {
    color: #919399;

    &:hover {
      color: #ff0000;
    }
  }
}
</style>
