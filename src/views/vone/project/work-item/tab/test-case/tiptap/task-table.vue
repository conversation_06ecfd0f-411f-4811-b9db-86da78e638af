<template>
  <div style="height: 100%">
    <vxe-table
      ref="task-table"
      class="vone-vxe-table"
      border
      min-height="100"
      show-overflow="tooltip"
      :empty-render="{ name: 'empty' }"
      :data="tableData"
      :column-config="{ minWidth: '120px' }"
      :checkbox-config="{ reserve: true }"
      :row-config="{
        keyField: 'id'
      }"
    >
      <vxe-column
        title="标题"
        field="name"
        min-width="200"
        fixed="left"
      >
        <template #default="{ row }">
          <span>{{ row.code + ' ' + row.name }}</span>
        </template>
      </vxe-column>
      <vxe-column title="类型" field="typeCode" width="120">
        <template #default="{ row }">
          <span v-if="row.typeCode && row.echoMap && row.echoMap.typeCode">
            {{ `${row.echoMap.typeCode.name}` }}
          </span>
          <span v-else>{{ row.typeCode }}</span>
        </template>
      </vxe-column>
      <vxe-column title="计划开始时间" field="planStime" width="135">
        <template #default="{ row }">
          <span v-if="row.planStime">
            {{ dayjs(row.planStime).format('YYYY-MM-DD HH:mm') }}
          </span>
          <span v-else>{{ row.planStime }}</span>
        </template>
      </vxe-column>
      <vxe-column title="计划完成时间" field="planEtime" width="135">
        <template #default="{ row }">
          <span v-if="row.planEtime">
            {{ dayjs(row.planEtime).format('YYYY-MM-DD HH:mm') }}
          </span>
          <span v-else>{{ row.planEtime }}</span>
        </template>
      </vxe-column>
      <vxe-column title="状态" field="stateCode" width="120">
        <template #default="{ row }">
          <span v-if="row.stateCode && row.echoMap && row.echoMap.stateCode">
            {{ `${row.echoMap.stateCode.name}` }}
          </span>
          <span v-else>{{ row.stateCode }}</span>
        </template>
      </vxe-column>
      <vxe-column title="处理人" field="handleBy" width="120">
        <template #default="{ row }">
          <span>
            <vone-remote-user
              v-model="row.handleBy"
              class="remoteuser"
              :default-data="[row.echoMap.handleBy]"
              :disabled="true"
            />
          </span>
        </template>
      </vxe-column>
      <vxe-column title="提出人" field="createdBy" width="120">
        <template #default="{ row }">
          <span>
            <vone-remote-user
              v-model="row.createdBy"
              class="remoteuser"
              :default-data="[row.echoMap.createdBy]"
              disabled
            />
          </span>
        </template>
      </vxe-column>
      <vxe-column title="负责人" field="leadingBy" width="120">
        <template #default="{ row }">
          <span>
            <vone-remote-user
              v-model="row.leadingBy"
              class="remoteuser"
              :default-data="[row.echoMap.leadingBy]"
              :disabled="true"
            />
          </span>
        </template>
      </vxe-column>
      <vxe-column title="关联需求" field="requirementId" width="100">
        <template #default="{ row }">
          <span v-if="row.requirementId && row.echoMap && row.echoMap.requirementId">
            {{ `${row.echoMap.requirementId.code} ${row.echoMap.requirementId.name}` }}
          </span>
          <span v-else>{{ row.requirementId }}</span>
        </template>
      </vxe-column>
      <vxe-column title="关联缺陷" field="bugId" width="100">
        <template #default="{ row }">
          <span v-if="row.bugId && row.echoMap && row.echoMap.bugId">
            {{ `${row.echoMap.bugId.code} ${row.echoMap.bugId.name}` }}
          </span>
          <span v-else>{{ row.bugId }}</span>
        </template>
      </vxe-column>
      <vxe-column title="进度" field="rateProgress" width="80">
        <template #default="{ row }">
          <el-tooltip placement="top" :content="` ${row.rateProgress ? row.rateProgress : 0}%`">
            <el-progress
              :percentage="row.rateProgress ? parseInt(row.rateProgress) : 0"
              color="var(--main-theme-color,#3e7bfa)"
              :show-text="false"
            />
          </el-tooltip>
        </template>
      </vxe-column>
      <vxe-column title="优先级" field="priorityCode" width="100">
        <template #default="{ row }">
          <span v-if="row.priorityCode && row.echoMap && row.echoMap.priorityCode">
            {{ `${row.echoMap.priorityCode.name}` }}
          </span>
          <span v-else>{{ row.priorityCode }}</span>
        </template>
      </vxe-column>
      <vxe-column field="planId" :title="$route.params.projectTypeCode == 'AGILE' ? '归属计划' : '归属里程碑'">
        <template #header>
          <span>{{ $route.params.projectTypeCode == 'AGILE' ? '归属计划' : '归属里程碑' }}</span>
        </template>
        <template #default="{ row }">
          <span v-if="row.planId && row.echoMap && row.echoMap.planId">
            {{ row.echoMap.planId.name }}
          </span>
          <span v-else>{{ row.planId }}</span>
        </template>
      </vxe-column>
      <vxe-column title="创建时间" field="createTime" width="120">
        <template #default="{ row }">
          <span v-if="row.createTime">
            {{ dayjs(row.createTime).format('YYYY-MM-DD') }}
          </span>
          <span v-else>{{ row.createTime }}</span>
        </template>
      </vxe-column>
    </vxe-table>
  </div>
</template>

<script>
export default {
  props: {
    node: {
      type: Object,
      default: () => { }
    },
    dataOptions: {
      type: Object,
      default: () => { }
    }
  },
  data() {
    return {
      tableData: []
    }
  },
  watch: {
    'node.attrs.dataOptions': {
      handler(value) {
        if (value?.selectData?.length) {
          this.tableData = value.selectData || []
        }
      },
      immediate: true,
      deep: true
    }
  },
  mounted() {
  },

  methods: {

  }
}
</script>
<style lang="scss" scoped>
.vone-vxe-table {
	::v-deep.el-input__suffix {
		display: none;
	}
}
</style>
