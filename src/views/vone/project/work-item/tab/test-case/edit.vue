<template>
  <div class="sectionPageContent">
    <div class="leftSection" :style="{'width': openFlag ? '0' : '320px', 'marginRight': openFlag ? '0' : '10px' }">
      <div class="header">
        <div>方案章节</div>
        <div class="icon-style">
          <el-tooltip v-if="!chapterData.length" content="添加章节" placement="top">
            <i class="iconfont el-icon-icon-line-expand" style="padding-right: 6px" @click="addChapter(null, false)" />
          </el-tooltip>
          <el-tooltip content="添加为模版" placement="top">
            <i class="iconfont el-icon-application-copy-content" @click="addTemplate" />
          </el-tooltip>
        </div>
      </div>
      <div class="treeContent">
        <el-tree
          ref="chapterTree"
          class="tree tree-icon"
          :props="{
            label: 'name'
          }"
          :data="chapterData"
          :highlight-current="true"
          node-key="id"
          default-expand-all
          :icon-class="'iconfont el-icon-direction-right'"
          :expand-on-click-node="false"
          @node-click="selectChapter"
        >
          <span slot-scope="{ node, data }" class="custom-tree-node">
            <el-tooltip v-if="data.isShow" class="node-label" effect="dark" :content="node.label" placement="top">
              <div>
                <span class="label" :data-node="node.level">{{ node.label }}</span>
              </div>
            </el-tooltip>
            <div v-else class="node-label" @mouseenter="(e) => isShowToltip(e, data)" @mouseout="hideTip(data)">
              <span class="label" :data-node="node.level">{{ node.label }}</span>
            </div>
            <span class="manager">
              <svg-icon v-if="data.echoMap.stateCode" :icon-class="data.echoMap.stateCode.code ==='DONE' ? 'done' : 'todo'" />
              <el-tag v-if="data.echoMap.leadingBy" effect="dark">{{ data.echoMap.leadingBy.name }}</el-tag>
            </span>
            <span class="operation-tree-icon">
              <el-tooltip v-if="!data.parentId" class="item" content="在下方添加" placement="top">
                <el-button
                  type="text"
                  icon="iconfont el-icon-icon-line-expand"
                  @click.stop="addChapter(data, false)"
                />
              </el-tooltip>
              <el-tooltip class="item" content="添加子章节" placement="top">
                <el-button
                  type="text"
                  icon="iconfont el-icon-application-add-content"
                  @click.stop="addChapter(data, true)"
                />
              </el-tooltip>
              <el-tooltip class="item" content="用户权限" placement="top">
                <el-button
                  type="text"
                  icon="iconfont el-icon-icon-yonghuquanxian"
                  @click.stop="userPerm(data)"
                />
              </el-tooltip>
              <el-tooltip class="item" content="删除" placement="top">
                <el-button
                  type="text"
                  icon="iconfont el-icon-application-delete"
                  @click.stop="remove(data)"
                />
              </el-tooltip>
            </span>
          </span>
        </el-tree>
        <vxe-loading v-model="treeLoading" text="正在加载..." />
      </div>
    </div>
    <div class="rightSection">
      <div class="header">
        <div class="icon-style">
          <el-tooltip v-if="openFlag" class="item" effect="dark" content="展开" placement="top">
            <i class="iconfont el-icon-direction-menu-unfold" @click="() => openFlag = !openFlag" />
          </el-tooltip>
          <el-tooltip v-else class="item" effect="dark" content="收起" placement="top">
            <i class="iconfont el-icon-direction-menu-fold" @click="() => openFlag = !openFlag" />
          </el-tooltip>
          <div v-if="chapterNode" class="status-wrapper">
            <span style="padding-right: 8px">{{ chapterNode ? chapterNode.name : '' }}</span>
            <el-dropdown v-if="editable" trigger="click" @command="chapterFlowStatus">
              <span class="el-dropdown-link">
                <el-tag effect="dark" :type="chapterNode.stateCode === 'DONE' ? 'success' : ''">
                  {{ stateTag }}
                </el-tag>
              </span>
              <el-dropdown-menu slot="dropdown" placement="bottom">
                <el-dropdown-item
                  v-for="item in flowStatus"
                  :key="item.stateCode"
                  :command="item.stateCode"
                >
                  {{ item.name }}
                </el-dropdown-item>
                <el-dropdown-item v-if="!flowStatus.length" command="NO">
                  暂无可流转状态
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </div>
        </div>
        <div class="operation">
          <el-button @click="previewDoc">预览</el-button>
          <el-button v-if="chapterNode && editable" type="primary" @click="saveDoc">保存</el-button>
        </div>
      </div>
      <div class="scheme-editor">
        <tiptap-editor
          v-if="content"
          height="calc(100vh - 175px)"
          :content="content"
          :show-custom-toolbar="['task', 'trade']"
          :extensions="extensions"
          :show-toolbar="showToolbar"
          @created="editorCreated"
        />
        <vxe-loading v-model="loading" text="编辑器初始化中，请稍后..." />
      </div>
    </div>
    <add-chapter-modal
      v-if="chapterVisible"
      :visible.sync="chapterVisible"
      :chapter-node="chapterNode"
      :space-id="spaceId"
      :is-child.sync="isChild"
      @refresh="getDocChapters"
    />
    <add-perm-modal
      v-if="permVisible"
      :visible.sync="permVisible"
      :chapter-node="chapterNode"
      @refresh="getDocChapters"
    />
    <add-template-modal
      v-if="templateVisible"
      :visible.sync="templateVisible"
      :space-id="spaceId"
    />
  </div>
</template>

<script>
import {
  getChapters,
  delChapter,
  getDocByCatalogId,
  addDocument,
  editDocument,
  getChapterStatus,
  flowChapterStatus,
  getAllDocument
} from '@/api/vone/knowledge'
import { getSpaceId } from '@/api/vone/project/scheme'
import { textRange } from '@/utils'
import AddChapterModal from './components/add-chapter-modal.vue'
import AddPermModal from './components/add-permission-modal.vue'
import { CustomTableExtension, TradeTableExtension } from 'tiptap-vue-package'
import CustomTableView from './tiptap/custom-table-view.vue'
import TradeTableView from './tiptap/trade-table-view.vue'
import AddTemplateModal from './components/add-template-modal.vue'
export default {
  components: {
    AddChapterModal,
    AddPermModal,
    AddTemplateModal
  },
  data() {
    return {
      loading: false,
      treeLoading: false,
      chapterData: [],
      visible: false,
      chapterNode: null,
      openFlag: false,
      chapterVisible: false,
      spaceId: '',
      isChild: false,
      showToolbar: true,
      editor: null,
      content: null,
      documentId: null,
      extensions: [
        CustomTableExtension.configure({ nodeViewComponents: CustomTableView }),
        TradeTableExtension.configure({ nodeViewComponents: TradeTableView })
      ],
      permVisible: false,
      flowStatus: [],
      templateVisible: false
    }
  },
  computed: {
    routePs() {
      const { id, projectKey, projectTypeCode, schemeId } = this.$route.params
      return { id, projectKey, projectTypeCode, schemeId }
    },
    loginUser() {
      return this.$store.state?.user?.user?.id
    },
    stateTag() {
      return this.chapterNode?.echoMap?.stateCode?.name
    },
    editable() {
      return this.editor?.options?.editable
    }
  },
  watch: {
  },
  async mounted() {
    const { data, isSuccess } = await getSpaceId(this.$route.params.id)
    if (isSuccess) {
      this.spaceId = data.id
    } else {
      this.$message.warning('获取空间ID失败，请稍后重试')
      return
    }
    this.getDocChapters()
    // 获取所有内容
    this.getAllDoc()
  },
  methods: {
    editorCreated(editor) {
      this.editor = editor
      if (this.loginUser !== this.chapterNode?.leadingBy) {
        this.editor.setEditable(false)
      } else {
        this.editor.setEditable(true)
      }
    },
    // 获取章节目录
    async getDocChapters() {
      try {
        this.treeLoading = true
        const { data, isSuccess, msg } = await getChapters({ bizId: this.routePs.schemeId })
        if (!isSuccess) return this.$message.warning(msg)
        this.chapterData = data
      } catch (e) {
        return
      } finally {
        this.treeLoading = false
      }
    },
    isShowToltip(e, node) {
      const bool = textRange(e.target)
      this.$set(node, 'isShow', bool)
    },
    hideTip(node) {
      this.$set(node, 'isShow', false)
    },
    // 选择章节
    async selectChapter(node) {
      this.content = null
      this.loading = true
      this.documentId = null
      this.chapterNode = node
      this.showToolbar = true
      const content = await this.getDoc(node.id)
      this.getChapterNextStatus(node.id)
      if (!content.length) {
        this.content = {
          type: 'doc',
          content: [
            {
              type: 'heading',
              attrs: { level: 2, lock: true },
              content: [
                { type: 'text', text: node.name }
              ]
            }
          ]
        }
      } else {
        const editorContent = JSON.parse(content?.[content.length - 1].content)
        this.content = {
          type: 'doc',
          content: editorContent
        }
      }
      this.loading = false
    },
    async getDoc(id) {
      const { data, isSuccess, msg } = await getDocByCatalogId({ catalogId: id })
      if (!isSuccess) return this.$message.warning(msg)
      this.documentId = data?.[0]?.id || null
      return data || []
    },
    addChapter(data, isChild) {
      this.chapterNode = data
      this.isChild = isChild
      this.$nextTick(() => {
        this.chapterVisible = true
      })
    },
    // 删除章节
    async remove(data) {
      if (data.children?.length) return this.$message.warning('当前章节下有子章节,不允许直接删除，请先删除子章节')
      this.$confirm(`是否删除章节【${data.name}】?`, '删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        customClass: 'delConfirm'
      }).then(() => {
        delChapter([data.id]).then((res) => {
          if (res.isSuccess) {
            this.$message.success('删除成功')
            this.getDocChapters()
          } else {
            this.$message.warning(res.msg)
          }
        })
      }).catch(() => { })
    },
    // 设置权限
    userPerm(data) {
      this.chapterNode = data
      this.permVisible = true
    },
    // 保存文档
    async saveDoc() {
      // 获取文档内容
      let content = this.editor.getJSON()?.content || []
      let res = null
      content = content.filter(ele => !(!ele.content && ele.type === 'paragraph'))
      if (content?.length) {
        try {
          const params = {
            catalogId: this.chapterNode?.id,
            content: JSON.stringify(content),
            type: 'chapter',
            sort: 1
          }
          if (this.documentId) {
            params.id = this.documentId
            res = await editDocument(params)
          } else {
            res = await addDocument(params)
          }
          if (!res.isSuccess) return this.$$message.warning(res.msg)
          this.$message.success('文档内容已保存')
        } catch {
          return
        }
      } else {
        this.$message.warning('当前无内容，请编写后保存')
      }
    },
    // 获取当前章节可流转的节点
    async getChapterNextStatus(id) {
      const { data, isSuccess, msg } = await getChapterStatus(id)
      if (!isSuccess) return this.$message.warning(msg)
      this.flowStatus = data
    },
    // 流转章节状态 flowChapterStatus
    async chapterFlowStatus(command) {
      if (command && command !== 'NO') {
        const { isSuccess, msg } = await flowChapterStatus(this.chapterNode.id, this.chapterNode.stateCode, command)
        if (!isSuccess) return this.$message(msg)
        // 更新树
        await this.getDocChapters()
        this.$nextTick(async() => {
          const node = this.$refs.chapterTree.getNode(this.chapterNode.id)
          // 重新获取当前章节信息
          await this.selectChapter(node?.data)
          this.$refs.chapterTree.setCurrentKey(node.data.id)
        })
      }
    },
    // 获取所有章节内容
    async getAllDoc() {
      try {
        this.loading = true
        const { data, isSuccess, msg } = await getAllDocument({ bizId: this.routePs.schemeId })
        if (!isSuccess) return this.$message.warning(msg)
        this.showToolbar = false
        let allContent = []
        data.forEach(ele => {
          const content = JSON.parse(ele.content) || null
          if (content) {
            allContent = [...allContent, ...content]
          }
        })
        this.content = {
          type: 'doc',
          content: allContent
        }
      } catch {
        this.loading = false
        return
      } finally {
        this.loading = false
      }
    },
    // 预览
    previewDoc() {
      this.$refs.chapterTree.setCurrentKey(null)
      this.chapterNode = null
      this.content = null
      this.getAllDoc()
    },
    // 添加为模板
    addTemplate() {
      this.templateVisible = true
    }
  }
}
</script>

<style lang='scss' scoped>
@import "~@/styles/variables.scss";
@import "./edit.scss"
</style>
