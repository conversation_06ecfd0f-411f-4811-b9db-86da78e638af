.leftSection {
  width: 320px;
  position: relative;
}
.header {
  height: 47px;
  border-bottom: 1px solid var(--el-divider);
  padding: 0 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.icon-style {
  display: flex;
  align-items: center;
  .iconfont {
    cursor: pointer;
    color: var(--font-second-color);
  }
  .iconfont:hover {
    color: var(--main-theme-color);
  }
  .iconfont.active {
    color: var(--main-theme-color);
  }
}
.treeContent {
  margin-top:8px;
  height: calc(100vh - #{$nav-top-height} - #{$main-margin} - #{$main-margin} - 57px);
  overflow-y: overlay;
  position: relative;
  .tree-icon {
    ::v-deep.el-tree-node__expand-icon.is-leaf {
      color: transparent;
      cursor: default;
    }
  }
  .custom-tree-node {
    flex: 1;
    width: calc(100% - 90px);
    height: 100%;
    display: flex;
    align-items: center;
  }
  .el-tree-node__content {
    height: 36px;
    color: var(--font-main-color);
    display: inline-block;
    .node-label {
      display: inline-block;
      width: calc(100% - 110px);
      overflow: hidden;
      white-space: nowrap;
      text-overflow:ellipsis;
      .svg-icon {
        width: 16px;
        height: 16px;
        vertical-align: -0.2em;
      }
    }
    .manager {
      position: absolute;
      right: 16px;
      display: flex;
      align-items: center;
    }
    .operation-tree-icon {
      .el-button {
        padding: 0px;
        height: unset;
        line-height: unset;
        min-width: unset;
        font-size: 16px;
        color: var(--font-second-color);
      }
      .el-button.is-disabled {
        background-color: unset;
        border: unset;
        color: var(--input-border-color) !important;
      }
      .el-button:hover {
        color: var(--main-theme-color);
      }
      opacity: 0;
      position: absolute;
      right: 16px;
    }
    &:hover {
      .manager {
        display: none
      }
      .operation-tree-icon {
        opacity: 1;
        background: var(--hover-bg-color);
      }
    }
    .svg-icon {
      margin-right: 4px;
    }
  }
}
::v-deep .el-tree-node__expand-icon{
  color: #777F8E;
}
.rightSection{
  position: relative;
  padding: 0;
  .scheme-editor {
    ::v-deep.tiptap-toolbar-wrapper {
      padding: 0;
    }
  }
  .status-wrapper {
    padding-left: 8px;
    display: flex;
    align-items: center;
    .el-dropdown {
      cursor: pointer;
    }
  }
}