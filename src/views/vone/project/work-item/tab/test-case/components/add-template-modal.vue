<template>
  <el-dialog
    class="dialogContainer"
    title="添加为模板"
    :visible="visible"
    width="456px"
    :close-on-click-modal="false"
    :before-close="close"
  >
    <el-form ref="form" :model="form" :rules="rules">
      <el-form-item label="模板名称" prop="name">
        <el-input v-model="form.name" placeholder="请输入模板名称" />
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="close">取消</el-button>
      <el-button type="primary" :loading="loading" @click="save">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { saveTemplate } from '@/api/vone/knowledge'
import { getTestSchemeDetail } from '@/api/vone/project/scheme'
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    spaceId: {
      type: String,
      default: ''
    },
    schemeId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      form: {
        name: '',
        bizId: this.$route.params.schemeId || this.schemeId,
        bizType: 'TEST_SCHEME',
        spaceId: this.spaceId
      },
      rules: {
        name: [{ required: true, message: '请输入模板名称', trigger: 'change' }]
      },
      loading: false
    }
  },
  async mounted() {
    try {
      const { data, isSuccess } = await getTestSchemeDetail(this.$route.params.schemeId || this.schemeId)
      if (isSuccess) {
        this.form.name = `${data.name}_模板` || ''
      }
    } catch { return }
  },
  methods: {
    async save() {
      try {
        await this.$refs.form.validate()
      } catch (e) {
        return
      }
      try {
        this.loading = true
        const { isSuccess, msg } = await saveTemplate(this.form)
        if (!isSuccess) return this.$message.warning(msg)
        this.close()
      } catch (e) {
        this.loading = false
        return
      } finally {
        this.loading = false
      }
    },
    close() {
      this.$emit('update:visible', false)
      this.$refs['form'].resetFields()
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
