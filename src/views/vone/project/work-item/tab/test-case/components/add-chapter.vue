<template>
  <div>
    <el-button type="primary" style="margin-bottom: 8px" @click="selectTemplate">使用模版</el-button>
    <div style="height: calc(100vh - 240px)">
      <vxe-table
        ref="test-chapter-table"
        class="vone-vxe-table catalog-table"
        border
        height="auto"
        show-overflow="tooltip"
        :empty-render="{ name: 'empty' }"
        :data="tableData"

        :cell-config="{height: '48px'}"
        :edit-config="{ trigger: 'click', mode: 'cell',showIcon:false }"
        :tree-config="{
          transform: true,
          rowField: 'id',
          parentField: 'parentId',
          expandAll: true
        }"
        :column-config="{ minWidth: '120px' }"
      >
        <vxe-column
          title="章节名称"
          field="name"
          :edit-render="{autofocus: '.vxe-input--inner', placeholder: '请点击输入章节名称'}"
          tree-node
        >
          <template #edit="{ row }">
            <vxe-input v-model="row.name" type="text" placeholder="请输入章节名称" />
          </template>
        </vxe-column>
        <vxe-column width="300" title="负责人" field="leadingBy">
          <template #default="{row}">
            <vone-remote-user v-model="row.leadingBy" :project-id="$route.params.id" />
          </template>
        </vxe-column>
        <vxe-column title="操作" fixed="right" align="left" width="120">
          <template #default="{ row }">
            <el-tooltip v-if="!row.parentId" class="item" content="在下方添加" placement="top">
              <el-button
                type="text"
                icon="iconfont el-icon-icon-line-expand"
                @click="addCatalog(row)"
              />
            </el-tooltip>
            <el-divider v-if="!row.parentId" direction="vertical" />
            <el-tooltip class="item" content="添加子章节" placement="top">
              <el-button
                type="text"
                icon="iconfont el-icon-application-add-content"
                @click="addCatalogChild(row)"
              />
            </el-tooltip>
            <el-divider direction="vertical" />
            <el-tooltip class="item" content="删除" placement="top">
              <el-button
                type="text"
                icon="iconfont el-icon-application-delete"
                @click="delCatalog(row)"
              />
            </el-tooltip>
          </template>
        </vxe-column>
      </vxe-table>
    </div>
    <template-manage-modal
      v-if="visible"
      :visible.sync="visible"
      :space-id="spaceId"
    />
  </div>
</template>

<script>
import { snowGuid } from '@/utils'
import { getSpaceId } from '@/api/vone/project/scheme'
import { batchAddChapter } from '@/api/vone/knowledge'
import templateManageModal from './template-manage-modal.vue'
export default {
  components: {
    templateManageModal
  },
  data() {
    return {
      tableData: [],
      spaceId: '',
      visible: false
    }
  },
  computed: {},
  async mounted() {
    const { data, isSuccess } = await getSpaceId(this.$route.params.id)
    if (isSuccess) {
      this.spaceId = data.id
    } else {
      this.$message.warning('获取空间ID失败，请稍后重试')
      return
    }
    const $table = this.$refs['test-chapter-table']
    if ($table) {
      const record = {
        parentId: null,
        id: snowGuid(),
        name: '',
        leadingBy: this.$store.state.user.user.id,
        level: 1,
        sort: 1,
        bizId: this.$route.query?.schemeId,
        bizType: 'TEST_SCHEME',
        type: 'CHAPTER',
        spaceId: data.id
      }
      const { row: newRow } = await $table.insert(record)
      await $table.setEditCell(newRow, 'name')
    }
  },
  methods: {
    async addCatalog(row) {
      if (row.name.trim() === '') {
        this.$message.warning('请完善章节名称后再添加！')
        return
      }
      const $table = this.$refs['test-chapter-table']
      const record = {
        parentId: null,
        id: snowGuid(),
        name: '',
        leadingBy: this.$store.state.user.user.id,
        level: 1,
        sort: 1,
        bizId: this.$route.query?.schemeId,
        bizType: 'TEST_SCHEME',
        type: 'CHAPTER',
        spaceId: this.spaceId
      }
      const { row: newRow } = await $table.insertNextAt(record, row)
      await $table.setEditCell(newRow, 'name')
    },
    async updataTable() {
      const data = this.$refs['test-chapter-table'].getTableData()?.fullData
      const processNode = (node, index) => {
        const content = [
          {
            type: 'heading',
            attrs: { level: node.level, lock: true },
            content: [
              { type: 'text', text: node.name }
            ]
          }
        ]
        // eslint-disable-next-line no-unused-vars
        const { _X_ROW_KEY, _X_ROW_CHILD, id, parentId, ...rest } = node
        const newNode = {
          ...rest,
          sort: index + 1,
          content: JSON.stringify(content)
        }
        if (node.children && node.children.length > 0) {
          newNode.children = node.children.map((child, childIndex) =>
            processNode(child, childIndex)
          )
        }
        return newNode
      }
      const result = data.map((ele, index) => processNode(ele, index))
      try {
        const { id, projectKey, projectTypeCode } = this.$route.params
        const schemeId = this.$route.query?.schemeId
        const { isSuccess, msg } = await batchAddChapter(result)
        if (!isSuccess) return this.$message.warning(msg)
        this.$router.replace(`/project/testCase/add/${projectKey}/${projectTypeCode}/${id}?step=3&schemeId=${schemeId}`)
      } catch (e) {
        return
      }
    },
    async addCatalogChild(row) {
      if (row.name.trim() === '') {
        this.$message.warning('请完善章节名称后再添加！')
        return
      }
      const $table = this.$refs['test-chapter-table']
      const record = {
        parentId: row.id,
        id: snowGuid(),
        name: '',
        leadingBy: this.$store.state.user.user.id,
        level: row.level + 1,
        sort: 1,
        bizId: this.$route.query?.schemeId,
        bizType: 'TEST_SCHEME',
        type: 'CHAPTER',
        spaceId: this.spaceId
      }
      const { row: newRow } = await $table.insertChildNextAt(record, row, -1)
      $table.setTreeExpand(row, true)
      await $table.setEditCell(newRow, 'name')
    },
    async delCatalog(row) {
      const $table = this.$refs['test-chapter-table']
      const data = $table.getTableData()?.fullData || []
      if (data.length === 1) {
        this.$message.warning('请保留一行数据')
        return
      }
      if (row.children?.length) {
        await this.$confirm(`删除父级目录将一同删除子级目录，是否删除?`, '删除', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          customClass: 'delConfirm',
          showClose: false,
          type: 'warning'
        }).then(() => {
          $table.remove(row)
        }).catch(() => {})
        return
      }
      $table.remove(row)
    },
    selectTemplate() {
      this.visible = true
    }
  }
}
</script>
<style lang="scss" scoped>
.catalog-table {
  ::v-deep.vxe-table--body {
    .vxe-cell {
      padding: 6px 10px;
      .el-input__inner {
        border: none;
      }
    }
  }
}
</style>
