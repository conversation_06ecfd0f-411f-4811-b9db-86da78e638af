<template>
  <div>
    <el-dialog :title="title" :visible="visible" top="5vh" width="800px" :before-close="onClose" :close-on-click-modal="false" v-on="$listeners">

      <el-form ref="dialogForm" :model="dialogForm" :rules="rules" label-position="top" :disabled="view">
        <el-row class="basicHeader" :class="{disabled:view}" :gutter="24">
          <el-form-item class="headerTitle fixedItem" prop="name">
            <span v-if="view" class="title">{{ dialogForm.name }}</span>
            <el-input v-else v-model="dialogForm.name" placeholder="请输入名称" />
          </el-form-item>
          <el-col :span="spanCol">
            <div class="fixedItem">
              <!-- 人员头像 -->
              <span v-if="echoMap && echoMap.leadingBy">
                <vone-user-avatar :avatar-path="echoMap.leadingBy.avatarPath" :avatar-type="true" :show-name="false" height="32px" width="32px" />
              </span>
              <span v-else>
                <i class="iconfont el-icon-icon-light-avatar" style="font-size:32px" />
              </span>
              <el-form-item label="处理人" prop="leadingBy">
                <span v-if="view" style="margin-left:10px;">{{ echoMap.leadingBy&&echoMap.leadingBy.name }}</span>
                <el-select v-else v-model="dialogForm.leadingBy" clearable filterable :data="pUserList" @change="getSelectUser">
                  <el-option v-for="item in pUserList" :key="item.id" :label="item.name" :value="item.id">
                    <vone-user-avatar v-if="item.avatarPath" :avatar-path="item.avatarPath" :avatar-type="item.avatarType" :show-name="false" height="22px" width="22px" />
                    {{ item.name }}
                  </el-option>
                </el-select>
              </el-form-item>
            </div>
          </el-col>
          <el-col :span="spanCol">
            <div class="fixedItem">
              <i class="iconfont el-icon-icon-fill-zhuangtai" :style="{color:dialogForm.stateCode =='1'? '#adb0b8':dialogForm.stateCode=='2'?'#64befa':'#3cb540'}" />
              <el-form-item label="状态" prop="stateCode">
                <span v-if="view" style="margin-left:10px;">{{ stateMap[dialogForm.stateCode] }}</span>
                <el-select v-else v-model="dialogForm.stateCode" placeholder="请选择状态">
                  <el-option v-for="i in stateOption" :key="i.id" :label="i.name" :value="i.code">
                    <i style="display:inline-block;width:10px;height:10px;border-radius:50%;" :style="{'color':i.color,border:`2px solid ${i.color}`}" />
                    {{ i.name }}
                  </el-option>
                </el-select>
              </el-form-item>
            </div>

          </el-col>
          <el-col v-if="dialogType === 'PLAN'" :span="6">
            <div class="fixedItem">
              <svg-icon icon-class="vone-date" />
              <el-form-item label="开始日期" prop="planStime">
                <span v-if="view" style="margin-left:10px;">{{ dialogForm.planStime }}</span>
                <el-date-picker v-else v-model="dialogForm.planStime" type="date" format="yyyy-MM-dd" value-format="yyyy-MM-dd" placeholder="请选择开始日期" style="width:130px;" :picker-options="pickerOptionsStart" />
              </el-form-item>
            </div>

          </el-col>
          <el-col :span="spanCol">
            <div class="fixedItem">
              <svg-icon icon-class="vone-date" />
              <el-form-item label="完成日期" prop="planEtime">
                <span v-if="view" style="margin-left:10px;">{{ dialogForm.planEtime }}</span>
                <el-date-picker v-else v-model="dialogForm.planEtime" type="date" format="yyyy-MM-dd" value-format="yyyy-MM-dd" placeholder="请选择完成日期" style="width:130px;" :picker-options="pickerOptions" />
              </el-form-item>
            </div>

          </el-col>
        </el-row>
        <el-row class="backContainer">
          <el-form-item label="描述" prop="description" class="desc">
            <!-- <vone-editor ref="editor" v-model="iterationForm.description" :height="130" @input.native="eventDisposalRangeChange(iterationForm.description)" /> -->
            <span v-if="view">{{ dialogForm.description }}</span>
            <el-input v-else v-model="dialogForm.description" type="textarea" :rows="2" placeholder="请输入描述" show-word-limit maxlength="255" />

            <div v-if="view&&!dialogForm.description">--</div>
          </el-form-item>
          <template v-if="dialogType === 'PLAN' || dialogType === 'MILESTONE'">
            <div class="deliver">
              <span>{{ dialogType ==="PLAN"?'关联工作项':'交付物' }}</span>
              <template v-if="!view">
                <el-button v-if="dialogType ==='PLAN'" icon="iconfont el-icon-edit-relate" type="text" @click="connectItem">关联</el-button>
                <el-button v-else icon="el-icon-plus" type="text" @click="addMilestoneDeliver">交付物目标</el-button>
              </template>
            </div>
            <!-- 添加交付物 -->
            <el-form v-if="isShow" ref="addDeliverablesForm" :model="addDeliverablesForm" class="addDeliverForm">
              <el-row :gutter="20">
                <el-col :span="14">
                  <el-form-item
                    prop="name"
                    :rules="[
                      { required: true, message: '请输入目标名称', trigger: 'blur'},
                      { pattern: '^.{1,100}$', message: '名称不超过100个字符' }
                    ]"
                  >
                    <el-input v-model="addDeliverablesForm.name" placeholder="请输入目标名称" />
                  </el-form-item>
                </el-col>
                <el-col :span="4">
                  <el-form-item
                    prop="type"
                    :rules="[
                      { required: true, message: '请选择类型', trigger: 'change'}
                    ]"
                  >
                    <el-select v-model="addDeliverablesForm.type" placeholder="请选择">
                      <el-option label="文件" value="FILE" />
                      <el-option label="链接" value="LINK" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-button @click="resetForm">取消</el-button>&nbsp;
                  <el-button :loading="addLoading" type="primary" @click="onSubmit">确定</el-button>
                </el-col>
              </el-row>
            </el-form>
            <!-- 关联工作项表格 -->
            <el-table ref="connectItemTable" v-loading="tableLoading" size="medium" :data="tableData" max-height="215px">
              <template v-if="dialogType ==='PLAN'">
                <el-table-column label="名称" prop="name" show-overflow-tooltip>
                  <template v-slot="{ row }">
                    <div style="display: inline-flex;align-items: center;gap: 0 6px;font-size: 14px;">
                      <i :class="['iconfont',row.echoMap.typeCode.icon]" :style="{color:row.echoMap.typeCode.color}" />
                      <span>{{ row.name }}</span>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column label="处理人" prop="leadingBy" width="120px">
                  <template v-slot="{ row }">
                    <span v-if="row.leadingBy && row.echoMap && row.echoMap.leadingBy">
                      <vone-user-avatar :avatar-path="row.echoMap.leadingBy.avatarPath" :name="row.echoMap.leadingBy.name" />
                    </span>
                    <span v-else class="noSetting">
                      <!-- <i class="iconfont el-icon-icon-light-avatar" style="font-size:26px" /> -->
                      <span> -- </span>
                    </span>
                  </template>
                </el-table-column>
                <el-table-column label="状态" prop="stateCode" width="120px">
                  <template v-slot="{ row }">
                    <span v-if="row.echoMap&&row.echoMap.stateCode" class="tagCustom" :style="{color:row.echoMap.stateCode.color,border:'1px solid currentColor'}">{{ row.echoMap.stateCode.name }}</span>
                    <span v-else class="status-text">{{ row.stateCode }}</span>
                  </template>
                </el-table-column>
                <el-table-column v-if="!view" label="操作" width="60px">
                  <template v-slot="{row}">
                    <el-tooltip content="取消关联" placement="top">
                      <el-button type="text" size="mini" icon="iconfont el-icon-edit-unrelate" class="minBtn" @click="cancelConnect(row)" />
                    </el-tooltip>
                  </template>
                </el-table-column>
              </template>
              <!-- 交付物显示 -->
              <template v-else>
                <el-table-column label="目标名称" prop="name" show-overflow-tooltip>
                  <template v-slot="{ row,$index }">
                    <span v-if="!row.isShowName" class="name">{{ row.name }}</span>
                    <el-input v-if="row.isShowName" v-model="row.name" v-focus placeholder="请填写" @blur="blurFn(row,$index,'isShowName')" @change="editChange(row)" />
                    <el-button v-if="!view&&!row.isShowName" class="buttons" icon="iconfont el-icon-application-edit-outline" type="text" size="small" @click="editFn(row, $index, 'isShowName')" />
                    <template />
                  </template>
                </el-table-column>
                <el-table-column label="类型" prop="type" width="60px">
                  <template v-slot="{ row }">
                    <span>{{ row.type == 'FILE'?'文件':'链接' }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="交付物" prop="link" show-overflow-tooltip>
                  <template v-slot="{ row,$index }">
                    <span v-if="!row.isShowLink" class="name file-name" @click="fileClick(row)">{{ row.type == 'FILE'? row.fileName : row.link }}</span>
                    <el-input v-if="row.isShowLink" v-model="row.link" v-focus placeholder="请填写" @blur="blurFn(row,$index,'isShowLink')" @change="editChange(row,'isShowLink')" />
                    <el-button v-if="!view&&row.type == 'LINK' && !row.isShowLink" class="buttons" icon="iconfont el-icon-application-edit-outline" type="text" size="small" @click="editFn(row, $index, 'isShowLink')" />
                    <vone-upload v-if="!view&&row.type == 'FILE' && !row.isShowLink" ref="uploadFile" :limit="1" :multiple="false" :show-file-list="false" class="buttons" type="text" file-title="" :files-data="[]" @onSuccess="fileSuccess($event,row)" />
                  </template>
                </el-table-column>
                <el-table-column label="文件大小" prop="fileSize" width="80px">
                  <template v-slot="{ row }">
                    <span>{{ row.fileSize |calcSize }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="交付者" prop="deliverer" width="120px">
                  <template v-slot="{ row }">
                    <vone-user-avatar v-if="row.echoMap.deliverer" :avatar-path="row.echoMap.deliverer.avatarPath" :name="row.echoMap.deliverer.name" width="20px" height="20px" />
                    <span v-else class="noSetting"> -- </span>
                  </template>
                </el-table-column>
                <el-table-column v-if="!view" label="操作" width="60px">
                  <template v-slot="{row}">
                    <el-tooltip content="删除" placement="top">
                      <el-button type="text" size="mini" icon="iconfont el-icon-application-delete" class="minBtn" @click="deleteRow(row)" />
                    </el-tooltip>
                  </template>
                </el-table-column>
              </template>
            </el-table>
          </template>
        </el-row>
      </el-form>

      <div slot="footer" style="text-align:right">
        <el-button v-if="!view" @click="onClose">取消</el-button>
        <el-button :loading="saveLoading" type="primary" @click="confirm">{{ view?'关闭':'确定' }}</el-button>
      </div>
    </el-dialog>
    <connectDialog v-if="connectParam.visible" v-bind="connectParam" :visible.sync="connectParam.visible" @success="getWorkItemlist" />
  </div>
</template>

<script>
import storage from 'store'
import { apiAlmFindItemNoPage, apiAlmPlanCancleSprint, apiAlmProjectPlanAdd } from '@/api/vone/project/iteration'
import { apiProjectUserNoPage } from '@/api/vone/project/index'
import { addDeliverables, deleteDeliverables, editDeliverables, getDeliverablesList } from '@/api/vone/project/deliverables'
import { catchErr, download, fileFormatSize } from '@/utils'
import pick from 'lodash/pick'
import connectDialog from './connectDialog'
import { apiBaseFileLoadById } from '@/api/vone/base/file'
export default {
  directives: {
    focus: {
      inserted: function(el) {
        el.querySelector('input').focus()
      }
    }
  },
  filters: {
    calcSize(val) {
      if (!val) return '--'
      return fileFormatSize(val)
    }
  },
  components: {
    connectDialog
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: ''
    },
    view: {
      type: Boolean,
      default: false
    },
    infoData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    var validatePass = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请输入开始时间'))
      } else if (value > this.dialogForm.planEtime) {
        callback(new Error('开始日期不能大于结束日期!'))
      } else {
        callback()
      }
    }
    return {
      pickerOptionsStart: {
        disabledDate: (time) => {
          if (this.dialogForm.planEtime) {
            return time.getTime() > new Date(this.dialogForm.planEtime).getTime()
          }
        }
      },
      pickerOptions: {
        disabledDate: (time) => {
          if (this.dialogForm.planStime) {
            return time.getTime() < new Date(this.dialogForm.planStime).getTime()
          }
        }
      },
      dialogForm: {
        name: '',
        leadingBy: '',
        stateCode: '',
        planStime: '',
        planEtime: '',
        description: ''
      },
      rules: {
        name: [{ required: true, message: '请输入名称' }, { pattern: '^.{1,50}$', message: '请输入不超过50个字符组成的名称' }],
        planStime: [{ validator: validatePass, required: true }],
        planEtime: [{ required: true, message: '请选择完成日期' }],
        leadingBy: [{ required: true, message: '请选择负责人' }],
        stateCode: [{ required: true, message: '请选择状态' }]
      },
      saveLoading: false,
      pUserList: [],
      dialogType: 'PLAN',
      stateMap: {
        '1': '未开始',
        '2': '进行中',
        '3': '已完成'
      },
      stateOption: [
        {
          name: '未开始',
          code: '1',
          color: '#adb0b8'
        }, {
          name: '进行中',
          code: '2',
          color: '#64befa'
        }, {
          name: '已完成',
          code: '3',
          color: '#3cb540'
        }
      ],
      echoMap: {
        leadingBy: null
      },
      isShow: false,
      addLoading: false,
      tableLoading: false,
      addDeliverablesForm: {
        name: '',
        type: ''
      },
      tableData: [],
      connectParam: { visible: false }
    }
  },
  computed: {
    spanCol() {
      return this.dialogType === 'PLAN' ? 6 : 8
    }
  },
  mounted() {
    this.getProjectUser()
    this.dialogForm = { ...this.infoData }
    this.dialogType = this.infoData?.typeCode
    // 截取标签内描述
    this.dialogForm.description = /<[a-zA-Z]+.*?>([\s\S]*?)<\/[a-zA-Z]*?>/g.exec(this.infoData.description)?.[1] || this.infoData.description || ''
    this.dialogType === 'PLAN' ? this.getWorkItemlist() : this.getMilestoneDelives()
    if (this.view) {
      this.dialogForm.planStime = this.infoData.planStime?.split(' ')?.[0] || ''
      this.dialogForm.planEtime = this.infoData.planEtime?.split(' ')?.[0] || ''
    }
  },
  methods: {
    // 查询项目计划关联工作项
    async getWorkItemlist() {
      const params = {
        projectId: this.$route.params.id,
        planId: this.infoData.id

      }
      this.tableLoading = true
      const [{ data, isSuccess, msg }, err] = await catchErr(apiAlmFindItemNoPage(params))
      this.tableLoading = false
      if (err) return
      if (!isSuccess) {
        this.$message.warning(msg)
        return
      }
      this.tableData = data
    },
    // 查询里程碑交付物
    async getMilestoneDelives() {
      const params = {
        planId: this.infoData.id
      }
      this.tableLoading = true
      const [{ data, isSuccess, msg }, err] = await catchErr(getDeliverablesList(params))
      this.tableLoading = false
      if (err) return
      if (!isSuccess) {
        this.$message.warning(msg)
        return
      }
      this.tableData = data
    },
    // 查询项目集下人员
    async getProjectUser() {
      const [res, err] = await catchErr(apiProjectUserNoPage({
        projectId: this.$route.params.id
      }))
      if (err) return
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.echoMap.leadingBy = this.infoData?.echoMap?.leadingBy || {}
      this.pUserList = res.data
    },
    // 获取选中的人员
    getSelectUser(user) {
      const selectd = this.pUserList.find(v => v.id === user)
      this.echoMap.leadingBy = selectd || null
    },
    changeDate(v) {
      if (this.dialogForm.planStime < v) {
        this.$refs['dialogForm'].clearValidate('planStime')
      }
    },
    // 关联工作项
    connectItem() {
      this.connectParam = {
        visible: true,
        infoData: this.infoData
      }
    },
    editFn(row, index, type) {
      row[type] = true
      this.$set(this.tableData, index, row)
    },
    blurFn(row, index, type) {
      row[type] = false
      this.$set(this.tableData, index, row)
    },
    async editChange(row, type) {
      row[type] = false
      this.editMethod(row, type)
    },
    async editMethod(e, type) {
      if (type != 'isShowName') {
        const userInfo = storage.get('user')
        e.deliverer = userInfo.id
      }
      const res = await editDeliverables(e)
      if (res.isSuccess) {
        this.$message.success('修改成功')
        this.getMilestoneDelives()
      }
    },
    async fileSuccess(e, row) {
      if (e.length > 0) {
        row.fileName = e[0].name
        row.fileId = e[0].id
        row.fileSize = e[0].size
      }
      this.editMethod(row)
    },
    async fileClick(row) {
      if (row.type == 'LINK') {
        const link = row.link
        if (link && (row.type.substr(0, 7).toLowerCase() == 'http://' || row.type.substr(0, 8).toLowerCase() == 'https://')) {
          window.open(row.link, '_blank')
        } else {
          window.open('http://' + row.link, '_blank')
        }
        row.type.substr(0, 7).toLowerCase()
      } else if (row.type == 'FILE') {
        try {
          download(row.fileName, await apiBaseFileLoadById([row.fileId]
          ))
          this.tableLoading = false
        } catch (e) {
          this.tableLoading = false
          return
        }
      }
    },
    // 新增里程碑交付物
    addMilestoneDeliver() {
      this.isShow = true
    },
    resetForm() {
      this.isShow = false
      this.$refs.addDeliverablesForm.resetFields()
      this.addDeliverablesForm = {
        name: '',
        type: ''
      }
    },
    async onSubmit() {
      try {
        await this.$refs.addDeliverablesForm.validate()
        this.addDeliverablesForm.planId = this.infoData.id
        this.addLoading = true
        const res = await addDeliverables(this.addDeliverablesForm)
        this.addLoading = false
        if (res.isSuccess) {
          this.$message.success('添加成功')
          this.isShow = false
          this.getMilestoneDelives()
          this.addDeliverablesForm = {
            name: '',
            type: ''
          }
        }
      } catch {
        this.addLoading = false
        return
      }
    },
    async cancelConnect(row) {
      const confirm = await catchErr(this.$confirm('确定要取消关联工作项吗？', '提示', {
        closeOnClickModal: false,
        type: 'warning'
      }))
      if (confirm[1]) return
      const res = await apiAlmPlanCancleSprint([{
        issueId: row.bizId,
        planId: this.infoData.id,
        typeClassify: row.classify?.code,
        typeCode: row.typeCode
      }])
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.$message.success('取消关联成功')
      this.getWorkItemlist()
    },
    // 删除当前项
    async deleteRow(row) {
      const confirm = await catchErr(this.$confirm('删除后不可恢复，确定要删除吗？', '删除', {
        type: 'warning',
        closeOnClickModal: false,
        customClass: 'delConfirm'
      }))
      if (confirm[1]) return
      const [res, err] = await catchErr(deleteDeliverables([row.id]))
      if (err) return
      if (res.isSuccess) {
        this.$message.success('删除成功')
        this.getMilestoneDelives()
      }
    },
    onClose() {
      this.$emit('update:visible', false)
      this.$refs.dialogForm.resetFields()
    },

    async confirm() {
      if (this.view) {
        this.onClose()
        return
      }
      try {
        await this.$refs.dialogForm.validate()
      } catch (error) {
        return
      }
      const params = {
        ...pick(this.dialogForm, ['id', 'projectId', 'name', 'leadingBy', 'stateCode', 'planStime', 'planEtime', 'description']),
        type: this.infoData.typeCode
      }
      this.saveLoading = true
      const [{ isSuccess, msg }, err] = await catchErr(apiAlmProjectPlanAdd(params))
      this.saveLoading = false
      if (err) return

      if (!isSuccess) {
        this.$message.warning(msg)
        return
      }
      this.$message.success('保存成功')
      this.$emit('success')
      this.onClose()
    }
  }
}
</script>
<style lang="scss" scoped>
.backContainer {
  padding: 16px;
  background-color: var(--main-bg-color);
  ::v-deep.desc {
    label {
      line-height: 1;
    }
  }
}
.deliver {
  padding: 6px 0 12px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  span {
    font-weight: 500;
    font-size: 14px;
    color: var(--main-font-color);
  }
  .el-button {
    height: 22px;
  }
}
::v-deep {
  .el-button {
    min-width: 0;
    &--text {
      padding: 0 6px;
    }
  }
  .el-dialog .el-dialog__body {
    padding: 0;

    .backContainer {
      .desc {
        label {
          line-height: 1;
          padding-bottom: 12px;
        }
      }
    }
  }
  .el-dialog .el-dialog__body .el-form-item--small[class~="is-required"] {
    margin-bottom: 0;
  }
  .el-dialog__body {
    .el-form-item__label {
      position: relative;
      font-weight: 500;
      font-size: 14px;
      color: var(--main-font-color);
    }
  }
  // 修改必选位置
  .el-form-item.is-required:not(.is-no-asterisk) > .el-form-item__label:before {
    position: absolute;
    right: -12px;
  }

  .el-table {
    tr.el-table__row {
      height: 34px;
      line-height: 34px;
    }
    th.el-table__cell > .cell {
      height: 34px;
      line-height: 34px;
      padding: 0 12px;
    }
    &::before {
      display: none;
    }

    .name {
      display: inline-block;
      vertical-align: middle;
      width: calc(100% - 34px);
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      line-height: 32px;
    }
    .buttons {
      min-width: 32px;
      display: none;
      float: right;
    }
    .file-name {
      color: #3e7bfa;
      cursor: pointer;
    }
    ::v-deep .upload {
      width: 42px;
      text-align: center;
      .el-button {
        min-width: 32px;
      }
    }
  }
  .el-table__body tr:hover {
    .buttons {
      display: block;
    }
  }
}
.el-dialog__body {
  .addDeliverForm {
    ::v-deep .el-form-item {
      margin-bottom: 10px;
    }
  }
}

.basicHeader {
  background-color: var(--node-cildren-bg-color);
  min-height: 100px;
  padding: 12px 28px;
  border-bottom: 1px solid var(--disabled-bg-color);
  margin: 0 !important;
  &.disabled {
    background-color: #fff;
    .el-col-6,
    .el-col-8 {
      & :hover {
        background: #fff;
      }
    }
  }

  .header {
    font-weight: 600;
    font-size: 16px;
    color: var(--main-font-color);
  }
  .el-col-6,
  .el-col-8 {
    & :hover {
      background: var(--col-hover-bg);
    }

    .fixedItem {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      i,
      .svg-icon {
        font-size: 32px;
      }
    }

    ::v-deep {
      .el-select .el-input.is-focus .el-input__inner {
        background-color: var(--col-hover-bg);
      }
      .el-input--small .el-input__inner {
        border: none;
        background: none;
        & :focus {
          background: var(--main-bg-color);
        }
      }
      .el-input__suffix {
        display: none;
      }
      .el-form-item__label {
        margin-left: 10px;
      }
      .el-input--small {
        font-size: 14px;
      }
    }
  }

  .headerTitle {
    .title {
      font-weight: 600;
      font-size: 16px;
      color: #1d2129;
    }
    ::v-deep .el-input--small .el-input__inner {
      border: none;
      background: none;
      font-weight: bold;
      font-size: 16px;
    }

    ::v-deep .el-input__inner:focus {
      background: var(--col-hover-bg);
    }
    &:hover {
      .el-input {
        background: var(--col-hover-bg);
      }
    }
  }
}
.el-select-dropdown__item {
  display: flex;
  align-items: center;
  gap: 0 8px;
  padding: 0 10px;
}
.minBtn {
  padding: 0;
}
</style>
