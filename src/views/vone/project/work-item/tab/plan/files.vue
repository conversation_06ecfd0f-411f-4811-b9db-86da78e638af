<template>
  <!-- vone-custom-table -->
  <div class="wrapper-box">
    <el-row>
      <el-col :span="4">
        <div class="tree-style">
          <div class="title">
            <span class="title-text">计划</span>
            <span>
              <el-popover v-model="searchPopover" placement="bottom-start" width="400" popper-class="table-search-form" trigger="click">
                <div class="search-main">
                  <el-form ref="searchForm" class="search-form" inline :model="planSearch" label-position="top">
                    <el-row :gutter="20">
                      <el-col :span="12">
                        <el-form-item label="迭代名称" prop="name">
                          <el-input v-model="planSearch.name" placeholder="请输入" />
                        </el-form-item>
                      </el-col>
                      <el-col :span="12">
                        <el-form-item label="负责人" prop="leadingBy">
                          <vone-remote-user v-model="planSearch.leadingBy" />
                        </el-form-item>
                      </el-col>
                      <el-col>
                        <el-form-item label="迭代时间" prop="date">
                          <el-date-picker
                            v-model="planSearch.date"
                            :append-to-body="false"

                            type="daterange"
                            range-separator="至"
                            start-placeholder="开始日期"
                            end-placeholder="结束日期"
                            value-format="yyyy-MM-dd"
                          />
                        </el-form-item>

                      </el-col>
                    </el-row>
                  </el-form>
                  <div class="footerSearch">
                    <el-button plain @click="cancel">重置</el-button>
                    <el-button type="primary" @click="searchData">确定</el-button>
                  </div>
                </div>

                <a slot="reference">
                  <i class="iconfont el-icon-application-filter" size="mini" />
                </a>
              </el-popover>

              <i class="iconfont el-icon-application-step-setting" style="margin-left:12px;cursor: pointer;" size="mini" :disabled="!$permission('project_iteration_add')" @click="confFile" />
            </span>

          </div>
          <el-tree
            ref="treeRef"
            :data="planlist"
            :props="defaultProps"
            node-key="id"
            :indent="22"
            highlight-current
            check-on-click-node
            check-strictly
            @node-click="nodeClick"
            @node-expand="nodeExpand"
            @node-collapse="nodeCollapse"
          >
            <span slot-scope="{ node, data }" class="custom-tree-node father">
              <span style="display: flex;flex: 1;width: 0;" :class="node.childNodes.length == 0 ? 'group-style' : ''">
                <span>
                  <svg class="icon" aria-hidden="true" style="font-size: 16px">
                    <use v-if="data.type.code === 'PLAN'" xlink:href="#el-icon-jihua" />
                    <use v-if="data.type.code === 'MILESTONE'" xlink:href="#el-icon-lichengbei" />
                  </svg>
                </span>
                <span style="margin-left: 6px;" :title="data.name">
                  {{ data.name }}
                </span>
              </span>
              <span class="operation-icon">
                <el-tooltip effect="dark" content="撤销归档" placement="top">
                  <el-button type="text" size="mini" icon="iconfont el-icon-yibiaopan-chexiao" @click.stop="() => callFile(data,node)" />
                </el-tooltip>

              </span>
            </span>
          </el-tree>
        </div>
      </el-col>
      <el-col v-if="isFiledList&&isFiledList.length>0" :span="20">
        <el-form v-if="nodekey != '-1'" ref="iterationForm" disabled label-position="top" :model="iterationForm">
          <el-row class="basicHeader" :gutter="24">
            <el-col :span="24">
              <el-input v-model="iterationForm.name" placeholder="请填写" />
            </el-col>
            <el-col :span="6">
              <div class="fixedItem">
                <span v-if="iterationForm.echoMap && iterationForm.echoMap.leadingBy">
                  <vone-user-avatar :avatar-path="iterationForm.echoMap.leadingBy.avatarPath" :avatar-type="true" :show-name="false" height="42px" width="42px" />
                </span>
                <span v-else>
                  <i class="iconfont el-icon-icon-light-avatar" style="font-size:42px" />
                </span>
                <el-form-item label="处理人" prop="leadingBy">
                  <vone-remote-user v-model="iterationForm.leadingBy" :no-name="false" />
                </el-form-item>

              </div>
            </el-col>
            <el-col :span="6">
              <div class="fixedItem">
                <i class="iconfont el-icon-icon-fill-zhuangtai" :style="{color:iterationForm.stateCode =='1'? '#adb0b8':iterationForm.stateCode=='2'?'#64befa':'#3cb540'}" />
                <el-form-item label="状态" prop="stateCode">
                  <el-select v-model="iterationForm.stateCode" placeholder="请选择">
                    <el-option v-for="i in stateOption" :key="i.id" :label="i.name" :value="i.code">
                      <i style="display:inline-block;width:10px;height:10px;border-radius:50%;" :style="{'color':i.color,border:`2px solid ${i.color}`}" />
                      {{ i.name }}
                    </el-option>
                  </el-select>
                </el-form-item>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="fixedItem">
                <svg-icon icon-class="vone-date" />
                <el-form-item label="开始日期" prop="planStime">
                  <el-date-picker v-model="iterationForm.planStime" type="date" format="yyyy-MM-dd" value-format="yyyy-MM-dd" placeholder="请选择日期" />
                </el-form-item>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="fixedItem">
                <svg-icon icon-class="vone-date" />
                <el-form-item label="完成日期" prop="planEtime">
                  <el-date-picker v-model="iterationForm.planEtime" type="date" format="yyyy-MM-dd" value-format="yyyy-MM-dd" placeholder="请选择日期" />
                </el-form-item>
              </div>
            </el-col>
          </el-row>
        </el-form>
        <div class="table-box">
          <vone-search-wrapper>
        <template slot="search">
          <vone-search-dynamic
              slot="search"
              :project-id="$route.params.id"
              :model.sync="formData"
              label-position="top"
              @getTableData="getConnetedItems"
            >
            <el-row :gutter="20">
                <el-col v-for="item in filterList" :key="item.key" :span="12">
                  <el-form-item :label="item.name" :prop="item.key">
                    <!-- 人员组件 -->
                    <vone-remote-user v-if="item.type == 'user'" v-model="formData[item.key]" multiple />

                    <!-- 输入框 -->
                    <el-input v-else-if="item.type == 'input'" v-model="formData[item.key]" :placeholder="item.placeholder" />

                    <!-- 下拉多选框 -->
                    <el-select v-else-if="item.type == 'select'" v-model="formData[item.key]" :placeholder="item.placeholder" multiple>
                      <el-option v-for="i in item.options" :key="i.id" :label="i.name" :value=" item.key == 'planIds' ? i.id : i.code" />
                    </el-select>

                  </el-form-item>
                </el-col>
              </el-row>
            </vone-search-dynamic>
        </template>
      </vone-search-wrapper>
      <main :style="{'height': nodekey != '-1'? 'calc(100vh - 405px)' : 'calc(100vh - 272px)'}">
          <vxe-table
            ref="connectItemTable"
            class="vone-vxe-table"
            border
            v-loading="loading"
            height="auto"
            auto-resize
            :data="tableData.records"
            @getTableData="getConnetedItems"
          >

            <vxe-column title="名称" field="name" show-overflow-tooltip>
              <template v-slot="{ row }">
                <span>{{ row.name }}</span>
              </template>
            </vxe-column>
            <vxe-column title="状态" field="stateCode" width="100px">
              <template v-slot="{ row }">
                <span v-if="row.echoMap&&row.echoMap.stateCode" class="tagCustom" :style="{color:row.echoMap.stateCode.color,border:'1px solid currentColor'}">{{ row.echoMap.stateCode.name }}</span>
                <span v-else class="status-text">{{ row.stateCode }}</span>
              </template>
            </vxe-column>
            <vxe-column title="优先级" field="priotory" width="100px">
              <template v-slot="{ row }">
                <span v-if="row.echoMap&&row.echoMap.priorityCode"><i :class="['iconfont',row.echoMap.priorityCode.icon]" :style="{color:row.echoMap.priorityCode.color}" />{{ row.echoMap.priorityCode.name }}</span>
                <span v-else>{{ row.priorityCode }}</span>
              </template>
            </vxe-column>
            <vxe-column title="计划完成时间" width="110px">
              <template v-slot="{ row }">
                <span>{{ row.planEtime |format }}</span>
              </template>
            </vxe-column>
            <vxe-column title="处理人" field="handleBy" width="100px">
              <template v-slot="{ row }">
                <span v-if="row.handleBy && row.echoMap && row.echoMap.handleBy">
                  <vone-user-avatar :avatar-path="row.echoMap.handleBy.avatarPath" :name="row.echoMap.handleBy.name" />
                </span>

              </template>
            </vxe-column>
            <vxe-column title="负责人" field="leadingBy" width="100px">
              <template v-slot="{ row }">
                <span v-if="row.leadingBy && row.echoMap && row.echoMap.leadingBy">
                  <vone-user-avatar :avatar-path="row.echoMap.leadingBy.avatarPath" :name="row.echoMap.leadingBy.name" />
                </span>
              </template>
            </vxe-column>
          </vxe-table>
      </main>
      <vone-pagination ref="pagination" :total="tableData.total" @update="getConnetedItems" />
        </div>
      </el-col>
    </el-row>

    <fileDialog v-if="file.visible" :visible.sync="file.visible" v-bind="file" />
  </div>
</template>

<script>
import {
  apiAlmProjectPlanNoPage,
  apiAlmPlanInfo,
  getWorkItemsByPage,
  unarchive
} from '@/api/vone/project/iteration'

// import connectDialog from './connectDialog.vue'
import { catchErr } from '@/utils'
import dayjs from 'dayjs'
import { list2Tree } from '@/utils/list2Tree'
import { gainTreeList } from '@/utils'
import fileDialog from './file-dialog.vue'
export default {
  components: {
    // connectDialog,
    fileDialog
  },
  filters: {
    format(value) {
      return value ? dayjs(value).format('YYYY-MM-DD') : ''
    }
  },
  props: {
    type: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      searchPopover: '',
      planlist: [],
      loading: false,
      listLoading: false,
      defaultProps: {
        children: 'children',
        label: 'name'
      },
      iterationForm: {},
      stateOption: [
        {
          name: '未开始',
          code: '1',
          color: '#adb0b8'
        }, {
          name: '进行中',
          code: '2',
          color: '#64befa'
        }, {
          name: '已完成',
          code: '3',
          color: '#3cb540'
        }, {
          name: '已归档',
          code: '4',
          color: '#f56c6c'
        }
      ],
      tableOptions: {},
      tableData: { records: [] },
      filterList: [
        {
          name: '名称',
          key: 'name',
          type: 'input',
          placeholder: '请输入名称'
        },
        {
          name: '处理人',
          key: 'handleBy',
          type: 'user'
        }
      ],
      formData: {
        name: '',
        handleBy: []
      },
      planSearch: {},
      file: { visible: false },
      nodekey: '',
      createParam: {
        visible: false
      },
      isFiledList: [],
      selected: [],
      associatedPlanForm: {},
      associatedPlanVisible: false,
      saveLoading: false,
      newPlanList: []
    }
  },
  computed: {},
  watch: {},
  created() {
    this.getPlanTree()
    this.$nextTick(() => {
      this.getConnetedItems()
    })
  },
  mounted() {

  },
  methods: {
    nodeClick(data) {
      this.nodekey = data.id
      this.getPlanInfo(data.id)
      this.getConnetedItems()
    },
    nodeExpand() {

    },
    nodeCollapse() {

    },
    // 筛选
    searchData() {
      this.getPlanTree()
    },
    // 重置
    cancel() {
      this.planSearch = {}
      this.getPlanTree()
    },
    // 配置归档
    confFile() {
      this.file = { visible: true }
    },
    async callFile(val) {
      try {
        await this.$confirm('确定撤销归档吗？', '提示', {
          type: 'warning'
        })
      } catch (e) {
        return
      }
      const res = await unarchive(val.id)
      if (res.isSuccess) {
        this.$message.success('撤销成功')

        this.getPlanTree()
      }
    },

    onClose() {
      this.$refs.associatedPlanForm.resetFields()
      this.associatedPlanVisible = false
    },

    onSelectionChange(selection) {
      this.selected = selection
    },
    // 查询项目已关联工作项
    async getConnetedItems() {
      if (!this.nodekey) return
      const tableAttr = this.$refs?.pagination?.pageObj || { current: 1, size: 20 }
      const params = {
        ...tableAttr,
        extra: {
            ...this.extraData
          },
        model: {
          projectId: this.$route.params.id,
          planId: this.nodekey
        }
      }
      this.loading = true
      const [{ data, isSuccess, msg }, err] = await catchErr(getWorkItemsByPage(params))
      this.loading = false
      if (err) return
      if (!isSuccess) {
        this.$message.warning(msg)
        return
      }
      this.tableData = data
    },
    //  查询计划详情
    async getPlanInfo(e) {
      this.formLoading = true
      const res = await apiAlmPlanInfo(e)
      this.formLoading = false
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.iterationForm = res.data
    },
    // 查询计划树
    async getPlanTree() {
      this.listLoading = true
      const res = await apiAlmProjectPlanNoPage({
        projectId: this.$route.params.id,
        isFiled: true,
        planEtime: this.planSearch.date ? this.planSearch.date[1] : '',
        planStime: this.planSearch?.date ? this.planSearch?.date[0] : '',
        name: this.planSearch.name,
        leadingBy: this.planSearch.leadingBy
      })
      this.listLoading = false
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.isFiledList = res.data || []
      this.planlist = list2Tree(res.data, { parentKey: 'parentId' })
      const stoneTree = gainTreeList(this.planlist)
      this.newPlanList = stoneTree.filter(r => r.id != -1)
      if (this.newPlanList.length == 0) return
      this.nodekey = this.newPlanList[0].id
      this.$nextTick(() => {
        this.$refs['treeRef'].setCurrentKey(this.nodekey)
      })
      this.nodeClick(this.newPlanList[0])
    }
  }
}
</script>
<style lang='scss' scoped>
.wrapper-box {
  border: 1px solid var(--el-divider);
  border-radius: 4px;
  height: calc(100vh - 162px);
  // overflow: hidden;
}
.footerSearch {
  text-align: right;
  padding: 12px 16px;
  border-top: 1px solid var(--disabled-bg-color);
}
.tree-style {
  height: 100%;
  overflow-y: hidden;
  background: #ffff;
  border-right: 1px solid var(--el-divider);
  .title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 56px;
    border-bottom: 1px solid #ebeef5;
    padding: 12px 16px;
    font-size: 16px;
  }

  .title-text {
    line-height: 56px;
    font-weight: 600;
    color: #202124;
  }
  .add-style {
    font-size: 17.5px;
    color: #3e7bfa;
    cursor: pointer;
  }
  .not-planned {
    padding: 0 16px;
    height: 36px;
    line-height: 36px;
    &:hover {
      background-color: var(--hover-bg-color, #f5f6fa);
    }
    cursor:pointer;
    .not-planned-btn {
      width: 100%;
      line-height: 36px;
      border-bottom: 1px solid #F2F3F5;
    }
  }
}
.not-planned-select {
  background-color: var(--hover-bg-color, #f5f6fa);
}
.el-tree {
  height: calc(100vh - 220px);
  padding: 6px 0 16px 0;
  overflow-y: auto;
  overflow-x: hidden;
  ::v-deep .el-tree-node__content {
    height: 34px;
    color: #202124;
    .el-tree-node__expand-icon {
      color: var(--main-second-color);
      margin-left: 16px;
      padding: 0;
      font-size: 16px;
      padding-right: 6px;
    }
    .is-leaf {
      color: transparent;
      cursor: default;
      margin-left: 0;
      padding: 6px 0;
    }
    .el-button {
      padding: 0;
    }
  }
}
.el-tree-node__content {
  .operation-icon {
    opacity: 0;
    flex: 1;
    position: absolute;
    right: 0;
    padding-left: 5px;
    .is-disabled {
      color: var(--placeholder-color);
    }
  }
  &:hover {
    .operation-icon {
      opacity: 1;
      padding-left: 5px;
      position: absolute;
      right: 10px;
      background-color: var(--hover-bg-color, #f5f6fa);
      outline: none;
    }
  }
}
.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 16px;
}

.custom-tree-node-list {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding: 0 16px 0 1px;
}
::v-deep.el-tree--highlight-current .el-tree-node.is-current {
  .el-tree-node__content {
    background-color: #f5f6fa;
  }
}
.group-style {
  margin-left: 22px;
}
.basicHeader {
  background-color: var(--node-cildren-bg-color);
  min-height: 100px;
  padding: 18px;
  border-bottom: 1px solid var(--disabled-bg-color);
  margin:0 !important;
  .el-col-6 {
    & :hover {
      background: var(--col-hover-bg);
    }

    ::v-deep .el-select .el-input.is-focus .el-input__inner {
      background-color: var(--col-hover-bg);
    }

    .fixedItem {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      padding-left: 10px;
      i,
      .svg-icon {
        font-size: 42px;
        margin-right: 10px;
      }
    }
    ::v-deep .el-input--small .el-input__inner {
      border: none;
      background: none;
      & :focus {
        background: var(--main-bg-color);
      }
    }
    ::v-deep .el-input__suffix {
      display: none;
    }
    ::v-deep .el-form-item__label {

      margin-left: 10px;
    }
    ::v-deep .el-input--small {
      font-size: 14px;
    }

    ::v-deep .el-input.is-disabled .el-input__inner {
      color: #000 !important;
      font-size: 14px;
    }
    ::v-deep .el-date-editor.el-input {
      width: 100%;
    }
    ::v-deep .el-form-item {
      margin-bottom: 0;
    }
  }

  .el-col-24 {
    ::v-deep .el-input--small .el-input__inner {
      border: none;
      background: none;
      font-weight: bold;
      font-size: 16px;
    }

    ::v-deep .el-input__inner:focus {
      background: var(--col-hover-bg);
    }
    & :hover {
      background: var(--col-hover-bg);
    }
  }
}
::v-deep .table-operation-view {
  margin: 0 -16px 16px;
  padding: 17px 16px;
}
.table-box {
  padding:16px;
}
</style>
