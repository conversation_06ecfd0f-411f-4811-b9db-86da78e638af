<template>
  <div>

    <!-- <div class="vone-tabs">
      <el-tabs v-model="showTab">
        <el-tab-pane label="测试用例树" name="testTree" />
        <el-tab-pane label="测试导图" name="testG6" />
      </el-tabs>
    </div> -->
    <template v-if="showTab == 'testTree'">
      <detail v-if="libraryId && !showDialog" class="testtree" :library-id="libraryId" style="border-radius: 4px;margin-left:0;margin-right:0;" source="testTree" @triggerRecord="triggerRecord" @triggerVersion="triggerVersion" />
      <caseRecords v-if="recordVisible" :record="record" @triggerRecord="triggerRecord" />
      <caseVersion v-if="versionVisible" :record="record" @triggerVersion="triggerVersion" />
    </template>
    <Graph v-else-if="libraryId" :graph-data="GraphData" :library-id="libraryId" />

  </div>
</template>

<script>
import storage from 'store'
import { list2Tree } from '@/utils/list2Tree'

import { getCaseTree } from '@/api/vone/testmanage'
import { getLibraryIdByProjectId } from '@/api/vone/testmanage'

import Graph from './Graph'
import detail from '@/views/vone/test/use-case/case-tree/index.vue'
import caseRecords from './case-records.vue'
import caseVersion from './case-version.vue'

export default {
  components: {
    detail,
    Graph,
    caseRecords,
    caseVersion
  },
  data() {
    return {
      libraryId: '',
      showTab: 'testTree',
      GraphData: {},
      recordVisible: false,
      versionVisible: false,
      record: {}
    }
  },
  computed: {
    loginUser() {
      return storage.get('user')
    },
    showDialog() {
      return this.recordVisible || this.versionVisible
    }
  },
  created() {
    this.getLibraryList()
  },
  methods: {
    //  查询当前项目下用例库
    async getLibraryList() {
      const { id } = this.$route.params
      const params = {
        projectId: id
      }

      const res = await getLibraryIdByProjectId(params)
      if (res.isSuccess) {
        this.libraryId = res.data
        getCaseTree(res.data).then(res1 => {
          if (res1.isSuccess) {
            const data = list2Tree(res1.data, { parentKey: 'parentId' })
            this.GraphData = data[0]
          }
        })
      }
    },
    triggerRecord({ visible, row }) {
      this.recordVisible = visible
      this.record = row || {}
    },
    triggerVersion({ visible, row }) {
      this.versionVisible = visible
      this.record = row || {}
    }
  }
}
</script>
<style lang='scss' scoped>
.testtree {
  min-height: calc(100vh - 128px);
  height: calc(100vh - 128px);
  ::v-deep .leftSection {
    height: calc(100vh - 128px) !important;
  }
  ::v-deep .rightSection {
    height: calc(100vh - 128px) !important;
  }
}
</style>
