
<template>
  <div>
    <page-wrapper v-if="data" id="graph" ref="graph" class="graph" style="height: calc(100vh - 134px)" />
    <vone-empty v-else />
  </div>
</template>
<script>
import { getCaseList } from '@/api/vone/testmanage/case'
import G6 from '@antv/g6'
import customNode from './registerNode'
import customEdge from './registerEdge'
export default {
  name: 'Graph',
  props: {
    graphData: {
      type: Object,
      default: () => { }
    },
    libraryId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      data: {},
      graph: null,
      container: null
    }
  },
  watch: {
    // graphData(val) {
    //   if (val) {
    //     this.data = val
    //     // this.graph.changeData(val)
    //     // this.graph.fitView()
    //     // 自定义节点
    //     customNode.init()
    //     // 自定义边
    //     customEdge.init()
    //     this.initGraph(this.data)
    //     window.addEventListener('resize', this.handleWindowResize)
    //   }
    // }
  },
  async mounted() {
    this.data = this.graphData
    if (!this.data) return
    // this.graph.changeData(val)
    // this.graph.fitView()
    // 自定义节点
    customNode.init()
    // 自定义边
    customEdge.init()
    this.initGraph(this.data)
    window.addEventListener('resize', this.handleWindowResize)
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.handleWindowResize)
    this.graph = null
    this.container = null
  },
  methods: {
    initGraph(data) {
      const container = this.$refs.graph.$el
      const width = container.clientWidth
      const height = container.clientHeight
      const BaseConfig = {
        nameFontSize: 12,
        childCountWidth: 22,
        countMarginLeft: 0,
        itemPadding: 16,
        nameMarginLeft: 4,
        rootPadding: 18
      }
      const graph = new G6.TreeGraph({
        container: container,
        width,
        height,
        modes: {
          default: [
            {
              type: 'collapse-expand'
            },
            'drag-canvas',
            'zoom-canvas'
          ]
        },
        defaultNode: {
          type: 'treeNode',
          anchorPoints: [
            [0, 0.5],
            [1, 0.5]
          ]
        },
        defaultEdge: {
          type: 'smooth'
        },
        layout: {
          type: 'mindmap',
          direction: 'LR',
          getId: function getId(d) {
            return d.id
          },
          getHeight: function getHeight() {
            return 10
          },
          getWidth: function getWidth(d) {
            const labelWidth = G6.Util.getTextSize(d.label || d.name || '', BaseConfig.nameFontSize)[0]

            return labelWidth
          },
          getVGap: function getVGap() {
            return 20
          },
          getHGap: function getHGap() {
            return 50
          }
        }
      })

      graph.data(data)
      graph.render()
      graph.fitView()
      graph.on('node:click', function(evt) {
        const item = evt.item
        const nodeId = item.get('id')
        if (nodeId.includes('node')) return
        const params = {
          libraryId: this.libraryId,
          treeId: nodeId
        }
        const model = item.getModel()
        const children = model.children
        if (item._cfg.model.stepType == 'text') {
          return
        }
        getCaseList(params).then(res => {
          if (res.isSuccess) {
            if (!children || children.length === 0) {
              res.data.map((item) => {
                if (item.stepType == 'subclause') {
                  const arry = JSON.parse(item.testStep)
                  item.children = []
                  arry.map((items) => {
                    item.children.push({
                      testStep: items.caseStepDes + '&' + items.expectResult,
                      stepType: 'testStep',
                      caseKey: 'text'
                    })
                  })
                } else if (item.testStep) {
                  item.name = item.name + (item.prerequisite ? '&' + item.prerequisite : '')
                  item.children = []
                  item.children.push({
                    testStep: item.testStep,
                    stepType: 'testStep',
                    caseKey: 'text'
                  })
                }
              })
              const childData = res.data
              const parentData = graph.findDataById(nodeId)

              // 如果childData是一个数组，则直接赋值给parentData.children
              // 如果是一个对象，则使用parentData.children.push(obj)
              parentData.children = childData || []
              graph.changeData()
            }
          } else {
            this.$message.warning(res.msg)
          }
        })
      })
    },
    handleWindowResize() {
      if (typeof window !== 'undefined') {
        window.onresize = () => {
          if (!this.graph || this.graph.get('destroyed')) return
          if (!this.container || !this.container.scrollWidth || !this.container.scrollHeight) return
          this.graph.changeSize(this.container.scrollWidth, this.container.scrollHeight)
        }
      }
    }
  }
}
</script>
<style lang="scss" scoped>
#graph {
  min-height: calc(100vh - 180px);
}
</style>
