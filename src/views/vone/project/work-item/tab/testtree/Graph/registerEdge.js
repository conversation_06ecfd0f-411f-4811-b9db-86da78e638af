import G6 from '@antv/g6'
const customNode = {
  init() {
    G6.registerEdge('smooth', {
      draw(cfg, group) {
        const { startPoint, endPoint } = cfg
        const hgap = Math.abs(endPoint.x - startPoint.x)

        const path = [
          ['M', startPoint.x, startPoint.y],
          [
            'C',
            startPoint.x + hgap / 4,
            startPoint.y,
            endPoint.x - hgap / 2,
            endPoint.y,
            endPoint.x,
            endPoint.y
          ]
        ]

        const shape = group.addShape('path', {
          attrs: {
            stroke: '#ebeef5',
            path
          },
          name: 'smooth-path-shape'
        })
        return shape
      }
    })
  }
}
export default customNode
