<template>
  <el-dialog title="范围维护" width="70%" :visible="spaceVisible" :close-on-click-modal="false" :before-close="onClose">
    <div class="content">
      <div class="content-left" />
      <div class="content-right">
        2
      </div>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button @click="onClose">取消</el-button>
      <el-button type="primary" :loading="saveLoading" @click="onsave">确定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import _ from 'lodash'
export default {
  props: {
    spaceVisible: {
      type: Boolean,
      default: false
    },
    row: {
      type: Object,
      default: () => { }
    },
    prioritList: {
      type: Object,
      default: () => []
    }
  },
  data() {
    return {
      formData: {
        name: '',
        typeId: '',
        description: '',
        icon: ''
      },
      rules: {
        name: [
          { required: true, message: '请输入名称' }
        ],
        code: [{ required: true, message: '请输入标识' }]
      },
      saveLoading: false,
      groupData: []
    }
  },
  mounted() {

  },
  methods: {
    onsave() {

    },
    onClose() {
      this.$emit('update:spaceVisible', false)
      this.$refs['formData'].resetFields()
    }
  }
}
</script>

<style lang="scss" scoped>
.content {
  height: calc(100vh - 300px);
  display: flex;
  justify-content: space-between;
  .content-left {
    width: 250px;
    height: 100%;
    border: 1px solid #eee;
    border-right: 0;
  }
  .content-right {
    flex: 1;
    height: 100%;
    border: 1px solid #eee;
  }
}
</style>
