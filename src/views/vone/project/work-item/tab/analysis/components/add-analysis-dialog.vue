<template>
  <el-dialog :title="title" :visible="spaceVisible" :close-on-click-modal="false" :before-close="onClose">
    <el-form ref="formData" :model="formData" label-position="top" label-width="100px" class="demo-ruleForm">
      <el-row>
        <el-col v-for="item in formFields" :key="item.key" :span="item.field_type == 'TEXTAREA' ? 24 : 12">
          <el-form-item
            :label="item.display_name"
            :prop="item.key"
            :rules="{
              required: item.required, message: `${item.display_name}不能为空`, trigger: 'change'
            }"
          >
            <el-input v-if="item.field_type == 'TEXT'" v-model="formData[item.key]" placeholder="请输入" />
            <el-input v-if="item.field_type == 'TEXTAREA'" v-model="formData[item.key]" type="textarea" placeholder="请输入" />
            <template v-else-if="item.field_type == 'SELECT'||item.field_type == 'MULTI-SELECT'">
              <el-select v-if="item.key == 'priority'" v-model="formData[item.key]" placeholder="请选择优先级" filterable>
                <el-option v-for="item in prioritList" :key="item.id" :label="item.name" :value="item.code" />
              </el-select>
              <el-select v-else v-model="formData[item.key]" :placeholder="`请选择${item.display_name}`" :collapse-tags="item.field_type == 'MULTI-SELECT'" :multiple="item.field_type == 'MULTI-SELECT'">
                <el-option v-for="option in item.options" :key="option.value" :label="option.text" :value="option.value" />
              </el-select>
            </template>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="onClose">取消</el-button>
      <el-button type="primary" :loading="saveLoading" @click="onsave">确定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import _ from 'lodash'
import { addTestPoints, editTestPoints } from '@/api/vone/project/workitem'

export default {
  props: {
    spaceVisible: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: ''
    },
    row: {
      type: Object,
      default: () => { }
    },
    functionId: {
      type: String,
      default: ''
    },
    prioritList: {
      type: Array,
      default: () => []
    },
    formFields: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      formData: {
        functionPoint: '',
        testPoints: '',
        ruleType: [],
        involveAccount: '',
        involveBatch: '',
        priority: '',
        ruleSource: ''
      },
      rules: {
        functionPoint: [
          { required: true, message: '请输入名称' }
        ],
        ruleType: [{ required: true, message: '请选择规则类型' }]
      },
      saveLoading: false,
      groupData: []
    }
  },
  mounted() {
    if (this.prioritList) {
      console.log(this.prioritList)
    }
    if (this.row && this.formFields) {
      console.log(this.formFields)
      this.formData = _.cloneDeep(this.row)
      this.formFields.map(e => {
        if (e.field_type == 'MULTI-SELECT') {
          console.log(this.formData[e.key].split(','))
          this.formData[e.key] = this.formData[e.key].split(',')
        }
      })
    }
  },
  methods: {
    async onsave() {
      try {
        await this.$refs.formData.validate()
      } catch (e) {
        return
      }
      this.loading = true
      const routeParams = this.$route.params
      const params = {
        issueTestReqId: routeParams.workId,
        functionId: this.functionId,
        ...this.formData
      }
      params.ruleType = params.ruleType.join(',')
      if (this.title == '新增测试点') {
        addTestPoints(params).then(async(res) => {
          this.loading = false
          if (res.isSuccess) {
            this.$message.success('保存成功')
            this.$emit('success')
            this.onClose()
          } else {
            this.$message.warning(res.msg)
          }
        })
      } else {
        editTestPoints(params).then(async(res) => {
          this.loading = false
          if (res.isSuccess) {
            this.$message.success('修改成功')
            this.$emit('success')
            this.onClose()
          } else {
            this.$message.warning(res.msg)
          }
        })
      }
    },
    onClose() {
      this.$emit('update:spaceVisible', false)
      this.$refs['formData'].resetFields()
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
