<template>
  <div>
    <div v-if="types.length" class="pageContentNoH topTab">
      <div class="menu">
        <div class="menu-title">
          <span>
            <i
              v-if="
                issueInfo.typeCode &&
                  issueInfo.echoMap &&
                  issueInfo.echoMap.typeCode
              "
              :class="`iconfont ${issueInfo.echoMap.typeCode.icon} custom-title-style-icon `"
              :style="{
                color: `${
                  issueInfo.echoMap.typeCode
                    ? issueInfo.echoMap.typeCode.color
                    : '#ccc'
                }`,
              }"
            />
          </span>

          <span class="text-over"> {{ issueInfo.name }} </span>

          <i v-if="!iconFlag" class="iconfont el-icon-direction-down" />
          <i v-else class="iconfont el-icon-direction-up" />
        </div>
        <el-select
          ref="select"
          v-model="issueId"
          filterable
          placeholder="切换需求"
          @change="issueChange"
          @visible-change="(v) => visibleChange(v, 'select', selectClick)"
        >
          <el-option
            v-for="item in issueList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          >
            <div>
              <span>
                <i
                  v-if="item.typeCode && item.echoMap && item.echoMap.typeCode"
                  :class="`iconfont ${item.echoMap.typeCode.icon} custom-title-style-icon `"
                  :style="{
                    color: `${
                      item.echoMap.typeCode
                        ? item.echoMap.typeCode.color
                        : '#ccc'
                    }`,
                  }"
                />
              </span>

              &nbsp;&nbsp;
              <span>{{ item.name }}</span>
            </div>
          </el-option>
        </el-select>
        <span class="solid" />
      </div>
      <el-tabs v-model="tabActive" class="vone-tab-line">
        <el-tab-pane
          v-for="item in types"
          :key="item.type"
          :label="item.name"
          :name="item.type"
        />
      </el-tabs>
    </div>
    <template v-for="item in types">
      <config-table
        v-if="tabActive === item.type"
        :key="item.type"
        :type="item.type"
        :name="item.name"
        :work-id="issueId"
      />
    </template>
    <div v-if="!types.length" class="empty">
      <vone-empty desc="请配置相关角色权限" />
    </div>
  </div>
</template>

<script>
import configTable from './tab/config'
import storage from 'store'

export default {
  components: {
    configTable
  },
  data() {
    return {
      tabActive: 'overview',
      types: [
        {
          type: 'overview',
          name: '概览',
          show: this.$permission('project_work_item_overview') || this.$permission('project_work_item_testreq_overview')
        },
        {
          type: 'planTask',
          name: '测试计划',
          show: this.$permission('project_work_item_plan_task') || this.$permission('project_work_item_test_testreq_plantask')
        },
        {
          type: 'task',
          name: '任务',
          show: this.$permission('project_work_item_task') || this.$permission('project_work_item_testreq_task')
        },
        {
          type: 'testPlan',
          name: '测试方案',
          show: this.$permission('project_work_item_test_plan') || this.$permission('project_work_item_testreq_plan')
        },
        {
          type: 'analysis',
          name: '测试分析',
          show: this.$permission('project_work_item_test_analysis') || this.$permission('project_work_item_test_testreq_analysis')
        },
        {
          type: 'case',
          name: '测试用例',
          show: this.$permission('project_work_item_test_case') || this.$permission('project_work_item_test_testreq_case')
        },
        {
          type: 'execution',
          name: '测试执行',
          show: this.$permission('project_work_item_test_execution') || this.$permission('project_work_item_test_testreq_execution')
        },
        {
          type: 'defect',
          name: '缺陷',
          show: this.$permission('project_work_item_test_defect') || this.$permission('project_work_item_test_testreq_defect')
        },
        {
          type: 'report',
          name: '测试报告',
          show: this.$permission('project_work_item_test_report') || this.$permission('project_work_item_test_testreq_report')
        }
      ],
      iconFlag: false,
      issueInfo: {},
      searchTitle: '查看全部需求',
      issueList: [],
      issueId: this.$route.params.workId,
      workItemType: ''
    }
  },
  watch: {
    $route: function(e) {
      this.issueId = e.params.workId
      this.tabActive = 'overview'
    }
  },
  created() {
    console.log(this.$route)
    this.workItemType = this.$route.meta.activeApp
    if (this.workItemType == 'project') {
      this.types = this.types.filter((item) => item.show).sort((a, b) => a.sort - b.sort)
    } else {
      this.types = [
        {
          type: 'overview',
          name: '概览'
        },
        {
          type: 'require',
          name: '需求'
        },
        {
          type: 'requireTestCase',
          name: '测试用例'
        }
      ]
    }

    this.tabActive = this.types[0]?.type
  },
  mounted() {
    const customInfo = storage.get('customInfo')
      ? JSON.parse(storage.get('customInfo'))
      : {}
    this.issueList = customInfo.tableList || []
    if (this.issueList && this.issueList.length > 0) {
      this.issueInfo = this.issueList.find((i) => i.id == this.issueId)
    }
  },
  methods: {
    issueChange(e) {
      const code = this.$route.path.split('/')[2]
      this.issueInfo = this.issueList.find((i) => i.id == e)
      const typeCodes = code == 'issue' ? 'ISSUE' : 'TESTREQ'
      const issueInfoParam = {
        visible: true, // 是否显示弹框。目前改造成路由，不需要了
        title: '需求详情', // 页面标题
        id: this.issueInfo.id, // issue id
        key: Date.now(),
        infoDisabled: true, // form表单是否可以编辑
        sprintId: undefined, // 暂时无用
        typeCode: typeCodes, // ISSUE
        tableList: this.tableList, // 需求列表，用来切换上一个和下一个
        leftTabs: this.leftTabs || [], // ISSUE
        rightTabs: this.rightTabs || [], // ISSUE
        formId: undefined, // 平台配置,预览表单接口,需要根据formId查询模板
        rowTypeCode: this.issueInfo.typeCode, // 需求中心/项目里,从列表数据取到的类型
        stateCode: this.issueInfo.stateCode, // 需求中心/项目里,从列表数据取到的状态
        rowProjectId: undefined, // 需求中心,从列表数据取到的关联项目的项目id
        showPopupForSplit: true, // 拆分子项是否弹框
        isShowCreateBtn: true // 是否显示新建按钮
      }
      storage.set('customInfo', JSON.stringify(issueInfoParam))
      if (this.workItemType == 'project') {
        const params = this.$route.params || {}
        this.$router.push(
          `/project/${code}/${params.projectKey}/${params.projectTypeCode}/${params.id}/${this.issueInfo.id}`
        )
      } else if (this.workItemType == 'reqm_center') {
        this.$router.push(
          `/reqmcenter/testrequire/details/${this.issueInfo.id}`
        )
      }
    },
    selectClick() {
      const code = this.$route.path.split('/')[2]
      this.$router.push({
        name: code == 'issue' ? 'project_issue' : 'project_testreq'
      })
    },
    visibleChange(visible, refName, onClick) {
      this.iconFlag = visible
      if (visible) {
        const ref = this.$refs[refName]
        let popper = ref.$refs.popper
        if (popper.$el) popper = popper.$el
        if (
          !Array.from(popper.children).some(
            (v) => v.className === 'el-cascader-menu__list'
          )
        ) {
          const el = document.createElement('ul')
          el.className = 'el-cascader-menu__list'
          el.style =
            'border-top: solid 1px #e4e7ed; padding:0; color: #606266;'
          el.innerHTML =
            `<li class="el-cascader-node color-info" style="height: 38px; line-height: 38px">
            <span class="el-cascader-node__label">` +
            this.searchTitle +
            `</span>
            <i class="el-icon-arrow-right el-cascader-node__postfix"/>
            </li>`
          popper.appendChild(el)
          el.onclick = () => {
            onClick && onClick()
          }
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@import "~@/styles/variables.scss";

.topTab {
  margin-bottom: 10px;
  height: 48px;
  display: flex;
}
.empty {
  height: calc(100vh - 100px);
  display: flex;
  justify-content: center;
  align-items: center;
}
.menu {
  position: relative;
  display: flex;
  align-items: center;
  height: $nav-top-height;
  margin-left: 5px;
  .menu-title {
    max-width: 214px;
    padding: 0 8px;
    display: flex;
    align-items: center;
    flex: 1;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    span {
      margin-right: 4px;
    }
    .iconfont {
      margin-left: 6px;
      color: var(--font-second-color);
    }
    .text-over {
      min-width: 56rem;
    }
  }
  .el-select {
    opacity: 0;
    position: absolute;
    top: 6px;
    left: 0;
    z-index: 99;
    cursor: pointer;
  }
  i {
    font-weight: normal;
    &.project-icon {
      font-size: 18px;
    }
  }
}
</style>
