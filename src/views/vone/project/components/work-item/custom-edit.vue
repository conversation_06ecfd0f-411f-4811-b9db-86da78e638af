<template>
  <div class="custom-edit">
    <el-card class="box-card">
      <!-- 编辑 -->
      <div class="drawerBox">
        <div>
          {{ title }}【
          <span class="drawerCode">
            {{ fixedForm.code }}
          </span>
          】
          <span v-if="!hidePrv">
            <i class="iconfont el-icon-yibiaopan-shangyi nextBtn" @click="dataPrev" />
            <i class="iconfont el-icon-yibiaopan-xiayi nextBtn" @click="dataNext" />
          </span>
        </div>

        <div>
          <el-form ref="fixedForm" :model="fixedForm" :rules="fixdFormRules" label-position="top">
            <div class="basicHeader">
              <el-skeleton :loading="drawerLoading" style="width: 100%" animated>
                <template slot="template">
                  <el-skeleton-item variant="p" style="width: 30%; margin-bottom: 14px" />
                  <div style="
                      display: flex;
                      align-items: center;
                      justify-content: space-between;
                    ">
                    <div v-for="index in 5" :key="index" style="flex: 1; display: flex; align-items: center">
                      <el-skeleton-item variant="image" style="width: 30px; height: 30px" />
                      <div style="width: 190px; margin-left: 10px">
                        <el-skeleton-item variant="p" style="width: 50%; margin-bottom: 6px" />
                        <el-skeleton-item variant="p" style="width: 70%" />
                      </div>
                    </div>
                  </div>
                </template>
              </el-skeleton>
              <div v-if="!drawerLoading">
                <el-row v-for="item in fixedProperty.filter((r) => r.key == 'name')" :key="item.id" class="name-row">
                  <el-col :span="24">
                    <!-- 输入框 -->
                    <el-input v-model="fixedForm[item.key]" :placeholder="item.placeholder" :disabled="!item.isUpdate" />
                  </el-col>
                </el-row>

                <div class="basica-form">
                  <div v-for="item in fixedProperty.filter((r) => r.key != 'name')" :key="item.id" class="fixedItem" :class="{ fixedItemType: item.key == 'typeCode' }">
                    <!-- 表单项 -->
                    <el-form-item :label="item.name" :prop="item.key">
                      <!-- 图标 -->
                      <span v-if="item.key == 'typeCode'">
                        <span v-if="
                            fixedForm.echoMap && fixedForm.echoMap[item.key]
                          ">
                          <i :style="{
                              color: fixedForm.echoMap[item.key].color,
                            }" :class="[
                              'iconfont',
                              `${fixedForm.echoMap[item.key].icon}`,
                            ]" style="font-size: 24px" />
                        </span>
                        <span v-else>
                          <svg class="icon" aria-hidden="true" style="font-size: 24px">
                            <use :xlink:href="`#el-icon-icon-yixiang`" />
                          </svg>
                        </span>
                      </span>

                      <span v-else-if="item.key == 'handleBy'">
                        <span v-if="
                            fixedForm.echoMap && fixedForm.echoMap[item.key]
                          ">
                          <vone-user-avatar :avatar-path="
                              fixedForm.echoMap[item.key].avatarPath
                            " :avatar-type="true" :show-name="false" height="24px" width="24px" />
                        </span>
                        <span v-else>
                          <i class="iconfont el-icon-icon-light-avatar" style="font-size: 24px" />
                        </span>
                      </span>

                      <span v-else>
                        <svg class="icon" aria-hidden="true" style="font-size: 24px">
                          <use :xlink:href="`#${iconMap[item.key]}`" />
                        </svg>
                      </span>
                      <!-- 人员组件 -->
                      <vone-remote-user v-if="item.type == 'USER' || item.type == 'PROJECTUSER'" v-model="fixedForm[item.key]" :project-id="
                          item.key == 'leadingBy' ||
                            item.key == 'putBy' ||
                            item.key == 'handleBy'
                            ? projectId
                            : ''
                        " :disabled="!item.isUpdate" :no-name="false" :multiple="item.multiple" />
                      <!-- 标签 -->
                      <tagSelect v-else-if="item.type == 'SELECT' && item.key == 'tagId'" v-model="fixedForm[item.key]" multiple collapse-tags />

                      <!-- 输入框 -->
                      <el-input v-else-if="item.type == 'INPUT'" v-model="fixedForm[item.key]" :placeholder="item.placeholder" :disabled="!item.isUpdate" />

                      <!-- 状态 -->
                      <span v-else-if="item.key == 'stateCode'">
                        <StateCode v-if="fixedForm[item.key]" :code="fixedForm[item.key]" :target-id="id" :form="fixedForm" @changeFlow="editStatus(fixedForm[item.key])" />
                      </span>
                      <!-- 下拉框 -->
                      <el-select v-else-if="
                          item.type == 'SELECT' && item.key != 'stateCode'
                        " v-model="fixedForm[item.key]" :placeholder="item.placeholder" :disabled="!item.isUpdate || item.key === 'typeCode'">
                        <el-option v-for="i in item.options" :key="i.id" :label="i.name" :value="item.key == 'planId' ? i.id : i.code">
                          <span v-if="item.key == 'typeCode'">
                            <i :class="['iconfont', `${i.icon}`]" :style="{ color: i.color }" />
                            {{ i.name }}
                          </span>
                        </el-option>
                      </el-select>
                      <!-- 日期组件 -->
                      <el-date-picker v-else-if="item.type == 'DATE'" v-model="fixedForm[item.key]" class="dataPicker" type="datetime" format="yyyy-MM-dd HH:mm" value-format="yyyy-MM-dd HH:mm:ss" :default-time="`${
                          item.defaultTime ? item.defaultTime : '00:00'
                        }:00`" :placeholder="item.placeholder" :disabled="!item.isUpdate" :picker-options="pickerOptions(item.key)" style="min-width: 155px" />
                    </el-form-item>
                  </div>
                </div>
              </div>
            </div>
          </el-form>
          <el-row>
            <el-col :span="16" class="centerBox">
              <el-tabs v-model="tabActive" class="vone-tab-line" @tab-click="handleClick($event, leftTabs)">
                <el-tab-pane label="基本信息" class="contentBox" name="basic">
                  <div class="pContent">
                    <el-skeleton v-if="infoLoading" style="width: 100%" :loading="infoLoading" animated :count="8">
                      <template slot="template">
                        <div style="padding: 14px">
                          <el-row type="flex" style="margin-top: 6px">
                            <el-skeleton-item variant="p" style="width: 50%; margin-right: 16px" />
                            <el-skeleton-item variant="p" style="width: 50%" />
                          </el-row>
                        </div>
                      </template>
                    </el-skeleton>
                    <div v-else class="centerBasic">
                      <el-form ref="otherForm" :model="otherForm" label-position="top">
                        <el-row>
                          <el-col v-for="item in basicProperty" :key="item.id" :span="24">
                            <el-form-item :prop="item.key" class="custom-label">
                              <span slot="label" class="custom-label-wrapper">
                                <span style="flex: 1">{{ item.name }}</span>
                              </span>
                              <!-- 文本编辑器 -->
                              <span v-if="item.type == 'EDITOR'">
                                <vone-editor ref="editor" v-model="otherForm[item.key]" :preview="!item.isUpdate" @input.native="
                                    eventDisposalRangeChange(
                                      otherForm[item.key]
                                    )
                                  " />
                              </span>

                              <!-- 文件 -->
                              <vone-upload v-else-if="item.type == 'FILE'" ref="uploadFile" :biz-type="fileMap[typeCode]" :files-data="otherForm.files" :disabled="!item.isUpdate" />
                            </el-form-item>
                          </el-col>
                        </el-row>
                      </el-form>

                      <el-divider />
                      <vone-div-wrapper :title="'基本属性'">
                        <el-form ref="customForm" :model="customForm" label-position="top" :rules="formRules">
                          <el-row :gutter="24" type="flex" class="row-box">
                            <el-col v-for="item in customList" :key="item.id" :span="12">
                              <el-form-item :label="item.name" :prop="item.key">
                                <!-- 人员组件 -->
                                <vone-remote-user v-if="item.type == 'USER'" v-model="customForm[item.key]" :project-id="
                                    item.key == 'leadingBy' ||
                                      item.key == 'putBy' ||
                                      item.key == 'handleBy'
                                      ? projectId
                                      : ''
                                  " :disabled="!item.isUpdate" :multiple="item.multiple" />

                                <!-- 文件 -->
                                <vone-upload v-else-if="item.type == 'FILE'" ref="formUploadFile" :files-data="
                                    fixedForm.echoMap &&
                                      fixedForm.echoMap[item.key]
                                      ? fixedForm.echoMap[item.key]
                                      : fixedForm[item.key]
                                  " :biz-type="fileMap[typeCode]" @onSuccess="
                                    (files) => updateFiles(files, item.key)
                                  " @remove="(file) => removeFile(file, item.key)" />

                                <!-- 项目人员组件 -->
                                <projectRemoteUser v-if="item.type == 'PROJECTUSER'" v-model="customForm[item.key]" :multiple="item.multiple" />

                                <!-- 数 -->
                                <el-input-number v-else-if="item.type == 'INT'" v-model="customForm[item.key]" :min="0" :max="1000" :precision="item.precision" controls-position="right" :disabled="!item.isUpdate" style="width: 100%" :placeholder="item.placeholder" @input.native="
                                    (e) =>
                                      eventDisposalRangeChangeINT(
                                        e,
                                        customForm[item.key]
                                      )
                                  " />

                                <!-- 日期组件 -->
                                <el-date-picker v-else-if="item.type == 'DATE'" v-model="customForm[item.key]" prefix-icon="el-icon-date" type="datetime" format="yyyy-MM-dd HH:mm" value-format="yyyy-MM-dd HH:mm:ss" :default-time="`${item.defaultTime}:00`" :placeholder="item.placeholder" :disabled="!item.isUpdate" :picker-options="pickerOptions(item.key)" />
                                <!-- 输入框 -->
                                <el-input v-if="item.type == 'INPUT'" v-model="customForm[item.key]" :placeholder="item.placeholder" :disabled="!item.isUpdate" />
                                <!-- 组织机构 -->
                                <vone-tree-select v-else-if="item.type == 'ORG'" v-model="customForm[item.key]" search-nested :tree-data="orgData" placeholder="请选择机构" :disabled="!item.isUpdate" :multiple="item.multiple" />

                                <!-- 输入文本框 -->
                                <el-input v-else-if="item.type == 'TEXTAREA'" v-model="customForm[item.key]" type="textarea" :placeholder="item.placeholder" :disabled="!item.isUpdate" autosize />
                                <template v-else-if="item.type == 'LINK'">
                                  <el-tooltip class="item" effect="dark" :content="customForm[item.key]" placement="top">
                                    <span v-if="!item.isShowLink" class="name file-name" @click="fileClick(customForm[item.key])">{{ customForm[item.key] }}</span>
                                  </el-tooltip>
                                  <el-input v-if="item.isShowLink" v-model="customForm[item.key]" v-focus :placeholder="item.placeholder" @blur="editFn(item, false)" />
                                  <el-button v-if="!item.isShowLink" class="buttons" icon="iconfont el-icon-application-edit" type="text" size="small" @click="editFn(item, true)" />
                                </template>
                                <template v-else-if="
                                    item.type == 'SELECT' &&
                                      item.key != 'productModuleFunctionId'
                                  ">
                                  <!-- 远程搜索下拉 -->
                                  <el-select v-if="
                                      item.key == 'requirementId' ||
                                        item.key == 'planId' ||
                                        item.key == 'projectId' ||
                                        item.key == 'bugId'
                                    " v-model="customForm[item.key]" remote :placeholder="item.placeholder" :multiple="item.multiple" clearable filterable :disabled="!item.isUpdate" :remote-method="
                                      (e) => remoteMethod(e, item.key)
                                    " @focus="setOptionWidth">
                                    <el-option v-for="i in item.options" :key="i.id" :label="i.name" :value="i.id" :style="{ width: selectOptionWidth }">
                                      {{ i.code }}
                                      {{ i.name }}
                                    </el-option>
                                  </el-select>
                                  <!-- 下拉单选框 -->
                                  <el-select v-else v-model="customForm[item.key]" :placeholder="item.placeholder" clearable filterable :multiple="item.multiple" :disabled="!item.isUpdate" @focus="setOptionWidth" @change="changeSelect">
                                    <el-option v-for="i in item.options" :key="i.id" :label="i.name" :value="
                                        item.key == 'sourceCode' ||
                                          item.key == 'typeCode' ||
                                          item.key == 'priorityCode' ||
                                          item.key == 'envCode'
                                          ? i.code
                                          : i.id
                                      " :style="{ width: selectOptionWidth }">
                                      <span v-if="item.key == 'ideaId'">{{
                                        `${i.code}  ${i.name}`
                                      }}</span>

                                      <span v-if="item.key == 'productId'">
                                        {{ i.name }}
                                        <!-- <span v-if="i.echoMap" style="float:right">
                                          <el-tag v-if="i.echoMap.isHost" type="success">
                                            主
                                          </el-tag>
                                          <el-tag v-if="i.echoMap.isHost == false" type="warning">
                                            辅
                                          </el-tag>
                                        </span> -->
                                      </span>
                                    </el-option>
                                  </el-select>
                                  <!-- 下拉多选框 -->
                                  <!-- <el-select v-else-if="item.type == 'SELECT' && item.multiple&&item.key!='productModuleFunctionId'" v-model="customForm[item.key]" :placeholder="item.placeholder" multiple clearable filterable :disabled="!item.isUpdate" @focus="setOptionWidth">
                                    <el-option v-for="i in item.options" :key="i.id" :label="i.name" :value="i.name" :style="{ width: selectOptionWidth }" />
                                  </el-select> -->
                                </template>

                                <!-- 功能模块字段 -->
                                <vone-tree-select v-else-if="
                                    item.type == 'SELECT' &&
                                      item.key == 'productModuleFunctionId'
                                  " v-model="customForm[item.key]" :disabled="!item.isUpdate" search-nested :tree-data="item.options" placeholder="请选择" />
                                <!-- 关联类型 -->
                                <!-- <dataSelect v-else-if="item.type == 'LINKED'" :disabled="!item.isUpdate" text-info="edit" :model.sync="customForm[item.key]" :config="item" :placeholder="item.placeholder" @change="dataSelectChange($event,item.key)" /> -->
                                <!-- 引用类型 -->
                                <div v-else-if="item.type == 'QUOTE'">
                                  <vone-remote-user v-if="item.quoteType === 'user'" v-model="customForm[item.key]" disabled />
                                  <el-input v-else v-model="customForm[item.key]" disabled placeholder="" />
                                </div>
                              </el-form-item>
                            </el-col>
                          </el-row>
                        </el-form>
                      </vone-div-wrapper>
                    </div>
                  </div>
                </el-tab-pane>

                <el-tab-pane v-for="(tab, index) in leftTabs" :key="index" class="contentBox" :label="tab.label" :name="tab.name">
                  <component :is="tab.name" v-if="tab.active" :ref="tab.name" :type-code="typeCode" :issue-id="fixedForm.id" :issue-info="fixedForm" :product-id="fixedForm.productId" :info-disabled="infoDisabled" :project-id="fixedForm.projectId" :tab-name="tab.label" :show-popup-for-split="showPopupForSplit" @initList="initList" @add-child="addChild" />
                </el-tab-pane>
              </el-tabs>
            </el-col>

            <el-col v-loading="infoLoading" :span="8" class="rightBox">
              <el-tabs v-model="rightTabActive" class="vone-tab-line mintabline" @tab-click="handleClick($event, rightTabs)">
                <el-tab-pane v-for="tab in rightTabs" :key="tab.name" :label="tab.label" :name="tab.name">
                  <vone-comment v-if="rightTabActive == 'comment' && tab.active" :source-id="commentId" :source-type="typeCode" />
                  <activeTab v-if="rightTabActive == 'active' && tab.active" :form-id="id" :type="typeCode" :source-type="typeCode" :source-id="commentId" />
                  <activityRecord v-if="rightTabActive == 'activityRecord' && tab.active" :form-id="id" :type="typeCode" :source-type="typeCode" :source-id="commentId" />
                  <workTime v-if="rightTabActive == 'workTime' && tab.active" :source-id="commentId" :source-type="typeCode" :project-id="fixedForm.projectId" :row-type-code="rowTypeCode" />
                </el-tab-pane>
              </el-tabs>
            </el-col>
          </el-row>
        </div>
        <div class="footer-btn">
          <el-button @click="onClose">取消</el-button>
          <el-button v-if="!infoDisabled" type="primary" :loading="saveLoading" @click="saveInfo">确定</el-button>
        </div>

        <chat-gpt v-if="chatVisible" :visible.sync="chatVisible" :issue-info="fixedForm" />
      </div>
    </el-card>

    <!-- 新增 -->
    <vone-custom-add v-if="issueAddParam.visible" ref="vone-custom-add" :key="issueAddParam.key" :visible.sync="issueAddParam.visible" v-bind="issueAddParam" :type-code="globalTypeCode" :is-tooltip="true" :title="issueAddParam.title" @success="saveChildSuccess" />
  </div>
</template>

<script>
import storage from 'store'

const fileMap = {
  ISSUE: 'ISSUE_FILE_UPLOAD',
  BUG: 'BUG_FILE_UPLOAD',
  TASK: 'TASK_FILE_UPLOAD',
  RISK: 'RISK_FILE_UPLOAD',
  IDEA: 'IDEA_FILE_UPLOAD'
}

const iconMap = {
  tagId: 'el-icon-icon-fill-biaoqian',
  planStime: 'el-icon-icon-fill-wanchengshijian',
  startTime: 'el-icon-icon-fill-wanchengshijian',
  planEtime: 'el-icon-icon-fill-wanchengshijian',
  endTime: 'el-icon-icon-fill-wanchengshijian',
  expectedTime: 'el-icon-icon-fill-wanchengshijian',
  stateCode: 'el-icon-icon-fill-zhuangtai'
}

// 处理-连接拼接成驼峰命名
function toHump(str) {
  const reg = /-(\w)/g
  return str.replace(reg, function ($0, $1) {
    return $1.toUpperCase()
  })
}

// 处理首字母大写
function upperFirst(str) {
  return str.charAt(0).toUpperCase() + str.slice(1)
}

// 读取当前文件夹下components文件夹下.vue文件
const requireComponents = require.context(
  '@/components/CustomEdit/commonTab/',
  false,
  /\.vue$/
)

const componentsObj = {}
requireComponents.keys().forEach((filePath) => {
  const componentName = upperFirst(
    toHump(filePath.split('/')[1].replace(/\.vue$/, ''))
  )
  const componentConfig = requireComponents(filePath)
  componentsObj[componentName] = componentConfig.default || componentConfig
})

import {
  apiAlmUpdate,
  apiAlmGetInfo,
  apiAlmSourceNoPage
} from '@/api/vone/project/issue'
import {
  apiAlmPriorityNoPage,
  apiAlmTypeNoPage,
  getAllProductInfoList,
  apiAlmGetTypeNoPage
} from '@/api/vone/alm/index'

import {
  apiVaBaseCustomFormField,
  apiVaBaseCustomFormFieldProgram,
  apiVaBaseCustomFormFieldProject
} from '@/api/vone/base/customForm'
import {
  productListByCondition,
  queryListByCondition
} from '@/api/vone/project/index'
import { planListByCondition } from '@/api/vone/project/iteration'
import { requirementListByCondition } from '@/api/vone/project/issue'
import { bugListByCondition } from '@/api/vone/project/defect'
import { getProjectPlans } from '@/api/vone/testmanage/case'
import { getProductVersionList, getModule } from '@/api/vone/product/index'
import { apiBaseDictNoPage } from '@/api/vone/base/dict'
import { gainTreeList } from '@/utils'
import { orgList } from '@/api/vone/base/org'
import { getSourceById } from '@/api/vone/base/source'
import _ from 'lodash'

// 其他组件：
import workTime from '@/components/CustomEdit/commonTab/work-time.vue'
import activeTab from '@/components/CustomEdit/commonTab/active.vue'
import tagSelect from '@/components/CustomEdit/components/tag-select.vue'
import activityRecord from '@/components/CustomEdit/commonTab/activity-record.vue'
import StateCode from '@/components/CustomEdit/components/state-code.vue'
import projectRemoteUser from '@/components/CustomEdit/components/project-user-remote.vue'

export default {
  name: 'CustomEdit',
  components: {
    projectRemoteUser,
    StateCode,
    activityRecord,
    tagSelect,
    activeTab,
    workTime,
    ...componentsObj
  },
  directives: {
    focus: {
      inserted: function (el) {
        el.querySelector('input').focus()
      }
    }
  },
  props: {},
  data() {
    return {
      // 下面是属性props的参数，调整成从local获取吧。
      visible: false, // 是否显示弹框。目前改造成路由，不需要了
      title: '', // 页面标题
      id: undefined, // issue id
      infoDisabled: false, // form表单是否可以编辑
      sprintId: undefined, // 1
      tableList: [], // 需求列表，用来切换上一个和下一个
      typeCode: undefined, // ISSUE
      leftTabs: [], // 左侧Tabs
      rightTabs: [], // 右侧Tabs
      formId: undefined, // 平台配置,预览表单接口,需要根据formId查询模板
      rowTypeCode: undefined, // 需求中心/项目里,从列表数据取到的类型
      stateCode: undefined, // 需求中心/项目里,从列表数据取到的状态
      rowProjectId: undefined, // 需求中心,从列表数据取到的关联项目的项目id
      hidePrv: true, // 不显示上一个下一个按钮
      showPopupForSplit: false, // 拆分子项是否弹框
      // 原有参数
      chatVisible: false,
      iconMap,
      page: null,
      drawerLoading: false,
      fileMap,
      tabActive: 'basic',
      rightTabActive: 'comment',
      saveLoading: false,
      pageLoading: false,
      currentIndex: undefined, // 当前数据的索引
      customForm: {},
      fixedForm: {},
      otherForm: {},
      formRules: {},
      fixdFormRules: {},
      basicProperty: [],
      fixedProperty: [],
      customList: [],
      infoLoading: true,
      selectOptionWidth: '',
      commentId: '',
      planIdList: [], // 计划
      orgData: [], // 组织机构
      isChange: false,
      projectId: this.$route.params.id,
      // 新增模块的内容
      globalTypeCode: 'ISSUE',
      issueAddParam: {
        // 新增
        visible: false
      }
    }
  },
  computed: {},
  watch: {
    'customForm.projectId': {
      handler(value) {
        if (!value) {
          this.$set(this.customForm, 'planId', '')
          this.setData(this.customList, 'planId', [])
          return
        }

        this.getPlanList(value)
      },
      immediate: true
    },
    'customForm.productId': {
      handler(value) {
        if (!value) {
          this.$set(this.customForm, 'productVersionId', '')
          this.$set(this.customForm, 'productRepairVersionId', '')
          this.$set(this.customForm, 'productModuleFunctionId', null)
          this.setData(this.customList, 'productVersionId', [])
          this.setData(this.customList, 'productRepairVersionId', [])
          this.setData(this.customList, 'productModuleFunctionId', [])
          return
        }
        this.getVersion(value)
        this.getModel(value)
      },
      deep: true,
      immediate: true
    }
  },
  created() {
    const customEdit = storage.get('customEdit')
      ? JSON.parse(storage.get('customEdit'))
      : {}
    this.visible = customEdit.visible || false
    this.title = customEdit.title || undefined
    this.id = customEdit.id || undefined
    this.infoDisabled = customEdit.infoDisabled || false
    this.sprintId = customEdit.sprintId || undefined
    this.tableList = customEdit.tableList || []
    this.typeCode = customEdit.typeCode || undefined
    this.leftTabs = customEdit.leftTabs || []
    this.rightTabs = customEdit.rightTabs || []
    this.formId = customEdit.formId || undefined
    this.rowTypeCode = customEdit.rowTypeCode || undefined
    this.stateCode = customEdit.stateCode || undefined
    this.rowProjectId = customEdit.rowProjectId || undefined
    this.hidePrv = customEdit.hidePrv || false
    this.showPopupForSplit = customEdit.showPopupForSplit || false
  },
  async mounted() {
    this.commentId = this.id || ''
    this.currentIndex = this.tableList.findIndex((item) => item.id === this.id)
    await this.getFormTemplete()
    this.getSelectSource()

    this.leftTabs.forEach((element) => {
      element.active = false
    })
    // this.getUserList()
  },

  methods: {
    pickerOptions(e) {
      var _this = this
      if (e == 'planEtime') {
        return {
          disabledDate(time) {
            if (_this.customForm['planStime']) {
              return (
                time.getTime() <
                new Date(_this.customForm['planStime']).getTime()
              )
            }
          }
        }
      } else if (e == 'planStime') {
        return {
          disabledDate(time) {
            if (_this.fixedForm['planEtime']) {
              return (
                time.getTime() >
                new Date(_this.fixedForm['planEtime']).getTime()
              )
            }
          }
        }
      }
    },
    fileClick(link) {
      if (link && (_.startsWith(link, 'http') || _.startsWith(link, 'https'))) {
        window.open(link, '_blank')
      } else {
        window.open('http://' + link, '_blank')
      }
    },
    editFn(e, vlaue) {
      this.$set(e, 'isShowLink', vlaue)
    },
    remoteMethod: _.debounce(function (e, t) {
      if (t == 'requirementId') {
        this.getRequirementList(e)
      } else if (t == 'planId') {
        this.getPlanList(this.customForm.projectId, e)
      } else if (t == 'projectId') {
        this.getProjectList(e)
      } else if (t == 'bugId') {
        this.getBugList(e)
      }
    }, 1000),
    async getQuoteType() {
      this.customList.forEach((e, index) => {
        if (e.type == 'QUOTE') {
          const config = JSON.parse(e.config)
          this.getTableConfig(config.relationShipsheet, e, (item) => {
            e.quoteType = item?.type
            this.$set(this.customList, index, e)
          })
        }
      })
    },
    async getTableConfig(id, e, callback) {
      const config = JSON.parse(e.config)
      const res = await getSourceById(id)
      if (res.isSuccess) {
        const list = res.data.fields.map((v) => {
          v.prop = v.id
          const obj = JSON.parse(v.config)
          return {
            ...v,
            ...obj
          }
        })
        const objs = list.find(
          (e) => e.id == config.relationField && !e.primary
        )
        callback(objs)
      }
    },
    dataSelectChange({ item, list, user }, key) {
      this.customList.forEach((e) => {
        if (e.type == 'QUOTE') {
          const config = JSON.parse(e.config)
          if (key == config.queryCriteriaC) {
            this.customForm[e.key] = item[config.relationField]
            const obj = list.find(
              (e) => e.id == config.relationField && !e.primary
            )
            e.quoteType = obj.type
            if (e.quoteType == 'user') {
              e.user = user[item[config.relationField]]
            }
          }
        }
      })
    },
    changeSelect() {
      this.isChange = true
    },
    // 数据源接口返回数据以后,把值塞到表单模板里
    setData(list, key, data) {
      list.forEach((e, index) => {
        if (e.key == key) {
          this.$set(e, 'options', data)
        }
      })
    },
    // 下拉框弹出时，设置弹框的宽度
    setOptionWidth(event) {
      this.$nextTick(() => {
        this.selectOptionWidth =
          event.target.parentNode.parentNode.clientWidth + 'px'
      })
    },

    async getSelectSource() {
      this.getOrgList()
      this.getSourceList()
      this.getPrioritList()
      this.getProjectList()

      const keys = this.customList.map((r) => r.key)
      if (keys.includes('envCode')) {
        this.getEnvList()
      }

      if (this.$route.params.projectKey) {
        this.getRequirementList()
        this.getBugList()
        this.getAllTypeCode()
        this.getProjectProductList()
      } else {
        this.getIssueType()
        this.productList()
      }

      this.typeCode == 'BUG' ? this.getProjectTestPlan() : null
    },
    // 工作项显示
    async getFormTemplete(val) {
      this.fixdForm = {}
      this.form = {}
      this.drawerLoading = true

      // 需求中心，项目，项目集，查询表单项时调不同的接口url，参数格式也不同
      // 需求中心和其它使用：apiVaBaseCustomFormField
      // 项目使用：apiVaBaseCustomFormFieldProject
      // apiVaBaseCustomFormFieldProgram

      const activeApp = this.$route.meta.activeApp
      const params = {
        typeCode: this.rowTypeCode || this.$route.query.rowTypeCode,
        stateCode: this.stateCode,
        projectId: this.rowProjectId || null
      }
      const res =
        activeApp == 'projectm'
          ? await apiVaBaseCustomFormFieldProgram(
            this.$route.params.id,
            this.typeCode,
            params
          )
          : activeApp == 'project'
            ? await apiVaBaseCustomFormFieldProject(
              this.$route.params.id,
              this.typeCode,
              params
            )
            : await apiVaBaseCustomFormField(this.typeCode, params)

      this.drawerLoading = false
      if (!res.isSuccess) {
        return
      }

      res.data.forEach((element) => {
        element.placeholder = JSON.parse(element.config)?.placeholder
        element.multiple = JSON.parse(element.config)?.multiple
        element.message = JSON.parse(element.config)?.message
        element.validator = JSON.parse(element.config)?.validator
        element.defaultTime = JSON.parse(element.config)?.defaultTime
        element.options = !element.isBuilt
          ? JSON.parse(element.config)?.options
          : []
        element.disabled = element.key == 'planId'
        element.type = element.type.code
        element.precision = JSON.parse(element.config)?.precision

        // if (element.type == 'QUOTE') {
        //   this.getQuoteType()
        // }
      })

      // 所有基本属性
      const basicAll =
        res.data && res.data.length
          ? res.data.filter((r) => r.isBasic && r.isShow && r.state)
          : []

      // 其它基本属性
      const other = basicAll.filter(
        (r) => r.key == 'files' || r.key == 'description'
      )

      // 固定基本属性
      const fixed = _.difference(basicAll, other)

      // 固定基本属性
      this.fixedProperty = fixed.sort(function (a, b) {
        return a.sort - b.sort
      })
      // 固定属性[文件和描述]
      this.basicProperty = other.sort(function (a, b) {
        return a.sort - b.sort
      })
      // 排序
      // 自定义属性
      const custom =
        res.data && res.data.length
          ? res.data.filter((r) => !r.isBasic && r.isShow && r.state)
          : []
      this.customList = custom.sort(function (a, b) {
        return a.sort - b.sort
      })

      // 固定属性的校验规则
      const fixedRoule = this.fixedProperty.map((r) => ({
        required: r.key !== 'typeCode' ? r.isRequired : false,
        message: r.message,
        max: r.max,
        pattern: r.validator,
        key: r.key,
        type: r.key == 'tagId' ? 'array' : 'string'
      }))

      fixedRoule.forEach((item) => {
        if (!this.fixdFormRules[item.key]) {
          this.fixdFormRules[item.key] = [item]
        } else {
          this.fixdFormRules[item.key].push(item)
        }
      })
      this.customList.forEach((r) => {
        const rulesObj = {
          required: r.isRequired,
          message: r.message ? r.message : '该字段为必填项',
          max: r.max,
          pattern: r.validator,
          key: r.key,
          type: r.multiple ? 'array' : r.type == 'INT' ? 'number' : 'string'
        }
        if (r.type == 'LINKED') {
          this.formRules[r.key] = [
            { required: r.isRequired, message: r.message }
          ]
        } else if (r.type == 'INT') {
          this.formRules[r.key] = [
            { required: r.isRequired, message: r.message, trigger: 'change' }
          ]
        } else {
          this.formRules[r.key] = [rulesObj]
        }
      })
      this.getQuoteType()
      await this.getIssueInfo(val)
    },
    // 查询所有机构
    async getOrgList() {
      const res = await orgList()
      if (!res.isSuccess) {
        return
      }
      const orgTree = gainTreeList(res.data)
      this.orgData = orgTree
    },
    // 查询来源
    async getSourceList() {
      const res = await apiAlmSourceNoPage({
        typeClassify: this.typeCode
      })
      if (!res.isSuccess) {
        return
      }
      this.setData(this.customList, 'sourceCode', res.data)
    },
    async getEnvList() {
      const res = await apiBaseDictNoPage({
        type: 'ENVIRONMENT'
      })
      if (!res.isSuccess) {
        return
      }
      this.setData(this.customList, 'envCode', res.data)
    },
    // 查询项目关联的产品，用于缺陷和任务表单关联产品【主办/辅办】
    async getProjectProductList() {
      const res = await getAllProductInfoList(this.$route.params.id || '0')
      if (!res.isSuccess) {
        return
      }
      this.setData(this.customList, 'productId', res.data)
    },
    // 关联产品版本
    async getVersion(val) {
      if (this.isChange) {
        this.$set(this.customForm, 'productVersionId', '')
        this.$set(this.customForm, 'productRepairVersionId', '')
      }

      const res = await getProductVersionList({ productId: val })
      if (!res.isSuccess) {
        return
      }
      this.setData(this.customList, 'productVersionId', res.data)
      this.setData(this.customList, 'productRepairVersionId', res.data)
    },
    // 关联产品功能
    async getModel(val) {
      if (this.isChange) {
        this.$set(this.customForm, 'productModuleFunctionId', null)
      }
      const form = {}
      this.$set(form, 'productId', val)
      const parameter = {
        model: { ...form },
        extra: { tableSave: false }
      }
      const res = await getModule(parameter)
      if (!res.isSuccess) {
        return
      }
      this.setData(
        this.customList,
        'productModuleFunctionId',
        this.getTreeList(res.data)
      )
    },
    getTreeList(data, obj = []) {
      data.map((item) => {
        const type =
          item.nodeType == 1 ? ' (模块)' : item.nodeType == 2 ? ' (功能)' : ''
        item.label = item.name + type
        if (item.children && item.children.length > 0) {
          this.getTreeList(item.children)
        }
      })
      return data
    },
    // 查优先级
    async getPrioritList() {
      const res = await apiAlmPriorityNoPage()
      if (!res.isSuccess) {
        return
      }

      this.setData(this.customList, 'priorityCode', res.data)
    },
    // 查项目下需求
    async getRequirementList(e) {
      const res = await requirementListByCondition({
        projectId: this.$route.params.id,
        name: e
      })
      if (!res.isSuccess) {
        return
      }
      this.setData(this.customList, 'requirementId', res.data)
    },
    // 查项目下缺陷
    async getBugList(e) {
      const res = await bugListByCondition({
        projectId: this.$route.params.id,
        name: e
      })
      if (!res.isSuccess) {
        return
      }

      this.setData(this.customList, 'bugId', res.data)
    },
    // 查询项目下类型
    async getAllTypeCode() {
      const res = await apiAlmGetTypeNoPage(
        this.$route.params.id,
        this.typeCode
      )
      if (!res.isSuccess) {
        return
      }

      this.setData(this.fixedProperty, 'typeCode', res.data)
    },
    // 查询所有类型
    async getIssueType() {
      const res = await apiAlmTypeNoPage({
        classify: this.typeCode
      })
      if (!res.isSuccess) {
        return
      }

      this.setData(this.fixedProperty, 'typeCode', res.data)
    },
    // 归属产品
    async productList() {
      const res = await productListByCondition()

      if (!res.isSuccess) {
        return
      }

      this.setData(this.customList, 'productId', res.data)
    },
    // 归属项目
    async getProjectList(e) {
      const res = await queryListByCondition({
        name: e
      })

      if (!res.isSuccess) {
        return
      }

      this.setData(this.customList, 'projectId', res.data)
    },

    // 迭代计划
    async getPlanList(val, e) {
      if (this.isChange) {
        this.$set(this.customForm, 'planId', '')
      }
      const res = await planListByCondition({
        isFiled: false,
        projectId: val,
        name: e
      })
      if (!res.isSuccess) {
        return
      }
      this.planIdList = this.sprintId
        ? res.data.filter((r) => r.stateCode != 4)
        : res.data
      this.setData(this.customList, 'planId', this.planIdList)
    },
    // 测试计划
    async getProjectTestPlan() {
      const res = await getProjectPlans({
        projectId: this.$route.params.id
      })
      if (!res.isSuccess) {
        return
      }
      this.setData(this.customList, 'testPlanId', res.data)
    },
    initList() {
      this.$emit('success')
    },
    eventDisposalRangeChange(value) {
      const textLength = this.$refs.editor[0].$el.innerText.replace(
        /[|]*\n/,
        ''
      ).length
      if (textLength >= 1000) {
        this.$refs.otherForm.validateField(['description'])
      } else {
        this.$refs.otherForm.clearValidate(['description'])
      }
    },
    updateFiles(files, key) {
      this.customForm[key] = files.map((v) => v.id).join(',')
    },
    removeFile(file, key) {
      const re = new RegExp(file.id, 'g')
      this.customForm[key] = this.customForm[key]
        .replace(re, '')
        .replace(',,', ',')
    },
    onClose() {
      const params = this.$route.params || {}
      this.$router.push(
        `/project/issue/${params.projectKey}/${params.projectTypeCode}/${params.id}`
      )
      this.$emit('update:visible', false)
    },
    handleClick(event, list) {
      list.forEach((v) => {
        if (v.name == event.name) {
          v.active = true
        } else {
          v.active = false
        }
      })
    },
    validatePromise(component, message) {
      return new Promise((resolve, reject) => {
        component.validate((valid, rules) => {
          if (!Object.keys(rules)?.length) {
            resolve(valid)
          } else {
            // const firstRule = rules[Object.keys(rules)?.[0]]
            // const errorMessage = firstRule?.[0]?.message || message
            this.$message.warning('请填写必填项')
            reject({ valid, rules })
          }
        })
      })
    },
    // 保存
    async saveInfo() {
      if (this.tabActive != 'basic') {
        this.onClose()
        return
      }
      try {
        const fixdForm_promise = this.validatePromise(
          this.$refs.fixedForm,
          '请填写必填项'
        )
        await fixdForm_promise
        const other_promise = this.validatePromise(
          this.$refs.otherForm,
          '请填写必填项'
        )
        await other_promise
        const custom_promise = this.validatePromise(
          this.$refs.customForm,
          '请填写必填项'
        )
        await custom_promise
        this.saveLoading = true
        this.$set(
          this.otherForm,
          'files',
          this.$refs['uploadFile'][0].uploadFiles
        )

        // 自定义属性下的文件
        const customFile = this.customList.find((r) => r.type == 'FILE')?.key
        if (customFile) {
          this.$set(
            this.customForm,
            customFile,
            this.$refs['formUploadFile'][0].uploadFiles
              .map((r) => r.id)
              .join(',')
          )
        }
        const params = {
          ...this.fixdForm,
          ...this.otherForm,
          ...this.customForm
        }

        // 判断key里面有没有C1到C30的，有的话，他的值如果是[],就是用逗号分隔成字符串传给后端
        var arr = Array.from(Array(30), (v, k) => `c${k + 1}`)
        const multipleList = this.customList
          .filter((r) => r.multiple)
          .map((r) => r.key)
        for (var item in params) {
          if (arr.includes(item)) {
            if (multipleList.includes(item) && Array.isArray(params[item])) {
              params[item] = params[item].map((r) => r).join(',') || null
            }
          }
        }

        const urlMap = {
          ISSUE: '/api/alm/alm/requirement',
          BUG: '/api/alm/alm/bug',
          TASK: '/api/alm/alm/task',
          RISK: '/api/alm/alm/risk',
          IDEA: '/api/alm/alm/idea'
        }
        const res = await apiAlmUpdate(urlMap[this.typeCode], params)
        this.saveLoading = false
        if (!res.isSuccess) {
          this.$message.warning(res.msg)
          return
        }

        this.$message.success('保存成功')
        this.$emit('success')
        this.onClose()
      } catch (e) {
        this.saveLoading = false
      }
    },
    changeFlow() {
      this.$emit('success')
      this.onClose()
    },

    // 详情
    async getIssueInfo(val) {
      this.drawerLoading = true
      this.infoLoading = true

      const urlMap = {
        ISSUE: `/api/alm/alm/requirement/${val || this.id}`,
        BUG: `/api/alm/alm/bug/${val || this.id}`,
        TASK: `/api/alm/alm/task/${val || this.id}`,
        RISK: `/api/alm/alm/risk/${val || this.id}`,
        IDEA: `/api/alm/alm/idea/${val || this.id}`
      }

      const res = await apiAlmGetInfo(urlMap[this.typeCode])

      if (!res.isSuccess) {
        return
      }

      if (!res.data) {
        return
      }
      // 判断key里面有没有C1到C30的，有的话，如果是多选,就是分隔成数组
      var arr = Array.from(Array(30), (v, k) => `c${k + 1}`)
      const multipleList = this.customList
        .filter((r) => r.multiple)
        .map((r) => r.key)
      for (var item in res.data) {
        if (arr.includes(item)) {
          if (multipleList.includes(item)) {
            res.data[item] = res.data[item] ? res.data[item].split(',') : []
          }
        }
      }
      if (
        res.data.tagId &&
        res.data.tagId.length &&
        res.data.echoMap &&
        res.data.echoMap.tagId
      ) {
        this.$set(
          res.data,
          'tagId',
          res.data.echoMap.tagId.map((r) => r.name) || []
        )
      }
      this.customForm = res.data
      this.fixedForm = res.data
      this.otherForm = res.data

      // if (this.customForm && this.customForm.projectId) {
      //   this.isChange = false
      //   this.getPlanList(this.customForm.projectId)
      // }

      if (this.customForm && this.customForm.productId) {
        this.isChange = false

        this.getVersion(this.customForm.productId)
        this.getModel(this.customForm.productId)
      }

      this.infoLoading = false
      this.drawerLoading = false
      this.customList.forEach((e) => {
        if (
          e.key == 'requirementId' ||
          e.key == 'planId' ||
          e.key == 'projectId' ||
          e.key == 'bugId'
        ) {
          this.$set(
            e,
            'options',
            res.data.echoMap[e.key] ? [res.data.echoMap[e.key]] : []
          )
        }
      })
      // 如果需求没有关联项目,不允许填报工时
      this.$emit('hideTab', res.data.projectId ? 1 : 0)
    },
    // 上一条
    dataPrev() {
      if (this.currentIndex === 0) {
        this.$message.warning('已经是第一条数据啦')
        return
      }
      this.currentIndex--
      this.getFormTemplete(this.tableList[this.currentIndex].id)
      this.commentId = this.tableList[this.currentIndex].id
    },
    // 下一条
    dataNext() {
      if (this.currentIndex === this.tableList.length - 1) {
        this.$message.warning('已经是最后一条数据啦')
        return
      }
      this.currentIndex++

      this.getFormTemplete(this.tableList[this.currentIndex].id)
      this.commentId = this.tableList[this.currentIndex].id
    },
    async editStatus() {
      this.getIssueInfo()
      this.$emit('success')
      // 查询当前表格项数据
    },
    // 取消数字输入框的校验
    eventDisposalRangeChangeINT(value, prop) {
      if (value != undefined) {
        this.$refs.customForm.clearValidate(prop)
      }
    },
    /**
     * 新增模块的内容
     */
    addChild(type) {
      console.log('addChild执行了', type)
      if (type == 'issue') {
        this.globalTypeCode = 'ISSUE'
        this.issueAddParam = {
          visible: true,
          key: Date.now(),
          infoDisabled: false,
          title: '新增需求'
        }
      } else if (type == 'task') {
        this.globalTypeCode = 'TASK'
        this.issueAddParam = {
          visible: true,
          key: Date.now(),
          infoDisabled: false,
          title: '新增任务'
        }
      } else if (type == 'bug') {
        this.globalTypeCode = 'BUG'
        this.issueAddParam = {
          visible: true,
          key: Date.now(),
          infoDisabled: false,
          title: '新增缺陷'
        }
      }

      setTimeout(async () => {
        // 获取需求详情
        const res = await apiAlmGetInfo(`/api/alm/alm/requirement/${this.id}`)

        // 基本信息赋值
        const fixdForm = this.$refs['vone-custom-add']?.fixdForm
        this.$set(fixdForm, 'name', res.data.name)
        this.$set(fixdForm, 'planEtime', res.data.planEtime)
        this.$set(fixdForm, 'description', res.data.description)

        // 自定义组件集合
        const customList = this.$refs['vone-custom-add'].customList || []
        // 基本属性赋值
        const form = this.$refs['vone-custom-add']?.form
        if (type === 'issue') {
          this.$set(form, 'parentId', res.data.id)
          this.$set(form, 'sourceCode', res.data.sourceCode)
          this.$set(form, 'planStime', res.data.planStime)
          this.$set(form, 'planId', res.data.planId)
          this.$set(form, 'productVersionId', res.data.productVersionId)
          this.$set(
            form,
            'productModuleFunctionId',
            res.data.productModuleFunctionId
          )
        } else if (type === 'task') {
          this.$set(
            form,
            'sourceCode',
            customList.find((item) => item.key === 'sourceCode')?.options?.[0][
            'code'
            ]
          )
          this.$set(form, 'planStime', res.data.planStime)
          this.$set(form, 'planId', res.data.planId)
          this.$set(form, 'productVersionId', res.data.productVersionId)
          this.$set(
            form,
            'productRepairVersionId',
            res.data.productRepairVersionId
          )
          this.$set(form, 'requirementId', res.data.id)
          this.$set(form, 'c4', res.data.putBy)
        } else if (type === 'bug') {
          this.$set(form, 'requirementId', res.data.id)
          this.$set(form, 'planStime', res.data.planStime)
          this.$set(form, 'planId', res.data.planId)
          this.$set(
            form,
            'sourceCode',
            customList.find((item) => item.key === 'sourceCode')?.options?.[0][
            'code'
            ]
          )
          this.$set(
            form,
            'envCode',
            customList.find((item) => item.key === 'envCode')?.options?.[0][
            'id'
            ]
          )
        }
      }, 500)
    },
    saveChildSuccess() {
      // this.getInitTableData({})
      // this.$refs['vone-custom-info']?.$refs['IssueToIssue']?.[0]?.getChildrenTable()
      // this.$refs['vone-custom-info']?.$refs['TaskTab']?.[0]?.getTableData()
      // this.$refs['vone-custom-info']?.$refs['BugTab']?.[0]?.getTableData()
      // this.$refs['vone-custom-edit']?.$refs['IssueToIssue']?.[0]?.getChildrenTable()
      // this.$refs['vone-custom-edit']?.$refs['TaskTab']?.[0]?.getTableData()
      // this.$refs['vone-custom-edit']?.$refs['BugTab']?.[0]?.getTableData()

      this.$refs['IssueToIssue']?.[0]?.getChildrenTable()
      this.$refs['TaskTab']?.[0]?.getTableData()
      this.$refs['BugTab']?.[0]?.getTableData()
    }
  }
}
</script>

<style scoped lang="scss">
// 改动的css：1. 注释了dialog相关的内容；2.原有的样式，避免css污染，放在了issue-info里面。
.custom-edit {
  height: calc(100vh - 20px - 48px);

  & > .el-card {
    height: 100%;
  }

  .footer-btn {
    text-align: right;
  }

  // 原有的样式，避免css污染，放在了issue-info里面。
  .drawerBox {
    ::v-deep .el-tabs--left .el-tabs__header.is-left {
      margin-right: 0;
    }

    ::v-deep .el-tabs--left .el-tabs__item.is-left {
      text-align: center;
    }

    .drawerCode {
      color: var(--auxiliary-font-color);
    }

    //::v-deep .el-dialog .el-dialog__body {
    //  padding: 0;
    //  overflow-y: hidden;
    //  height: calc(90vh - 115px);
    //}

    .basicHeader {
      background-color: #fff;
      height: 120px;
      padding: 8px 16px;
      border-bottom: 1px solid var(--solid-border-color);
      margin: 0 !important;

      .name-row {
        .el-col-24 {
          ::v-deep .el-input--small .el-input__inner {
            height: 36px;
            line-height: 36px;
            border: none;
            background: none;
            font-weight: 600;
            font-size: 16px;

            &:hover {
              background-color: #eaecf0;
            }

            &:focus {
              background-color: #fff;
              border: 1px solid #3e7bfa;
            }
          }
        }
      }

      .basica-form {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 90%;

        .fixedItem {
          width: 100%;
          padding: 8px;
          flex: 1;
          display: flex;
          justify-content: flex-start;
          align-items: center;
          padding-left: 10px;

          i,
          .svg-icon {
            font-size: 24px;
          }

          ::v-deep .el-select__tags {
            height: 22px;
            line-height: 22px;
            flex-wrap: nowrap;
          }

          ::v-deep .el-input {
            height: 22px;
            line-height: 22px;
          }

          ::v-deep .el-input--small .el-input__inner {
            font-size: 14px;
            border: none;
            background: none;
            height: 22px;
            line-height: 22px;
            // color: var(--main-font-color);
          }

          ::v-deep .is-disabled {
            color: var(--input-border-color);
          }

          ::v-deep .el-input__icon {
            line-height: 22px;
          }

          ::v-deep .el-input__suffix {
            display: none;
          }

          .dataPicker {
            min-width: 155px;

            ::v-deep .el-input__prefix {
              display: none;
            }

            ::v-deep .el-input__inner {
              padding-left: 12px;
            }
          }

          ::v-deep .el-form-item {
            margin-bottom: 0;

            .el-form-item__label {
              color: var(--auxiliary-font-color);
              height: 22px;
              line-height: 22px;
              padding: 0 0 4px 0px;
            }

            .el-form-item__content {
              line-height: 22px;
              display: flex;
              align-items: center;
            }
          }
        }

        .fixedItem:hover {
          background-color: #f2f3f5;
        }

        .fixedItemType:hover {
          background-color: #fff;
        }
      }
    }

    .centerBox {
      border-right: 1px solid var(--solid-border-color);

      .custom-label {
        ::v-deep.el-form-item__label {
          width: 100%;

          .custom-label-wrapper {
            display: flex;

            .label-copilot {
              cursor: pointer;
              color: var(--main-theme-color);
              font-weight: 500;

              &:hover {
                color: var(--main-hover-theme-color);
              }
            }
          }
        }
      }

      ::v-deep .el-tabs--left .el-tabs__nav-wrap.is-left::after {
        width: 1px;
      }

      .contentBox {
        padding: 16px;
        height: calc(90vh - 282px);
        overflow-y: auto;
      }
    }

    .rightBox {
      height: calc(90vh - 235px);
      // overflow-y: auto;
      ::v-deep .el-tabs__header {
        margin: 0;

        .el-tabs__item {
          height: 48px;
          line-height: 48px;
        }
      }

      ::v-deep .el-tabs__item.is-active {
        color: var(--main-theme-color);
      }

      ::v-deep .el-tabs__nav-wrap::after {
        height: 1px;
      }
    }

    .border {
      border-left: 4px solid var(--main-theme-color);
      padding-left: 10px;
      height: 24px;
      line-height: 24px;
      font-size: 16px;
    }

    ::v-deep .el-tabs--border-card > .el-tabs__content {
      padding: 0;
    }

    ::v-deep .el-date-editor.el-input {
      width: 100%;
    }

    ::v-deep .el-tabs__nav-wrap .is-top {
      .el-tabs__nav-scroll {
        padding-left: 15px;
      }
    }

    .row-box {
      margin-bottom: 20px;
      display: flex;
      flex-wrap: wrap;
    }

    .focusBlur {
      border: 1px solid var(--input-border-color);
      border-radius: 4px;
      padding: 1px 12px;
      min-height: 50px;
      max-height: 200px;
      overflow-y: auto;
    }

    .disabledDiv {
      cursor: not-allowed !important;
      pointer-events: none;
      background-color: var(--disabled-bg-color);
      border: none;
    }
  }

  .el-divider {
    margin: 0px 0px 16px 0px;
  }

  ::v-deep .card-wrapper-header {
    margin-top: 0px !important;
  }

  .file-name {
    display: inline-block;
    width: calc(100% - 65px);
    color: #3e7bfa;
    cursor: pointer;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    vertical-align: middle;
  }
}
</style>
