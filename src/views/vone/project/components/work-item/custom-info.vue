<template>
  <div class="issue-info">
    <!--需求：dialog调整成路由。
    页面保持跟src/components/CustomInfo/index.vue一致
    改动：1.在详情页面添加需求等场景（add-child）需要手动调整在当前页面（原来在issue页面公用的新增逻辑）
    -->
    <el-card class="box-card">
      <div class="drawerBox">
        <div>
          {{ title }}【
          <span class="drawerCode">
            {{ fixedForm.code }}
          </span>
          】
          <!-- <i class="iconfont el-icon-yibiaopan-shangyi nextBtn" @click="dataPrev" />
          <i class="iconfont el-icon-yibiaopan-xiayi nextBtn" @click="dataNext" /> -->
        </div>

        <div>
          <el-form
            ref="fixedForm"
            :model="fixedForm"
            :disabled="infoDisabled"
            label-position="top"
          >
            <div class="basicHeader">
              <el-skeleton
                :loading="drawerLoading"
                style="width: 100%"
                animated
              >
                <template slot="template">
                  <el-skeleton-item
                    variant="p"
                    style="width: 30%; margin-bottom: 14px"
                  />
                  <div
                    style="
                      display: flex;
                      align-items: center;
                      justify-content: space-between;
                    "
                  >
                    <div
                      v-for="index in 5"
                      :key="index"
                      style="flex: 1; display: flex; align-items: center"
                    >
                      <el-skeleton-item
                        variant="image"
                        style="width: 30px; height: 30px"
                      />
                      <div style="width: 190px; margin-left: 10px">
                        <el-skeleton-item
                          variant="p"
                          style="width: 50%; margin-bottom: 6px"
                        />
                        <el-skeleton-item variant="p" style="width: 70%" />
                      </div>
                    </div>
                  </div>
                </template>
              </el-skeleton>

              <div v-if="!drawerLoading">
                <el-row
                  v-for="item in fixedProperty.filter((r) => r.key == 'name')"
                  :key="item.id"
                  class="name-row"
                >
                  <el-col :span="24">
                    <!-- 输入框 -->
                    <el-input
                      v-model="fixedForm[item.key]"
                      :placeholder="item.placeholder"
                      :disabled="!item.isUpdate"
                    />
                  </el-col>
                </el-row>

                <div class="basica-form">
                  <div
                    v-for="item in fixedProperty.filter((r) => r.key != 'name')"
                    :key="item.id"
                    class="fixedItem"
                  >
                    <el-form-item
                      :label="item.key != 'name' ? item.name : null"
                      :prop="item.key"
                    >
                      <!-- 图标 -->
                      <span v-if="item.key == 'typeCode'">
                        <span
                          v-if="
                            fixedForm.echoMap && fixedForm.echoMap[item.key]
                          "
                        >
                          <i
                            :style="{
                              color: fixedForm.echoMap[item.key].color,
                            }"
                            :class="[
                              'iconfont',
                              `${fixedForm.echoMap[item.key].icon}`,
                            ]"
                          />
                        </span>
                        <span v-else>
                          <svg
                            class="icon"
                            aria-hidden="true"
                            style="font-size: 24px"
                          >
                            <use :xlink:href="`#el-icon-icon-yixiang`" />
                          </svg>
                        </span>
                      </span>

                      <span v-else-if="item.key == 'handleBy'">
                        <span
                          v-if="
                            fixedForm.echoMap && fixedForm.echoMap[item.key]
                          "
                        >
                          <vone-user-avatar
                            :avatar-path="
                              fixedForm.echoMap[item.key].avatarPath
                            "
                            :avatar-type="true"
                            :show-name="false"
                            height="24px"
                            width="24px"
                          />
                        </span>
                        <span v-else>
                          <i
                            class="iconfont el-icon-icon-light-avatar"
                            style="font-size: 24px"
                          />
                        </span>
                      </span>
                      <span v-else>
                        <svg
                          class="icon"
                          aria-hidden="true"
                          style="font-size: 24px"
                        >
                          <use :xlink:href="`#${iconMap[item.key]}`" />
                        </svg>
                      </span>
                      <!-- 标签 -->
                      <span
                        v-if="item.type == 'SELECT' && item.key == 'tagId'"
                        style="padding-left: 12px"
                      >
                        <el-tooltip
                          v-if="fixedForm[item.key].length > 1"
                          :content="fixedForm[item.key].join(',')"
                          placement="top"
                        >
                          <span
                            v-for="(i, index) in fixedForm[item.key].slice(
                              0,
                              1
                            )"
                            :key="index"
                          >
                            <span>
                              <el-tag type="success" class="ml-10">{{
                                i
                              }}</el-tag>
                            </span>

                            &nbsp; &nbsp;
                            <el-tag
                              v-if="fixedForm[item.key].length > 1"
                              type="success"
                            >{{
                              ` + ${fixedForm[item.key].length - 1}`
                            }}</el-tag>
                          </span>
                        </el-tooltip>

                        <span v-else>
                          <span
                            v-for="(j, index) in fixedForm[item.key]"
                            :key="index"
                          >
                            <span>
                              <el-tag type="success" class="ml-10">{{
                                j
                              }}</el-tag>
                            </span>
                          </span>
                        </span>
                      </span>
                      <!-- 时间 -->
                      <span
                        v-else-if="item.type == 'DATE'"
                        style="padding-left: 12px"
                        class="dataPicker"
                      >
                        {{ fixedForm[item.key] && dayjs(fixedForm[item.key]).format('YYYY-MM-DD HH:mm'), }}
                      </span>
                      <!-- 状态 -->
                      <span
                        v-else-if="item.key == 'stateCode'"
                        style="
                          max-width: 190px;
                          padding-left: 12px;
                          overflow: hidden;
                          text-overflow: ellipsis;
                          white-space: nowrap;
                        "
                      >
                        {{
                          fixedForm[item.key] && fixedForm.echoMap[item.key]
                            ? fixedForm.echoMap[item.key].name
                            : "待处理"
                        }}
                      </span>
                      <span v-else style="padding-left: 12px">
                        <span v-if="fixedForm.echoMap[item.key]">
                          {{ fixedForm.echoMap[item.key].name }}
                        </span>
                      </span>
                    </el-form-item>
                  </div>

                  <!-- </el-col> -->
                </div>
              </div>
            </div>
          </el-form>
          <el-row>
            <el-col :span="16" class="centerBox">
              <div class="pContent">
                <el-skeleton
                  v-if="infoLoading"
                  style="width: 100%"
                  :loading="infoLoading"
                  animated
                  :count="8"
                >
                  <template slot="template">
                    <div style="padding: 14px">
                      <el-row type="flex" style="margin-top: 6px">
                        <el-skeleton-item
                          variant="p"
                          style="width: 50%; margin-right: 16px"
                        />
                        <el-skeleton-item variant="p" style="width: 50%" />
                      </el-row>
                    </div>
                  </template>
                </el-skeleton>
                <div v-else class="centerBasic">
                  <el-form
                    ref="otherForm"
                    :model="otherForm"
                    label-position="top"
                    :disabled="infoDisabled"
                  >
                    <el-row class="upDetail">
                      <el-col
                        v-for="item in basicProperty"
                        :key="item.id"
                        :span="24"
                      >
                        <el-form-item :label="item.name" :prop="item.key">
                          <!-- 文本编辑器 -->
                          <span v-if="item.type == 'EDITOR'">
                            <vone-editor
                              v-if="otherForm[item.key]"
                              ref="editor"
                              v-model="otherForm[item.key]"
                              :preview="infoDisabled"
                            />

                            <span v-else>--</span>
                          </span>

                          <!-- 文件 -->
                          <span v-else-if="item.type == 'FILE'">
                            <vone-upload
                              ref="uploadFile"
                              :biz-type="fileMap[typeCode]"
                              :files-data="otherForm.files"
                            />
                          </span>
                        </el-form-item>
                      </el-col>
                    </el-row>
                  </el-form>

                  <el-divider />
                  <h4>基本属性</h4>
                  <vone-desc :column="2">
                    <template v-for="item in customList">
                      <vone-desc-item :key="item.id" :label="`${item.name} :`">
                        <template>
                          <span
                            v-if="
                              customForm[item.key] &&
                                customForm.echoMap[item.key] &&
                                item.isBuilt
                            "
                          >
                            <vone-user-avatar
                              v-if="item.type == 'USER'"
                              :avatar-path="
                                customForm.echoMap[item.key].avatarPath
                              "
                              :avatar-type="
                                customForm.echoMap[item.key].avatarType
                              "
                              :name="customForm.echoMap[item.key].name"
                              height="20px"
                              width="20px"
                            />

                            <span
                              v-else-if="item.type == 'SELECT' && item.multiple"
                            >
                              {{
                                customForm.echoMap[item.key]
                                  .map((r) => r.name)
                                  .join(",")
                              }}
                            </span>
                            <span v-else>{{
                              customForm.echoMap[item.key].name
                            }}</span>
                          </span>
                          <span
                            v-else-if="customForm[item.key] && !item.isBuilt"
                          >
                            <span
                              v-if="
                                item.type == 'SELECT' &&
                                  !item.multiple &&
                                  item.options
                              "
                            >
                              {{
                                item.options.find(
                                  (r) => r.id == customForm[item.key]
                                )
                                  ? item.options.find(
                                    (r) => r.id == customForm[item.key]
                                  ).name
                                  : customForm[item.key]
                              }}
                            </span>
                            <span
                              v-else-if="item.type == 'SELECT' && item.multiple"
                            >
                              {{ customForm[item.key] }}
                            </span>
                            <!-- 人员 -->
                            <span
                              v-else-if="
                                item.type == 'USER' ||
                                  (item.type == 'PROJECTUSER' &&
                                    customForm.echoMap[item.key].length)
                              "
                            >
                              <vone-user-avatar
                                v-for="user in customForm.echoMap[item.key]"
                                :key="user.id"
                                :avatar-path="user.avatarPath"
                                :avatar-type="user.avatarType"
                                :name="user.name"
                                height="20px"
                                width="20px"
                              />
                            </span>
                            <!-- 机构 -->
                            <span v-else-if="item.type == 'ORG'">
                              {{
                                customForm.echoMap[item.key]
                                  .map((r) => r.name)
                                  .join(",")
                              }}
                            </span>

                            <!-- 文件 -->
                            <vone-upload
                              v-else-if="item.type == 'FILE'"
                              ref="formUploadFile"
                              :files-data="customForm.echoMap[item.key]"
                              :biz-type="fileMap[typeCode]"
                              :class-name="'hidebtn'"
                            />

                            <!-- 关联 -->
                            <dataSelect
                              v-else-if="item.type == 'LINKED'"
                              text-info="info"
                              :model.sync="customForm[item.key]"
                              :config="item"
                            />

                            <!-- 引用 -->
                            <div v-else-if="item.type == 'QUOTE'">
                              <vone-user-avatar
                                v-if="
                                  item.quoteType === 'user' &&
                                    userMap[customForm[item.key]]
                                "
                                :avatar-path="
                                  userMap[customForm[item.key]].avatarPath
                                "
                                :avatar-type="true"
                                height="22px"
                                width="22px"
                                :name="userMap[customForm[item.key]].name"
                                :show-name="true"
                              />
                              <span v-else> {{ customForm[item.key] }} </span>
                            </div>
                            <!-- 超链接 -->
                            <el-tooltip
                              v-else-if="item.type == 'LINK'"
                              class="item"
                              effect="dark"
                              :content="customForm[item.key]"
                              placement="top-start"
                            >
                              <span
                                class="file-name"
                                @click="fileClick(customForm[item.key])"
                              >{{ customForm[item.key] }}</span>
                            </el-tooltip>
                            <span v-else>
                              {{ customForm[item.key] }}
                            </span>

                            <!-- {{ customForm.echoMap[item.key] }} -->
                          </span>

                          <span v-else>
                            {{ customForm[item.key] || "--" }}
                          </span>
                        </template>
                      </vone-desc-item>
                    </template>
                  </vone-desc>

                  <!-- <el-form ref="customForm" :model="customForm" label-position="top" :disabled="infoDisabled" class="custom" label-width="110px">
                        <el-row :gutter="24">
                          <el-col v-for="item in customList" :key="item.id" :span="12">
                            <el-form-item :label="`${item.name} :`" :prop="item.key" />

                          </el-col>
                        </el-row>
                      </el-form> -->
                </div>
              </div>
            </el-col>

            <el-col v-loading="infoLoading" :span="8" class="rightBox">
              <el-tabs
                v-model="rightTabActive"
                class="vone-tab-line mintabline"
                @tab-click="handleClick($event, rightTabs)"
              >
                <el-tab-pane
                  v-for="tab in rightTabs"
                  :key="tab.name"
                  :label="tab.label"
                  :name="tab.name"
                >
                  <vone-comment
                    v-if="rightTabActive == 'comment' && tab.active"
                    height="calc(60vh - 20px)"
                    :source-id="commentId"
                    :source-type="typeCode"
                  />
                  <activeTab
                    v-if="rightTabActive == 'active' && tab.active"
                    :form-id="id"
                    :type="typeCode"
                    height="calc(60vh - 20px)"
                    :source-type="typeCode"
                    :source-id="commentId"
                    :view-type="'detail'"
                  />
                  <activityRecord
                    v-if="rightTabActive == 'activityRecord' && tab.active"
                    :form-id="id"
                    :type="typeCode"
                    :source-type="typeCode"
                    height="calc(60vh - 40px)"
                    :source-id="commentId"
                  />
                  <workTime
                    v-if="rightTabActive == 'workTime' && tab.active"
                    height="calc(60vh - 40px)"
                    :source-id="commentId"
                    :source-type="typeCode"
                    :project-id="fixedForm.projectId"
                    :row-type-code="rowTypeCode"
                  />
                </el-tab-pane>
              </el-tabs>
            </el-col>
          </el-row>
        </div>

        <!-- <div class="footer-btn">
          <el-button @click="onClose">关闭</el-button>
        </div> -->
      </div>
    </el-card>

    <!-- 新增 -->
    <vone-custom-add
      v-if="issueAddParam.visible"
      ref="vone-custom-add"
      :key="issueAddParam.key"
      :visible.sync="issueAddParam.visible"
      v-bind="issueAddParam"
      :type-code="globalTypeCode"
      :is-tooltip="true"
      :title="issueAddParam.title"
      @success="saveChildSuccess"
    />
  </div>
</template>

<script>
import _ from 'lodash'
import storage from 'store'

const fileMap = {
  ISSUE: 'ISSUE_FILE_UPLOAD',
  BUG: 'BUG_FILE_UPLOAD',
  TASK: 'TASK_FILE_UPLOAD',
  RISK: 'RISK_FILE_UPLOAD',
  IDEA: 'IDEA_FILE_UPLOAD',
  TESTREQ: 'TESTREQ_FILE_UPLOAD'
}

const iconMap = {
  tagId: 'el-icon-icon-fill-biaoqian',
  planStime: 'el-icon-icon-fill-wanchengshijian',
  startTime: 'el-icon-icon-fill-wanchengshijian',
  planEtime: 'el-icon-icon-fill-wanchengshijian',
  endTime: 'el-icon-icon-fill-wanchengshijian',
  expectedTime: 'el-icon-icon-fill-wanchengshijian',
  stateCode: 'el-icon-icon-fill-zhuangtai'
}

import { apiAlmGetInfo } from '@/api/vone/project/issue'
import {
  apiVaBaseCustomFormField,
  apiVaBaseCustomFormFieldProgram,
  apiVaBaseCustomFormFieldProject
} from '@/api/vone/base/customForm'
import { apiBaseAllUserNoPage } from '@/api/vone/base/user'
import { getSourceById } from '@/api/vone/base/source'

// 处理-连接拼接成驼峰命名
function toHump(str) {
  const reg = /-(\w)/g
  return str.replace(reg, function($0, $1) {
    return $1.toUpperCase()
  })
}

// 处理首字母大写
function upperFirst(str) {
  return str.charAt(0).toUpperCase() + str.slice(1)
}

// 读取当前文件夹下components文件夹下.vue文件
const requireComponents = require.context(
  '@/components/CustomEdit/commonTab/',
  false,
  /\.vue$/
)

const componentsObj = {}
requireComponents.keys().forEach((filePath) => {
  const componentName = upperFirst(
    toHump(filePath.split('/')[1].replace(/\.vue$/, ''))
  )
  const componentConfig = requireComponents(filePath)
  componentsObj[componentName] = componentConfig.default || componentConfig
})
import workTime from '@/components/CustomEdit/commonTab/work-time.vue'
import activeTab from '@/components/CustomEdit/commonTab/active.vue'
import dataSelect from '@/components/CustomEdit/components/data-select.vue'
import activityRecord from '@/components/CustomEdit/commonTab/activity-record.vue'

export default {
  name: 'IssueInfo',
  components: {
    activityRecord,
    dataSelect,
    activeTab,
    workTime,
    ...componentsObj
  },
  props: {
    workId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      // 下面是属性props的参数，调整成从local获取吧。
      visible: false, // 是否显示弹框。目前改造成路由，不需要了
      title: '', // 页面标题
      id: undefined, // issue id
      infoDisabled: false, // form表单是否可以编辑
      sprintId: undefined, // 1
      tableList: [], // 需求列表，用来切换上一个和下一个
      typeCode: undefined, // ISSUE
      leftTabs: [], // 左侧Tabs
      rightTabs: [], // 右侧Tabs
      formId: undefined, // 平台配置,预览表单接口,需要根据formId查询模板
      rowTypeCode: undefined, // 需求中心/项目里,从列表数据取到的类型
      stateCode: undefined, // 需求中心/项目里,从列表数据取到的状态
      rowProjectId: undefined, // 需求中心,从列表数据取到的关联项目的项目id
      showPopupForSplit: false, // 拆分子项是否弹框
      isShowCreateBtn: true, // 是否显示新建按钮
      // 原有data
      iconMap,
      drawerLoading: false,
      fileMap,
      tabActive: 'basic',
      rightTabActive: 'comment',
      currentIndex: undefined, // 当前数据的索引
      customForm: {},
      fixedForm: {},
      otherForm: {},
      basicProperty: [],
      fixedProperty: [],
      customList: [],
      infoLoading: true,
      commentId: '',
      userMap: {},
      // 新增模块的内容
      globalTypeCode: 'ISSUE',
      issueAddParam: {
        // 新增
        visible: false
      }
    }
  },
  watch: {
    workId: {
      deep: true,
      handler() {
        this.dataChange()
      }
    }
  },
  created() {
    const customInfo = storage.get('customInfo')
      ? JSON.parse(storage.get('customInfo'))
      : {}
    // 下面是属性props的参数，调整成从local获取吧。
    this.visible = customInfo.visible || false
    this.title = customInfo.title || ''
    this.id = customInfo.id || undefined
    this.infoDisabled = customInfo.infoDisabled || false
    this.sprintId = customInfo.sprintId || undefined
    this.tableList = customInfo.tableList || []
    this.typeCode = customInfo.typeCode || undefined
    this.leftTabs = customInfo.leftTabs || []
    this.rightTabs = customInfo.rightTabs || []
    this.formId = customInfo.formId || undefined
    this.rowTypeCode = customInfo.rowTypeCode || undefined
    this.stateCode = customInfo.stateCode || undefined
    this.rowProjectId = customInfo.rowProjectId || undefined
    this.showPopupForSplit = customInfo.showPopupForSplit || false
    this.isShowCreateBtn = customInfo.isShowCreateBtn || true
  },
  mounted() {
    this.commentId = this.id || ''
    this.$nextTick(() => {
      this.rightTabActive = this.rightTabs[0]?.name
      this.rightTabs.forEach((v) => {
        if (v.name == this.rightTabActive) {
          v.active = true
        } else {
          v.active = false
        }
      })
      this.getFormTemplete()
    })

    this.currentIndex = this.tableList.findIndex((item) => item.id === this.id)

    this.getUserList()

    this.leftTabs.forEach((element) => {
      element.active = false
    })
  },
  methods: {
    fileClick(link) {
      if (link && (_.startsWith(link, 'http') || _.startsWith(link, 'https'))) {
        window.open(link, '_blank')
      } else {
        window.open('http://' + link, '_blank')
      }
    },
    async getQuoteType() {
      this.customList.forEach((e, index) => {
        if (e.type == 'QUOTE') {
          const config = JSON.parse(e.config)
          this.getTableConfig(config.relationShipsheet, e, (item) => {
            e.quoteType = item?.type
            this.$nextTick(() => {
              this.$set(this.customList, index, e)
            })
          })
        }
      })
    },
    async getTableConfig(id, e, callback) {
      const config = JSON.parse(e.config)
      const res = await getSourceById(id)
      if (res.isSuccess) {
        const list = res.data.fields.map((v) => {
          v.prop = v.id
          const obj = JSON.parse(v.config)
          return {
            ...v,
            ...obj
          }
        })
        const objs = list.find(
          (e) => e.id == config.relationField && !e.primary
        )
        callback(objs)
      }
    },
    async getUserList() {
      const res = await apiBaseAllUserNoPage()
      if (!res.isSuccess) {
        return
      }
      this.userMap = res.data.reduce((acc, cur) => {
        acc[cur.id] = cur
        return acc
      }, {})
    },
    async getFormTemplete(val) {
      this.drawerLoading = true
      // 需求中心，项目，项目集，查询表单项时调不同的接口url，参数格式也不同
      // 需求中心和其它使用：apiVaBaseCustomFormField
      // 项目使用：apiVaBaseCustomFormFieldProject
      // 项目集：apiVaBaseCustomFormFieldProgram
      const activeApp = this.$route.meta.activeApp
      const params = {
        typeCode: this.rowTypeCode || this.$route.query.rowTypeCode,
        stateCode: this.stateCode,
        projectId: this.rowProjectId || null
      }
      const res =
        activeApp == 'projectm'
          ? await apiVaBaseCustomFormFieldProgram(
            this.$route.params.id,
            this.typeCode,
            params
          )
          : activeApp == 'project'
            ? await apiVaBaseCustomFormFieldProject(
              this.$route.params.id,
              this.typeCode,
              params
            )
            : await apiVaBaseCustomFormField(this.typeCode, params)

      this.drawerLoading = false
      if (!res.isSuccess) {
        return
      }

      res.data.forEach((element) => {
        element.placeholder = JSON.parse(element.config)?.placeholder
        element.multiple = JSON.parse(element.config)?.multiple
        element.message = JSON.parse(element.config)?.message
        element.validator = JSON.parse(element.config)?.validator
        element.defaultTime = JSON.parse(element.config)?.defaultTime
        element.disabled = element.key == 'planId'
        element.options = JSON.parse(element.config)?.options
        element.type =
          element.type && element.type.code != 'CUSTOM'
            ? element.type.code
            : JSON.parse(element.config)?.customType
      })

      // 所有基本属性
      const basicAll =
        res.data && res.data.length
          ? res.data.filter((r) => r.isBasic && r.isShow && r.state)
          : []

      // 其它基本属性
      const other = basicAll.filter(
        (r) => r.key == 'files' || r.key == 'description'
      )

      // 固定基本属性
      const fixed = _.difference(basicAll, other)

      // 固定基本属性
      this.fixedProperty = fixed.sort(function(a, b) {
        return a.sort - b.sort
      })
      // 固定属性[文件和描述]
      this.basicProperty = other.sort(function(a, b) {
        return a.sort - b.sort
      })
      // 排序
      // 自定义属性
      const custom =
        res.data && res.data.length
          ? res.data.filter((r) => !r.isBasic && r.isShow && r.state)
          : []
      this.customList = custom.sort(function(a, b) {
        return a.sort - b.sort
      })

      await this.getIssueInfo(val)
      this.getQuoteType()
    },
    initList() {
      this.$emit('initList')
    },

    onClose() {
      this.splitFlag = false
      const params = this.$route.params || {}
      this.$router.push(
        `/project/issue/${params.projectKey}/${params.projectTypeCode}/${params.id}`
      )
      this.$emit('update:visible', false)
    },
    handleClick(event, list) {
      list.forEach((v) => {
        if (v.name == event.name) {
          v.active = true
        } else {
          v.active = false
        }
      })
    },

    // 详情
    async getIssueInfo(val) {
      this.infoLoading = true
      this.drawerLoading = true

      const urlMap = {
        ISSUE: `/api/alm/alm/requirement/${val || this.id}`,
        BUG: `/api/alm/alm/bug/${val || this.id}`,
        TASK: `/api/alm/alm/task/${val || this.id}`,
        RISK: `/api/alm/alm/risk/${val || this.id}`,
        IDEA: `/api/alm/alm/idea/${val || this.id}`,
        TESTREQ: `/api/alm/alm/testreq/${val || this.id}`
      }
      const res = await apiAlmGetInfo(urlMap[this.typeCode])

      if (!res.isSuccess) {
        return
      }

      if (res.data?.tagId?.length && res.data.echoMap?.tagId) {
        this.$set(
          res.data,
          'tagId',
          res.data.echoMap.tagId.map((r) => r.name) || []
        )
      }
      this.customForm = res.data
      this.fixedForm = res.data
      this.otherForm = res.data

      this.infoLoading = false
      this.drawerLoading = false
      // 如果需求没有关联项目,不允许填报工时
      this.$emit('hideTab', res.data.projectId ? 1 : 0)
    },

    // 上一条
    dataPrev() {
      if (this.currentIndex === 0) {
        this.$message.warning('已经是第一条数据啦')
        return
      }
      this.currentIndex--
      this.getFormTemplete(this.tableList[this.currentIndex].id)
      this.commentId = this.tableList[this.currentIndex].id
    },
    // 下一条
    dataNext() {
      if (this.currentIndex === this.tableList.length - 1) {
        this.$message.warning('已经是最后一条数据啦')
        return
      }
      this.currentIndex++

      this.getFormTemplete(this.tableList[this.currentIndex].id)
      this.commentId = this.tableList[this.currentIndex].id
    },
    dataChange() {
      this.getFormTemplete(this.workId)
      this.commentId = this.workId
    },
    /**
     * 新增模块的内容
     */
    addChild(type) {
      console.log('addChild执行了', type)
      if (type == 'issue') {
        this.globalTypeCode = 'ISSUE'
        this.issueAddParam = {
          visible: true,
          key: Date.now(),
          infoDisabled: false,
          title: '新增需求'
        }
      } else if (type == 'task') {
        this.globalTypeCode = 'TASK'
        this.issueAddParam = {
          visible: true,
          key: Date.now(),
          infoDisabled: false,
          title: '新增任务'
        }
      } else if (type == 'bug') {
        this.globalTypeCode = 'BUG'
        this.issueAddParam = {
          visible: true,
          key: Date.now(),
          infoDisabled: false,
          title: '新增缺陷'
        }
      }

      setTimeout(async() => {
        // 获取需求详情
        const res = await apiAlmGetInfo(`/api/alm/alm/requirement/${this.id}`)

        // 基本信息赋值
        const fixdForm = this.$refs['vone-custom-add']?.fixdForm
        this.$set(fixdForm, 'name', res.data.name)
        this.$set(fixdForm, 'planEtime', res.data.planEtime)
        this.$set(fixdForm, 'description', res.data.description)

        // 自定义组件集合
        const customList = this.$refs['vone-custom-add'].customList || []
        // 基本属性赋值
        const form = this.$refs['vone-custom-add']?.form
        if (type === 'issue') {
          this.$set(form, 'parentId', res.data.id)
          this.$set(form, 'sourceCode', res.data.sourceCode)
          this.$set(form, 'planStime', res.data.planStime)
          this.$set(form, 'planId', res.data.planId)
          this.$set(form, 'productVersionId', res.data.productVersionId)
          this.$set(
            form,
            'productModuleFunctionId',
            res.data.productModuleFunctionId
          )
        } else if (type === 'task') {
          this.$set(
            form,
            'sourceCode',
            customList.find((item) => item.key === 'sourceCode')?.options?.[0][
              'code'
            ]
          )
          this.$set(form, 'planStime', res.data.planStime)
          this.$set(form, 'planId', res.data.planId)
          this.$set(form, 'productVersionId', res.data.productVersionId)
          this.$set(
            form,
            'productRepairVersionId',
            res.data.productRepairVersionId
          )
          this.$set(form, 'requirementId', res.data.id)
          this.$set(form, 'c4', res.data.putBy)
        } else if (type === 'bug') {
          this.$set(form, 'requirementId', res.data.id)
          this.$set(form, 'planStime', res.data.planStime)
          this.$set(form, 'planId', res.data.planId)
          this.$set(
            form,
            'sourceCode',
            customList.find((item) => item.key === 'sourceCode')?.options?.[0][
              'code'
            ]
          )
          this.$set(
            form,
            'envCode',
            customList.find((item) => item.key === 'envCode')?.options?.[0][
              'id'
            ]
          )
        }
      }, 500)
    },
    saveChildSuccess() {
      // this.getInitTableData({})
      // this.$refs['vone-custom-info']?.$refs['IssueToIssue']?.[0]?.getChildrenTable()
      // this.$refs['vone-custom-info']?.$refs['TaskTab']?.[0]?.getTableData()
      // this.$refs['vone-custom-info']?.$refs['BugTab']?.[0]?.getTableData()
      // this.$refs['vone-custom-edit']?.$refs['IssueToIssue']?.[0]?.getChildrenTable()
      // this.$refs['vone-custom-edit']?.$refs['TaskTab']?.[0]?.getTableData()
      // this.$refs['vone-custom-edit']?.$refs['BugTab']?.[0]?.getTableData()

      this.$refs['IssueToIssue']?.[0]?.getChildrenTable()
      this.$refs['TaskTab']?.[0]?.getTableData()
      this.$refs['BugTab']?.[0]?.getTableData()
    }
  }
}
</script>

<style scoped lang="scss">
// 改动的css：1. 注释了dialog相关的内容；2.原有的样式，避免css污染，放在了issue-info里面。3.修改centerBox和rightBox的height
.issue-info {
  height: calc(100vh - 20px - 48px - 58px);

  & > .el-card {
    height: 100%;
  }

  .footer-btn {
    text-align: right;
  }

  // 原有的样式，避免css污染，放在了issue-info里面。
  ::v-deep .el-tabs--left .el-tabs__header.is-left {
    margin-right: 0;
  }

  //::v-deep .el-dialog .el-dialog__body {
  //  padding: 0;
  //  overflow-y: hidden;
  //  height: calc(90vh - 115px);
  //}

  .drawerCode {
    color: var(--auxiliary-font-color);
  }

  .basicHeader {
    background-color: #fff;
    height: 120px;
    padding: 8px 16px;
    border-bottom: 1px solid var(--solid-border-color);
    margin: 0 !important;

    .name-row {
      .el-col-24 {
        ::v-deep .el-input--small .el-input__inner {
          height: 36px;
          line-height: 36px;
          border: none;
          background: none;
          font-weight: 600;
          font-size: 16px;

          &:hover {
            background-color: #eaecf0;
          }

          &:focus {
            background-color: #fff;
            border: 1px solid #3e7bfa;
          }
        }
      }
    }

    .basica-form {
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 90%;

      .fixedItem {
        width: 100%;
        padding: 8px;
        flex: 1;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        padding-left: 10px;

        i,
        .svg-icon {
          font-size: 24px;
        }

        ::v-deep .el-select__tags {
          height: 22px;
          line-height: 22px;
          flex-wrap: nowrap;
        }

        ::v-deep .el-input {
          height: 22px;
          line-height: 22px;
        }

        ::v-deep .el-input--small .el-input__inner {
          font-size: 14px;
          border: none;
          background: none;
          height: 22px;
          line-height: 22px;
          // color: var(--main-font-color);
        }

        ::v-deep .is-disabled {
          color: var(--input-border-color);
        }

        ::v-deep .el-input__icon {
          line-height: 22px;
        }

        ::v-deep .el-input__suffix {
          display: none;
        }

        .dataPicker {
          min-width: 155px;

          ::v-deep .el-input__prefix {
            display: none;
          }

          ::v-deep .el-input__inner {
            padding-left: 12px;
          }
        }

        ::v-deep .el-form-item {
          margin-bottom: 0;

          .el-form-item__label {
            color: var(--auxiliary-font-color);
            height: 22px;
            line-height: 22px;
            padding: 0 0 4px 0px;
          }

          .el-form-item__content {
            line-height: 22px;
            display: flex;
            align-items: center;
          }
        }
      }

      .fixedItem:hover {
        background-color: #f2f3f5;
      }

      .fixedItemType:hover {
        background-color: #fff;
      }
    }
  }

  .centerBox {
    border-right: 1px solid var(--solid-border-color);
    height: calc(100vh - 300px);
    overflow: hidden;
    overflow-y: auto;
    .contentBox {
      padding: 16px;
      //120header+ 20title +cardPadding 20+20 + btn32 + nav48 + 48tabHeader + 20mainPadding
      height: calc(100vh - 282px);
      height: calc(100vh - 120px - 20px - 40px - 32px - 48px - 48px - 20px);
      overflow-y: auto;
    }
  }

  .rightBox {
    height: calc(100vh - 235px);
    height: calc(100vh - 235px - 45px);
    overflow-y: auto;

    ::v-deep .el-tabs__header {
      margin: 0;

      .el-tabs__item {
        height: 48px;
        line-height: 48px;
      }
    }

    ::v-deep .el-tabs__item.is-active {
      color: var(--main-theme-color);
    }
  }

  .border {
    border-left: 4px solid var(--main-theme-color);
    padding-left: 10px;
    height: 24px;
    line-height: 24px;
    font-size: 16px;
  }

  ::v-deep .el-tabs--border-card > .el-tabs__content {
    padding: 0;
  }

  ::v-deep .el-date-editor.el-input {
    width: 100%;
  }

  ::v-deep .el-tabs__nav-wrap .is-top {
    .el-tabs__nav-scroll {
      padding-left: 15px;
    }
  }

  ::v-deep .el-input.is-disabled .el-input__inner {
    color: var(--main-font-color) !important;
  }

  ::v-deep .el-form-item__label {
    color: var(--auxiliary-font-color) !important;
  }

  .custom {
    ::v-deep .el-form-item__content {
      color: var(--main-font-color) !important;
    }
  }

  //::v-deep .el-dialog .el-dialog__body {
  //  // height: 708px;
  //  overflow-y: hidden;
  //  padding: 0;
  //}

  ::v-deep .el-tabs__nav-wrap::after {
    height: 1px;
  }

  .mainFont {
    color: var(--col-form-border);
    margin-left: 10px;
    cursor: not-allowed;
  }

  .ml-10 {
    margin-left: 10px;
    cursor: not-allowed;
  }

  .upDetail {
    ::v-deep .el-form-item__label {
      color: var(--font-main-color) !important;
      font-weight: 500;
    }
  }

  ::v-deep .el-divider {
    margin: 0px;
  }

  h4 {
    color: var(--font-main-color);
    margin: 16px 0px 0px 0px;
  }

  ::v-deep .card-wrapper-header {
    margin-top: 0px !important;
  }

  .file-name {
    display: inline-block;
    width: 100%;
    color: #3e7bfa;
    cursor: pointer;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    vertical-align: middle;
  }

  // 原有样式结束
}
</style>
