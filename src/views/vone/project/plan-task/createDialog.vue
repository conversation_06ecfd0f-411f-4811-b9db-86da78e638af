<template>
  <el-dialog :title="diaTitle" width="800px" :visible="visible" append-to-body :before-close="onClose" :close-on-click-modal="false" v-on="$listeners">
    <el-form ref="dialogForm" :model="dialogForm" label-position="top" :rules="rules">
      <!-- <el-row>
        <el-form-item v-if="!dialogType" prop="type">
          <el-radio-group v-model="dialogForm.type">
            <el-radio label="MILESTONE"><svg class="icon" aria-hidden="true"><use xlink:href="#el-icon-lichengbei" /></svg>里程碑</el-radio>
            <el-radio label="PLAN"><svg class="icon" aria-hidden="true"><use xlink:href="#el-icon-jihua" /></svg>计划</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-row> -->
      <el-row>
        <el-col :span="24">
          <el-form-item label="名称" prop="name">
            <el-input v-model.trim="dialogForm.name" placeholder="请输入名称" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="16">
        <el-col :span="12">
          <el-form-item label="负责人" prop="leadingBy">
            <vone-icon-select v-model="dialogForm.leadingBy" select-type="user" clearable filterable :data="pUserList" style="width:100%">
              <el-option v-for="item in pUserList" :key="item.id" :label="item.name" :value="item.id">
                <vone-user-avatar v-if="item.avatarPath" :avatar-path="item.avatarPath" :avatar-type="item.avatarType" :show-name="false" height="22px" width="22px" />
                {{ item.name }}
              </el-option>
            </vone-icon-select>
            <!-- <vone-remote-user v-model="dialogForm.leadingBy" /> -->
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="状态" prop="stateCode">
            <el-select v-model="dialogForm.stateCode" placeholder="请选择" style="width: 100%">
              <el-option label="未开始" value="1" style="color:#adb0b8;">
                <i style="display:inline-block;width:10px;height:10px;border-radius:50%;border: 2px solid #adb0b8;" /> 未开始
              </el-option>
              <el-option label="进行中" value="2" style="color:#64befa;">
                <i style="display:inline-block;width:10px;height:10px;border-radius:50%;border: 2px solid #64befa;" /> 进行中
              </el-option>
              <el-option label="已完成" value="3" style="color:#3cb540;">
                <i style="display:inline-block;width:10px;height:10px;border-radius:50%;border: 2px solid #3cb540;" /> 已完成
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="16">
        <el-col v-if="dialogForm.type!=='MILESTONE'" :span="12">
          <el-form-item label="开始日期" prop="planStime">
            <el-date-picker v-model="dialogForm.planStime" type="date" placeholder="请选择开始日期" value-format="yyyy-MM-dd" :picker-options="pickerOptionsStart" :editable="false" style="width:100%;" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="完成日期" prop="planEtime">
            <el-date-picker v-model="dialogForm.planEtime" type="date" placeholder="请选择完成日期" value-format="yyyy-MM-dd" :picker-options="pickerOptions" :editable="false" style="width:100%;" @change="changeDate" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="描述" prop="description">
            <el-input v-model="dialogForm.description" type="textarea" :rows="2" placeholder="请输入描述" maxlength="255" show-word-limit />
          </el-form-item>
        </el-col>
      </el-row>

    </el-form>

    <div slot="footer" style="text-align:right">
      <el-button @click="onClose">取消</el-button>
      <el-button :loading="saveLoading" type="primary" @click="confirm">确定</el-button>
    </div>
  </el-dialog>
</template>

<script>

import { apiAlmProjectPlanAdd } from '@/api/vone/project/iteration'
import { apiProjectUserNoPage } from '@/api/vone/project/index'
import { catchErr } from '@/utils'
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    parentId: {
      type: String,
      default: undefined
    },
    title: {
      type: String,
      default: ''
    },
    dialogType: {
      type: String,
      default: ''
    }
  },
  data() {
    var validatePass = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请输入开始时间'))
      } else if (this.dialogForm.planEtime && value > this.dialogForm.planEtime) {
        callback(new Error('开始日期不能大于结束日期!'))
      } else {
        callback()
      }
    }
    return {
      pickerOptionsStart: {
        disabledDate: (time) => {
          if (this.dialogForm.planEtime) {
            return time.getTime() > new Date(this.dialogForm.planEtime).getTime()
          }
        }
      },
      pickerOptions: {
        disabledDate: (time) => {
          if (this.dialogForm.planStime) {
            return time.getTime() < new Date(this.dialogForm.planStime).getTime()
          }
        }
      },
      dialogForm: {
        name: '',
        type: 'PLAN',
        leadingBy: '',
        stateCode: '',
        planStime: '',
        planEtime: '',
        description: ''
      },
      rules: {
        name: [{ required: true, message: '请输入名称' }, { pattern: '^.{1,50}$', message: '请输入不超过50个字符组成的名称' }],
        type: [{ required: true, message: '请选择类型' }],
        planStime: [{ validator: validatePass, required: true }],
        planEtime: [{ required: true, message: '请选择结束日期' }],
        leadingBy: [{ required: true, message: '请选择负责人' }],
        stateCode: [{ required: true, message: '请选择状态' }]
      },
      saveLoading: false,
      pUserList: []
    }
  },
  computed: {
    diaTitle() {
      if (this.dialogForm.type) return '新建' + (this.dialogForm.type == 'PLAN' ? '计划' : '里程碑')
      return this.dialogType ? this.title : '新建'
    }
  },
  mounted() {
    this.dialogForm.type = 'PLAN'
    this.getProjectUser()
  },
  methods: {
    // 查询项目集下人员
    async getProjectUser() {
      const [res, err] = await catchErr(apiProjectUserNoPage({
        projectId: this.$route.params.id
      }))
      if (err) return
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.pUserList = res.data
    },
    changeDate(v) {
      if (this.dialogForm.planStime < v) {
        this.$refs['dialogForm'].clearValidate('planStime')
      }
    },
    onClose() {
      this.$emit('update:visible', false)
      this.$refs.dialogForm.resetFields()
    },

    async confirm() {
      try {
        await this.$refs.dialogForm.validate()
      } catch (error) {
        return
      }

      this.saveLoading = true
      const params = {
        ...this.dialogForm,
        parentId: this.parentId,
        projectId: this.$route.params.id
        // type: this.dialogForm.type
      }
      const [{ isSuccess, msg }, err] = await catchErr(apiAlmProjectPlanAdd(params))
      this.saveLoading = false
      if (err) return

      if (!isSuccess) {
        this.$message.warning(msg)
        return
      }
      this.$message.success('保存成功')
      this.$emit('success')
      this.onClose()
    }
  }
}
</script>
