<template>
  <div>
    <div class="pageContentNoH" style="margin-bottom: 10px">
      <div class="title-header">根据模板新建</div>
      <el-row type="flex" style="flex-wrap: wrap">
        <div
          v-for="item in templeteList"
          :key="item.id"
          style="margin-right: 16px; margin-top: 12px"
        >
          <a @click="fastAdd(item.code)">
            <div
              :class="[item.code]"
              class="cardBox"
              :style="{
                width: item.code == 'DEVOPS' ? '180px' : '160px',
                background: `linear-gradient(to right, #f7f7f7, ${item.color})`
              }"
            >
              <span class="flexCard">
                <svg class="icon" aria-hidden="true">
                  <use :xlink:href="'#' + `${item.icon}`" />
                </svg>
                <div class="itemName">{{ item.name }}</div>
              </span>
              <div class="itemBtn">创建</div>
            </div>
          </a>
        </div>
      </el-row>
    </div>
    <div class="pageContentNoH mainheight">
      <vone-search-wrapper>
        <template slot="search">
          <vone-search-dynamic
            ref="searchForm"
            :show-card="true"
            :card-options="cardOptions"
            table-search-key="project-table"
            :model.sync="formData"
            :extra.sync="extraData"
            :default-fileds.sync="defaultFileds"
            show-basic
            :default-filter-collection="true"
            @getTableData="getProjectList"
          />
        </template>
        <template slot="actions">
          <el-button
            icon="iconfont el-icon-tips-plus-circle"
            type="primary"
            :disabled="!$permission('alm:projectInfo:add')"
            @click="addNewProject"
          >新增</el-button>
        </template>
        <template slot="fliter">
          <vone-search-filter
            :extra.sync="extraData"
            :model.sync="formData"
            :default-fileds.sync="defaultFileds"
            @getTableData="getProjectList"
          />
        </template>
      </vone-search-wrapper>
      <div v-loading="tableLoading" style="margin-top: -8px">
        <div
          v-if="extraData.cardView == 'table' "
          style="height: calc(100vh - 252rem)"
        >
          <vxe-table
            ref="projectTable"
            class="vone-vxe-table"
            border
            height="auto"
            show-overflow="tooltip"
            :loading="tableLoading"
            :empty-render="{ name: 'empty' }"
            :data="tableData.records"
            :column-config="{ resizable: true, minWidth: 120 }"
            :checkbox-config="{ reserve: true }"
            row-id="id"
          >
            <vxe-column title="名称">
              <template #default="{ row }">
                <div class="flexRow" :class="row.top ? 'vCard' : ''">
                  <a
                    @click="
                      itemProject(row.code, row.typeCode, row.id, row.name, row)
                    "
                  >{{ row.name }}</a>

                  <div @click.stop="focus(row)">
                    <i
                      v-if="row.collection"
                      style="color: #ffc642; font-size: 18px"
                      class="iconfont el-icon-icon-shoucang-on"
                    />
                    <i
                      v-else
                      class="el-icon-star-off"
                      style="font-size: 18px"
                    />
                  </div>
                </div>
              </template>
            </vxe-column>
            <vxe-column title="标识" field="code" width="100" />
            <vxe-column title="项目经理" field="leadingBy" width="120">
              <template #default="{ row }">
                <span
                  v-if="row.leadingBy && row.echoMap && row.echoMap.leadingBy"
                >
                  <vone-user-avatar
                    :avatar-path="row.echoMap.leadingBy.avatarPath"
                    :name="row.echoMap.leadingBy.name"
                  />
                </span>
                <i
                  v-else-if="!row.leadingBy"
                  class="iconfont el-icon-application-member"
                />
                <span v-else> -- </span>
              </template>
            </vxe-column>
            <vxe-column title="状态" field="isClosure" width="120">
              <template #default="{ row }">
                <span v-if="row.projectStage">
                  <el-tag>
                    {{ row.projectStage.desc }}
                  </el-tag>
                </span>
              </template>
            </vxe-column>
            <vxe-column title="类型" field="typeCode" width="80">
              <template #default="{ row }">
                <el-tag
                  v-if="row.typeCode && row.echoMap && row.echoMap.typeCode"
                  effect="dark"
                  color="success"
                  size="mini"
                >{{ row.echoMap.typeCode.name }}</el-tag>
                <span v-else>--</span>
              </template>
            </vxe-column>
            <vxe-column title="操作" fixed="right" align="left" width="120">
              <template #default="{ row }">
                <el-tooltip class="item" content="编辑" placement="top">
                  <el-button
                    type="text"
                    :disabled="!$permission('project_edit')"
                    icon="iconfont el-icon-application-edit"
                    @click="editProject(row)"
                  />
                </el-tooltip>
                <el-divider direction="vertical" />
                <el-tooltip
                  v-if="!row.top"
                  class="item"
                  content="置顶"
                  placement="top"
                >
                  <el-button
                    type="text"
                    icon="iconfont el-icon-application-top-back"
                    @click="setProjectTop(row)"
                  />
                </el-tooltip>
                <el-tooltip
                  v-else
                  class="item"
                  content="取消置顶"
                  placement="top"
                >
                  <el-button
                    type="text"
                    icon="iconfont el-icon-application-untop-back"
                    @click="setProjectTop(row)"
                  />
                </el-tooltip>
                <el-divider direction="vertical" />
                <el-tooltip class="item" content="删除" placement="top">
                  <el-button
                    type="text"
                    :disabled="!$permission('project_del')"
                    icon="iconfont el-icon-application-delete"
                    @click="projectDelete(row)"
                  />
                </el-tooltip>
              </template>
            </vxe-column>
          </vxe-table>
          <vone-pagination
            ref="pagination"
            :total="tableData.total"
            @update="getProjectList"
          />
        </div>
        <div
          v-else-if="extraData.cardView == 'tree'"
          style="height: calc(100vh - 252rem)"
        >
          <vxe-table
            ref="projectTable"
            class="vone-vxe-table"
            border
            height="auto"
            show-overflow="tooltip"
            :loading="tableLoading"
            :empty-render="{ name: 'empty' }"
            :data="tableData.records"
            :column-config="{ resizable: true, minWidth: 120 }"
            :checkbox-config="{ reserve: true }"
            :tree-config="{
              transform: true,
              rowField: 'id',
              parentField: 'parentId',
              hasChild: 'hasChildren',
              lazy: true,
              loadMethod: ({ row }) => getClidData(row),
            }"
            row-id="id"
          >
            <vxe-column title="名称" tree-node>
              <template #default="{ row }">
                <div class="flexRow" :class="row.top ? 'vCard' : ''">
                  <a
                    @click="
                      itemProject(row.code, row.typeCode, row.id, row.name, row)
                    "
                  >{{ row.name }}</a>

                  <div @click.stop="focus(row)">
                    <i
                      v-if="row.collection"
                      style="color: #ffc642; font-size: 18px"
                      class="iconfont el-icon-icon-shoucang-on"
                    />
                    <i
                      v-else
                      class="el-icon-star-off"
                      style="font-size: 18px"
                    />
                  </div>
                </div>
              </template>
            </vxe-column>
            <vxe-column title="标识" field="code" width="100" />
            <vxe-column title="项目经理" field="leadingBy" width="120">
              <template #default="{ row }">
                <span
                  v-if="row.leadingBy && row.echoMap && row.echoMap.leadingBy"
                >
                  <vone-user-avatar
                    :avatar-path="row.echoMap.leadingBy.avatarPath"
                    :name="row.echoMap.leadingBy.name"
                  />
                </span>
                <i
                  v-else-if="!row.leadingBy"
                  class="iconfont el-icon-application-member"
                />
                <span v-else> -- </span>
              </template>
            </vxe-column>
            <vxe-column title="状态" field="isClosure" width="120">
              <template #default="{ row }">
                <span v-if="row.projectStage">
                  <el-tag>
                    {{ row.projectStage.desc }}
                  </el-tag>
                </span>
              </template>
            </vxe-column>
            <vxe-column title="类型" field="typeCode" width="80">
              <template #default="{ row }">
                <el-tag
                  v-if="row.typeCode && row.echoMap && row.echoMap.typeCode"
                  effect="dark"
                  color="success"
                  size="mini"
                >{{ row.echoMap.typeCode.name }}</el-tag>
                <span v-else>--</span>
              </template>
            </vxe-column>
            <vxe-column title="操作" fixed="right" align="left" width="120">
              <template #default="{ row }">
                <el-tooltip class="item" content="编辑" placement="top">
                  <el-button
                    type="text"
                    :disabled="!$permission('project_edit')"
                    icon="iconfont el-icon-application-edit"
                    @click="editProject(row)"
                  />
                </el-tooltip>
                <el-divider direction="vertical" />
                <el-tooltip
                  v-if="!row.top"
                  class="item"
                  content="置顶"
                  placement="top"
                >
                  <el-button
                    type="text"
                    icon="iconfont el-icon-application-top-back"
                    @click="setProjectTop(row)"
                  />
                </el-tooltip>
                <el-tooltip
                  v-else
                  class="item"
                  content="取消置顶"
                  placement="top"
                >
                  <el-button
                    type="text"
                    icon="iconfont el-icon-application-untop-back"
                    @click="setProjectTop(row)"
                  />
                </el-tooltip>
                <el-divider direction="vertical" />
                <el-tooltip class="item" content="删除" placement="top">
                  <el-button
                    type="text"
                    :disabled="!$permission('project_del')"
                    icon="iconfont el-icon-application-delete"
                    @click="projectDelete(row)"
                  />
                </el-tooltip>
              </template>
            </vxe-column>
          </vxe-table>
          <vone-pagination
            ref="pagination"
            :total="tableData.total"
            @update="getProjectList"
          />
        </div>
        <vone-cards
          v-else
          ref="project-card"
          :data="tableData"
          :row-count="4"
          height="calc(100vh - 262px)"
          @updateData="getProjectList"
        >
          <template slot-scope="{ row }">
            <a
              @click="
                itemProject(row.code, row.typeCode, row.id, row.name, row)
              "
            >
              <vone-card
                :title="row.name"
                :actions="rowActions"
                :class="row.top ? 'vCard' : ''"
                :actions-num="4"
              >
                <template slot="icon">
                  <svg class="icon" aria-hidden="true">
                    <use
                      :xlink:href="`#${
                        row.echoMap.typeCode ? row.echoMap.typeCode.icon : ''
                      }`"
                    />
                  </svg>
                </template>

                <template slot="title">
                  <el-row type="flex" justify="space-between">
                    <div class="title-box">
                      <el-tooltip
                        v-if="row.isShow"
                        class="item"
                        :content="row.name"
                        placement="top"
                      >
                        <div class="title">{{ row.name }}</div>
                      </el-tooltip>
                      <div
                        v-else
                        class="title"
                        @mouseenter="(e) => isShowToltip(e, row)"
                        @mouseout="hideTip(row)"
                      >
                        {{ row.name }}
                      </div>
                      <span>{{ row.key }}</span>
                    </div>
                    <div @click.stop="focus(row)">
                      <i
                        v-if="row.collection"
                        style="color: #ffc642; font-size: 18px"
                        class="iconfont el-icon-icon-shoucang-on"
                      />
                      <i
                        v-else
                        class="el-icon-star-off"
                        style="font-size: 18px"
                      />
                    </div>
                  </el-row>
                </template>
                <template v-slot:tagboxs>
                  <el-tag
                    v-if="
                      row.echoMap.projectProcess &&
                        row.echoMap.projectProcess.status
                    "
                    type="success"
                  >{{ row.echoMap.projectProcess.status }}</el-tag>
                </template>
                <div slot="desc">
                  <vone-user-avatar
                    :avatar-path="
                      getUserInfo(row) ? getUserInfo(row).avatarPath : ''
                    "
                    :name="getUserInfo(row) ? getUserInfo(row).name : ''"
                  />
                </div>
                <el-row class="descbox" flex>
                  <vone-toolitip :content="row.code" :label="'标识'" />
                  <vone-toolitip
                    :content="
                      row.echoMap && row.echoMap.hostProduct
                        ? row.echoMap.hostProduct.name
                        : ''
                    "
                    :label="'关联主办系统'"
                  />

                  <vone-toolitip :content="row.description" :label="'描述'" />
                </el-row>
              </vone-card>
            </a>
          </template>
        </vone-cards>
      </div>
    </div>

    <!-- 新增项目对话框 -->
    <newProjectDialog
      v-if="projectParam.visible"
      v-bind="projectParam"
      :visible.sync="projectParam.visible"
      @success="getProjectList"
    />
  </div>
</template>

<script>
import * as dayjs from 'dayjs'
import { cloneDeep } from 'lodash'

import {
  getAllProject,
  apiProjectDel,
  apiProjectAuth,
  addProjectAndStartFlow,
  getAllProjectType,
  closeProject,
  collectProject,
  topProject,
  getParentProjects,
  getChildProjects
} from '@/api/vone/project/index'
import { getPermission, setPermission, getRouter } from '@/utils/auth'
import newProjectDialog from './components/newProjectDialog.vue'
// import myTask from './my-task/index'
import storage from 'store'
import { textRange } from '@/utils'
export default {
  components: {
    newProjectDialog
    // myTask
  },
  filters: {
    format(val) {
      if (!val) return ''
      return dayjs(val).format('YYYY-MM-DD')
    }
  },
  data() {
    return {
      defaultFileds: [
        {
          key: 'name',
          name: '名称',
          type: {
            code: 'INPUT'
          },
          placeholder: '请输入名称'
        },
        {
          key: 'collection',
          name: '我收藏的',
          type: {
            code: 'SELECT'
          },
          placeholder: '请选择',
          optionList: [
            {
              code: true,
              name: '是',
              id: '1'
            },
            {
              code: false,
              name: '否',
              id: '2'
            }
          ]
        }
      ],
      cardOptions: [
        {
          name: '卡片',
          value: 'card',
          icon: 'el-icon-application-cengjishitu'
        },
        {
          name: '表格',
          value: 'table',
          icon: 'el-icon-application-biaogeshitu'
        },
        {
          name: '层级',
          value: 'tree',
          icon: 'el-icon-application-biaogeshitu'
        }
      ],
      rowActions: [
        // {
        //   type: 'text',
        //   text: '结项申请',
        //   hidden: ({ row }) => row.pStage == 'APPROVAL' || row.pStage == 'CLOSED',
        //   onClick: ({ row }) => this.endProject(row),
        //   icon: 'iconfont el-icon-application-project-end'
        // },
        {
          type: 'text',
          text: '修改',
          hidden: ({ row }) =>
            row.pStage == 'APPROVAL' || row.pStage == 'CLOSED',
          icon: 'iconfont el-icon-application-edit',
          onClick: ({ row }) => this.editProject(row),
          disabled: !this.$permission('project_edit')
        },
        {
          type: 'text',
          text: '置顶',
          hidden: ({ row }) => row.top,
          onClick: ({ row }) => this.setProjectTop(row),
          icon: 'iconfont el-icon-application-top-back'
        },
        {
          type: 'text',
          text: '取消置顶',
          hidden: ({ row }) => !row.top,
          onClick: ({ row }) => this.setProjectTop(row),
          icon: 'iconfont el-icon-application-untop-back'
        },
        {
          type: 'text',
          text: '删除',
          hidden: ({ row }) =>
            row.pStage == 'APPROVAL' || row.pStage == 'CLOSED',
          icon: 'iconfont el-icon-application-delete',
          onClick: ({ row }) => this.projectDelete(row),
          disabled: !this.$permission('project_del')
        }
      ],
      templeteList: [],
      tableData: {},
      projectParam: { visible: false },
      selectedProject: {},
      activeName: 'project',
      tableLoading: false,
      formData: {},
      extraData: {
        // cardView: 'card'
      }
    }
  },
  computed: {
    getUserInfo() {
      return function(row) {
        return row?.leadingBy && row?.echoMap?.leadingBy
      }
    }
  },
  watch: {
    $route: function() {
      this.getProjectList()
    }
  },
  mounted() {
    // this.getProjectList()
    this.getTemplate()
  },
  methods: {
    isShowToltip(e, node) {
      const bool = textRange(e.target)
      this.$set(node, 'isShow', bool)
    },
    hideTip(node) {
      this.$set(node, 'isShow', false)
    },
    async getTemplate() {
      const res = await getAllProjectType()
      if (!res.isSuccess) {
        return
      }
      this.templeteList = res.data.filter((e) => e.state)
    },
    inputChange(val) {
      if (val == 'list') {
        this.getProjectList()
      }
    },
    handleClick(tab) {
      if (tab.name === 'collect') {
        this.$router.push({
          name: 'projects_participation'
        })
      }
    },
    // 树表格展开
    getClidData(row, page) {
      if (row.load) return

      // row.loading = true
      // const tableAttr = this.$refs?.pagination?.pageObj || { current: 1, size: 20 }
      // this.$set(this.formData, 'projectId', [this.$route.params.id])
      // const groupMap = this.tableOptions.groupingOptions.reduce((r, v) => (r[v.value] = v.key) && r, {})

      // if (groupMap[row.groupType] == 'planEtime') {
      //   this.$set(this.formData, 'planEtime', { start: row.id + ' 00:00:00', end: row.id + ' 23:59:59' })
      // } else {
      //   this.$set(this.formData, groupMap[row.groupType], [row.id])
      // }

      const params = {
        extra: { 'tableId': '1',
          'tableSave': true,
          'boardField': [],
          'fixedView': [],
          'height': 0,
          'groupView': 'handle_by' },
        model: { 'parentProjectId':
          row.id,
        'parentOnly': false
        }
      }

      // if (this.groupItem?.type == 'time') {
      //   params.model.planEtime = {
      //     start: row.id + ' 00:00:00',
      //     end: row.id + ' 23:59:59'
      //   }
      // } else {
      //   params.model[this.groupItem.form] = [row.id]
      // }

      return getChildProjects(params)
        .then(res => {
          if (!res.isSuccess) {
            this.$message.warning(res.msg)
            return
          }
          // res.data.records.forEach(element => {
          //   element.tag =
          //     element.tagId && element.tagId.length && element.echoMap && element.echoMap.tagId
          //       ? element.echoMap.tagId.map(r => r.name)
          //       : []
          // })
          row.load = true
          row.loading = false
          // this.$nextTick(() => {
          //   row.childrenData = res.data.records || []
          //   row.count = res.data.total
          //   row.pageData = res.data
          //   this.timeStamp = new Date().valueOf()
          // })
          return res.data.records
        })
        .catch(() => {
          this.$set(row, 'loading', false)
        })
    },
    // 查询项目列表
    async getProjectList() {
      this.tableLoading = true
      let params = {}
      let pageObj = {}
      this.tableData.records = []
      if (
        this.extraData.cardView == 'table' ||
        this.extraData.cardView == 'tree'
      ) {
        pageObj = this.$refs.pagination?.pageObj
      } else {
        pageObj = this.$refs['project-card'].exportTableQueryData()
      }

      params = {
        ...pageObj,
        sort: 'createTime',
        order: 'descending',
        extra: {
          ...this.extraData
        },
        model: { ...this.formData }
      }

      if (this.extraData.cardView == 'tree') {
        const { isSuccess, data, msg } = await getParentProjects(params)
        this.tableLoading = false
        if (!isSuccess) {
          this.$message.error(msg)
          return
        }

        data.records.forEach((element) => {
          element.pStage = element.projectStage?.code || 'RUNNING'
          // element.hasChildren = true
        })
        this.tableData = data
        console.log(this.tableData.records)
      } else {
        const { isSuccess, data, msg } = await getAllProject(params)
        this.tableLoading = false
        if (!isSuccess) {
          this.$message.error(msg)
          return
        }

        data.records.forEach((element) => {
          element.pStage = element.projectStage?.code || 'RUNNING'
        })
        this.tableData = data
      }
    },

    async projectDelete(item) {
      try {
        await this.$confirm(`确定删除【${item.name}】吗`, '删除', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          customClass: 'delConfirm',
          showClose: false,
          type: 'warning'
        })
        const { isSuccess, msg } = await apiProjectDel([item.id])
        if (!isSuccess) {
          this.$message.error(msg)
          return
        }
        this.$message.success('删除项目成功')
        this.getProjectList()
      } catch (error) {
        //
      }
    },

    async itemProject(code, typeCode, id, name, row) {
      const arry = ['APPROVAL', 'CLOSED']
      if (row.projectStage && arry.indexOf(row.projectStage.code) > -1) {
        return this.$message.warning(
          ` 【${row.projectStage.desc}】流程审批中... `
        )
      }
      const { data, isSuccess, msg } = await apiProjectAuth(id)
      const jumpRouter = cloneDeep(data)
      if (!isSuccess) {
        this.$message.error(msg)
        return
      }
      if (!isSuccess) {
        return this.$message('获取用户权限失败，请重新登录')
      }
      if (!data.length) {
        this.$message.warning(
          '当前登录用户【项目角色】查看当前项目信息权限不足,请联系项目经理授权'
        )
        return
      }
      // -------------------------------------------------------------------------------
      // 处理按钮权限
      const hasPermissionList = [] // 接口返回的用来接收新的按钮权限数组
      var findPermission = function(V) {
        V.forEach((item) => {
          // 把传入的数组循环遍历
          if (item.meta.isButton === true) {
            hasPermissionList.push(item.meta.code) // item.meta.isButton 为true 把id添加到新数组
          }
          if (item.children) {
            findPermission(item.children) // 递归调用自身
          }
        })
      }
      findPermission(data) // 调用函数

      const permission = getPermission() // 从登录接口获取的权限按钮数据

      const allPermision = [...new Set([...permission, ...hasPermissionList])] // 去重

      setPermission(allPermision)

      // -------------------------------------------------------------------------------
      data.map((item) => {
        item.meta.activeApp = item?.meta?.code
        if (item?.children && item?.children.length > 0) {
          if (item.children[0].path !== '#') {
            item.redirect = item.children[0].path
          }
          const parentData = item // 一级数据
          this.gainMenuList(item.children, parentData)
        }
      })
      const routerMenu = cloneDeep(getRouter())
      const projectSettingMenu = cloneDeep(
        routerMenu
          .find((ele) => ele.name == 'project')
          .children.find((ele) => ele.name == 'project_view')
      )
      routerMenu.map((item) => {
        if (item.name == 'project') {
          item.children = []
          item.children.push(projectSettingMenu)
          item.children = [...item.children, ...data]
        }
      })

      this.$store.commit('user/set_router', routerMenu)

      // 保存路由信息
      this.$store.dispatch('project/itemProject', typeCode)

      const firstMenuName =
        jumpRouter[0]?.children[0]?.name || jumpRouter[0]?.name
      this.$router.push({
        name: firstMenuName,
        params: {
          projectKey: code,
          projectTypeCode: typeCode,
          id: id,
          name: name
        }
      })
    },
    addNewProject() {
      this.projectParam = { visible: true, title: '新增项目' }
    },
    fastAdd(val) {
      if (!this.$permission('alm:projectInfo:add')) {
        this.$message.warning('当前登录用户【项目角色】创建项目权限不足')
        return
      }
      this.projectParam = { visible: true, title: '新增项目', fastType: val }
    },
    editProject(data) {
      this.projectParam = {
        visible: true,
        title: '编辑项目',
        id: data.id,
        row: data
      }
    },
    async endProject(data) {
      try {
        await this.$confirm(`是否确定结项？`, '提示').then(async() => {
          if (
            data.echoMap &&
            data.echoMap.typeCode &&
            data.echoMap.typeCode.initiation
          ) {
            // 结项流程
            const userInfo = storage.get('user')
            const params = {
              processDefinitionKey: data.echoMap.typeCode.closureProcess,
              startAssignee: userInfo.id,
              variables: {
                assignee: userInfo.id,
                closureProject: {
                  assistProductIds: [],
                  typeName: data.echoMap.typeCode.name,
                  ...data
                },
                formData: {
                  componentUrl: 'vone/project/components/project-flow-info',
                  projectName: data.name
                },
                title: data.name || ''
              }
            }
            const res = await addProjectAndStartFlow(params)
            if (!res.isSuccess) {
              this.$message.error(res.msg)
              return
            }
            this.$message.success('结项申请成功')
            this.getProjectList()
          } else {
            // 非流程结项
            closeProject({
              id: data.id
            }).then((res) => {
              if (res.isSuccess) {
                this.$message.success('结项申请成功')
                this.getProjectList()
              } else {
                this.$message.warning(res.msg)
              }
            })
          }
        })
      } catch (error) {
        //
      }
    },
    fileDown(row) { },
    async focus(row) {
      const res = await collectProject(row.id, !row.collection)
      if (!res.isSuccess) {
        this.$message.error(res.msg)
        return
      }
      this.$message.success('操作成功')
      this.getProjectList()
    },
    async setProjectTop(row) {
      const res = await topProject(row.id, !row.top)
      if (!res.isSuccess) {
        this.$message.error(res.msg)
        return
      }
      this.$message.success('操作成功')
      this.getProjectList()
    },
    gainMenuList(childrenData, parentData, secondItem) {
      childrenData.map((item) => {
        item.meta.activeApp = parentData.meta.code
        item.meta.activeMenu = secondItem ? secondItem.path : item.path // 存在三级，那么指定的菜单为二级的path
        if (item.children && item.children.length > 0) {
          if (item.children[0].path !== '#') {
            item.redirect = item.children[0].path
          }
          this.gainMenuList(item.children, parentData, item)
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.pageContentNoH {
  .title-header {
    border-left: 4px solid var(--main-theme-color);
    padding-left: 10px;
    color: var(--font-main-color);
    font-weight: 500;
    // margin-bottom: 12px;
    line-height: 18px;
  }
  padding: 16px;
}
.mainheight {
  height: calc(100vh - 140px);
  position: relative;
}

.cardBox {
  width: 160px;
  height: 48px;
  position: relative;
  // box-shadow: var(--main-bg-shadow);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0px 16px;
  border-radius: 4px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  padding-right: 10px;
  .flexCard {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  &:hover .itemBtn {
    display: block;
  }
  &:hover::before {
    content: '';
    border-radius: 4px;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 99;
    svg {
      position: relative;
      z-index: 0;
    }
  }

  .icon {
    font-size: 24px;
    margin-right: 8px;
  }
  .itemName {
    width: 110px;
    font-size: 16px;
    font-weight: 500;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    color: var(--font-main-color);
  }
  .itemBtn {
    position: absolute;
    right: 10px;
    z-index: 999;
    display: none;
    color: #fff;
    background-color: var(--main-theme-color);
    padding: 5px;
    font-size: 12px;
    border-radius: 5px;
  }
}

.AGILE {
  background: linear-gradient(to right, #e3f0ff, #91bbff);
  background-size: 100% 100%;
}

.WALL {
  background: linear-gradient(to right, #fcf5e4, #ffca7a);
  background-size: 100% 100%;
}

.TEST {
  background: linear-gradient(to right, #e6f7ff, #6bc1ff);
  background-size: 100% 100%;
}
.DEVOPS {
  background: linear-gradient(to right, #e3fcf8, #4fd6cd);
  background-size: 100% 100%;
}
.CAR {
  background: linear-gradient(to right, #e6f2ff, #6ba6ff);
  background-size: 100% 100%;
}
.PLATFORM {
  background: linear-gradient(to right, #f5f0ff, #b59eff);
  background-size: 100% 100%;
  // &:hover{
  //   background: linear-gradient(to right, rgba(0, 0, 0, 0.5), rgba(0, 0, 0,0.5)), linear-gradient(to right, #F5F0FF, #B59EFF);
  // }
}
.userText {
  height: 35px;
  line-height: 35px;
}
.descbox {
  font-size: 14px;
  color: var(--font-second-color);
}
.vCard {
  position: relative;
  overflow: hidden;
}
.vCard::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 0;
  height: 0;
  border-bottom: 20px solid transparent;
  border-left: 20px solid #09bdbd; /* 根据需要设置颜色 */
}
.flexRow {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-left: -10px;
  line-height: 36px;
  a {
    padding-left: 20px;
  }
}
</style>
