<template>
  <div>

    <vone-lane ref="lane" :dragstart="dragstart" @added="laneAdded">
      <!-- 默认展示 -->
      <div class="defaultBox">
        <div v-if="boardColumns.length>0" class="scroll-wrap">
          <header class="header-wrap">
            <span v-for="item in boardColumns" :key="item.name" class="stage-wrap">{{ item.name }}
              <span class="stageCount">
                {{
                  boardList && boardList[item.stateCode] && boardList[item.stateCode].length }}
              </span>
            </span>

          </header>
          <div v-loading="loadingKanban" class="scroll-body">
            <div v-for="item in boardColumns" :key="item.name" class="stageBox">
              <vone-lane-item :key="item.stateCode" :value="item.stateCode" :disabled="disabledBoard" :list="boardList[item.stateCode]">
                <vone-lane-card
                  v-for="item in boardList[item.stateCode]"
                  :key="item.id"
                  :title="item.name"
                  :type="item.typeCode"
                  :item="item"
                  :code="item.code"
                  :date="item.planEtime"
                  date-tooltip="截止时间"
                  :user="item.handleBy"
                  :default-fields="defaultFields"
                  @click="showDetails(item)"
                />
              </vone-lane-item>
            </div>
          </div>
        </div>
      </div>
    </vone-lane>
    <!-- 需求详情 -->
    <vone-custom-info v-if="issueParam.visible" :key="issueParam.key" :visible.sync="issueParam.visible" v-bind="issueParam" type-code="TESTREQ" :right-tabs="rightTabs" hide-prv />

    <!-- 任务详情 -->
    <vone-custom-info v-if="taskParam.visible" :key="taskParam.key" :visible.sync="taskParam.visible" v-bind="taskParam" type-code="TASK" :right-tabs="rightTabs" hide-prv />
    <!-- 缺陷详情 -->
    <vone-custom-info v-if="defectParam.visible" :key="defectParam.key" :visible.sync="defectParam.visible" v-bind="defectParam" type-code="BUG" :right-tabs="rightTabs" hide-prv />

  </div>

</template>
<script>
import { getTestIssueBoardColumns } from '@/api/vone/project/board'
import { apiAlmTestIssueFindNextNode, apiAlmTestFlow, iderToTestRequire } from '@/api/vone/project/issue'
import { apiAlmTypeNoPage } from '@/api/vone/alm/index'
import { cloneDeep } from 'lodash'

export default {
  props: {
    formData: {
      type: Object,
      default: () => {}
    },
    extraData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      boardTypeList: [], // 看板类型列表
      boardColumns: [], // 看板泳道列表
      columnStatusMap: {}, // 每一列里面的状态
      boardList: {}, // 看板数据
      defaultFields: {
        'TESTREQ': ['编号', '规模', '优先级', '计划完成时间', '处理人', '状态', '延期']
      },
      loadingKanban: false,
      disabledBoard: false,
      issueParam: { visible: false }, // 编辑需求
      defectParam: { visible: false }, // 缺陷编辑
      taskParam: { visible: false }, // 编辑任务
      rightTabs: [
        {
          active: true,
          label: '评论',
          name: 'comment'
        },
        {
          label: '活动',
          name: 'active'
        },
        {
          label: '工时',
          name: 'workTime'
        }
      ]
    }
  },
  watch: {
    extraData: {
      handler(val) {
        this.$set(this.defaultFields, 'TESTREQ', val.boardField)
      },
      deep: true
    }
  },
  async mounted() {
    this.getBoardForProject()
  },
  methods: {
    async boardTypeChange() {
      this.boardColumns = []
      this.boardList = {}

      // 查询需求列数据
      const res = await getTestIssueBoardColumns(this.formData.typeCode || 'SOR')

      if (res.isSuccess) {
        this.boardColumns = res.data.columns.map(ele => {
          ele.stateCode = ele.statuses[0].stateCode
          return ele
        }).sort((a, b) => a.sort - b.sort)
        res.data.columns.map(ele => {
          this.columnStatusMap[ele.stateCode] = ele.statuses.map(status => status.stateCode)
        })
      }
      this.loadKanbanData()
    },
    // 查询需求分类
    getBoardForProject() {
      this.loadingKanban = true
      apiAlmTypeNoPage({ classify: 'TESTREQ' }).then(res => {
        this.loadingKanban = false
        if (res.isSuccess) {
          res.data.map(item => {
            if (item) {
              this.boardTypeList.push({
                id: item.id,
                name: item.name,
                code: item.code
              })
            }
          })

          // 没有类型的筛选条件时默认选中第一个类型
          // if (!this.formData.typeCode) {
          //   this.$set(this.formData, 'typeCode', this.boardTypeList[0].code)
          // }

          this.boardTypeChange()
        } else {
          this.$message(res.msg)
        }
      })
    },
    // 查询看板数据
    async loadKanbanData() {
      this.loadingKanban = true
      const res = await iderToTestRequire({ typeCode: this.formData.typeCode || 'SOR', ...this.formData })
      this.loadingKanban = false
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      const bordList = cloneDeep(res.data)
      this.boardList = bordList.reduce((acc, cur) => {
        cur.classify = { code: 'TESTREQ', name: '需求' }
        acc[cur.stateCode] ? acc[cur.stateCode].push(cur) : acc[cur.stateCode] = [cur]
        return acc
      }, {})
    },
    // 判断流转方向
    async dragstart({ element }) {
      const res = await apiAlmTestIssueFindNextNode(element.id)
      if (res.isSuccess) {
        const colDragStates = {}
        const status = res.data.map(item => item.stateCode)
        for (const i in this.columnStatusMap) {
          colDragStates[i] = []
          const states = this.columnStatusMap[i].map(v => v.split('$')[1])
          colDragStates[i] = states.filter(item => status.indexOf(item) > -1)
        }
        return colDragStates
      } else {
        this.$message.warning(res.msg)
        return
      }
    },
    async laneAdded({ element, laneItem }) {
      const state = this.boardColumns.find(status => status.stateCode === laneItem)
      const targetState = state.statuses[0] || {}
      const res = await apiAlmTestFlow(element.id, element.stateCode, targetState.stateCode)
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.loadKanbanData()
    },
    showDetails(item) {
      // 获取分类,判断当前数据属于需求/任务/缺陷
      const type = item.echoMap && item.echoMap.type ? item.echoMap.type.classify.code : null
      if (type == 'TESTREQ') {
        this.issueParam = {
          visible: true,
          title: '需求详情',
          id: item.id,
          key: Date.now(),
          infoDisabled: false,
          rowTypeCode: item.typeCode,
          stateCode: item.stateCode,
          leftTabs: [
            {
              label: '需求',
              name: 'IssueToIssue'
            },
            {
              label: '关联任务',
              name: 'TaskTab'
            },
            {
              label: '关联缺陷',
              name: 'BugTab'
            },
            // {
            //   label: '关联代码',
            //   name: 'DevelopTab'
            // },
            {
              label: '测试用例',
              name: 'TestCase'
            }
          ]
        }
      } else if (type == 'BUG') {
        this.defectParam = {
          visible: true,
          title: '缺陷详情',
          id: item.id,
          key: Date.now(),
          infoDisabled: false,
          rowTypeCode: item.typeCode,
          stateCode: item.stateCode,
          leftTabs: []
        }
      } else if (type == 'TASK') {
        this.taskParam = {
          visible: true,
          title: '任务详情',
          id: item.id,
          key: Date.now(),
          infoDisabled: false,
          rowTypeCode: item.typeCode,
          stateCode: item.stateCode,
          leftTabs: [
            {
              label: '任务',
              name: 'TaskTask'
            }
          ]
        }
      }
    }
  }
}
</script>
<style lang="scss" scoped>
  .lane-board {
    display: flex;
    overflow-y: auto;
    overflow-x: hidden;
    width: fit-content;
  }

  ::v-deep .el-card {
    border: none;
  }
	.el-dropdown-item-row {
		padding-right: 20px;

		i {
			position: absolute;
			right: -6px;
		}
	}

	::v-deep .vone-lane-scroll {
		height: calc(100vh - 105px - 61px);
    overflow-x: hidden;
	}

	::v-deep.vone-lane-item__draggable {
		height: calc(100vh - 270px);
	}

	.defaultBox {
		display: flex;
    flex-direction: column;
    width: 100%;
    // padding: 12px 16px;
    overflow-y: hidden;
    overflow-x: overlay;
	}
  ::v-deep .el-tabs__header{
    margin: 0  !important;
  }
  ::v-deep .el-card__body {
    padding: 0 !important;
  }
  ::v-deep .toolbar{
    height:52px !important;
    padding-bottom:16px;
  }
  .tabs-group {
    max-width: 600px;
    margin-left: 30px;
    ::v-deep .el-tabs__nav-prev,::v-deep .el-tabs__nav-next {
      line-height: 32px;
    }
    ::v-deep >.el-tabs__header{
      border-bottom: none;
      .el-tabs__nav {
        border-bottom: 1px solid #E4E7ED;
        border-radius: 5px;
      }
    }
    ::v-deep .el-tabs__item {
      font-size: 12px;
      height: 32px;
      line-height: 32px;
      padding: 0 15px;
      border-bottom: none;
      &::nth-child(2) {
        padding-left: 15px;
      }
      & .el-tabs__item:hover {
        color: #fff
      }
    }
    ::v-deep .is-active {
      background: var(--main-theme-color,#3e7bfa);
      color: #fff;
    }
  }

.scroll-wrap {
  width: fit-content;
}
.header-wrap {
  white-space: nowrap;
  width: max-content;
}
.stage-wrap {
  display: inline-block;
  width: 266px;
  height: 40px;
  line-height: 40px;
  padding-left: 16px;
  font-weight: 500;
  color: #1d2129;
  background: #EAEDF0;
  border-radius: 4px 4px 0px 0px;
  &:not(:last-child) {
    margin-right: 12px;
  }
}
.scroll-body {
  display: flex;
  padding: 0;
  height: calc(100vh - 227px);
}
// 阶段样式
.stageBox {
  width: 266px;
  height: 100%;
  background: #fafafa;
  & + & {
    margin-left: 12px;
  }

  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
    background: #fafafa;
  }
}
// ::v-deep .is-always-shadow{
//   padding-top:47px;
// }
 ::v-deep .v-lane-card {
    // cursor: pointer;
}
.stageCount{
  color: #777F8E;
  margin-left: 5px;
}

</style>

