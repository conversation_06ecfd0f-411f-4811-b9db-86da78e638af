<template>
  <div>
    <div :style="{height: tableHeight}">
      <vxe-table
        ref="reqm-req-table"
        class="vone-vxe-table"
        border
        height="auto"
        show-overflow="tooltip"
        :loading="tableLoading"
        :empty-render="{ name: 'empty' }"
        :data="tableData.records"
        :column-config="{ minWidth:'120px' }"
        :checkbox-config="{ reserve: true }"
        row-id="id"
        @resizable-change="({ column }) => resizableChangeEvent(column, 'issue-table')"
      >
        <vxe-column type="checkbox" width="36" fixed="left" align="center" />
        <vxe-column title="标题" field="name" min-width="480" fixed="left" class-name="name_col custom-title-style" show-overflow="ellipsis" tree-node>
          <template #default="{ row }">
            <span v-if="row.delay" style="position: absolute; left: 0">
              <el-tooltip :open-delay="500" content="当前工作项已延期" placement="top">
                <i class="el-icon-warning-outline color-danger ml-2" />
              </el-tooltip>
            </span>
            <el-tooltip v-showWorkItemTooltips :content="row.code + ' ' + row.name" placement="top-start" :visible-arrow="false">
              <span class="custom-title-main" @click="showInfo(row)">
                <i
                  v-if="row.typeCode && row.echoMap && row.echoMap.typeCode"
                  :class="`iconfont ${row.echoMap.typeCode.icon} custom-title-style-icon `"
                  :style="{ color:`${row.echoMap.typeCode ? row.echoMap.typeCode.color : '#ccc'}`}"
                />
                <span class="custom-title-style-text">{{ row.code + " " + row.name }}</span>
              </span>
            </el-tooltip>
            <span
              class="custom-title-style-copy"
              :style="{position: 'absolute', top:' -4px',right: '-40px', display: copyRow && copyRow.id == row.id ? 'block' : ''}"
            >
              <el-dropdown trigger="click" :hide-on-click="true" @visible-change="(e) => (visibleChange(e, row))" @command="customCopy">
                <el-button type="text" icon="iconfont el-icon-application-more" />
                <el-dropdown-menu slot="dropdown" class="custom-title-copy-dropdown">
                  <el-dropdown-item icon="iconfont el-icon-edit-character-b" command="title">
                    <span>复制编号</span>
                  </el-dropdown-item>
                  <el-dropdown-item icon="iconfont el-icon-application-copy-content" command="code">
                    <span>复制标题</span>
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </span>
          </template>
        </vxe-column>
        <vxe-column field="stateCode" title="状态" width="110" sortable>
          <template #default="{ row, rowIndex }">
            <issueStatus
              v-if="row"
              :workitem="row"
              :no-permission="!$permission('reqm_center_require_flow')"
              @changeFlow="editRowStatus(row, rowIndex)"
            />
          </template>
        </vxe-column>
        <vxe-column field="handleBy" title="处理人" width="120">
          <template #default="{ row }">
            <span>
              <vone-remote-user v-model="row.handleBy" class="remoteuser" :default-data="[row.echoMap.handleBy]" :disabled="!$permission('reqm-center-require-edit')" @change="workitemChange(row,$event, 'handleBy')" />
            </span>

          </template>
        </vxe-column>
        <vxe-column field="putBy" title="提出人" width="120">
          <template #default="{ row }">
            <span>
              <vone-remote-user v-model="row.putBy" class="remoteuser" :default-data="[row.echoMap.putBy]" :disabled="!$permission('reqm_center_idea_edit')" @change="workitemChange(row,$event, 'putBy')" />
            </span>

          </template>
        </vxe-column>
        <vxe-column field="createTime" title="创建时间" width="120" show-overflow-tooltip sortable>
          <template #default="{ row }">
            <span v-if="row.createTime">
              {{ dayjs(row.createTime).format("YYYY-MM-DD") }}
            </span>
            <span v-else>{{ row.createTime }}</span>
          </template>
        </vxe-column>
        <vxe-column field="leadingBy" title="负责人" width="120">
          <template #default="{ row }">
            <span>
              <vone-remote-user v-model="row.leadingBy" class="remoteuser" :default-data="[row.echoMap.leadingBy]" :disabled="!$permission('reqm_center_idea_edit')" @change="workitemChange(row,$event, 'leadingBy')" />
            </span>

          </template>
        </vxe-column>
        <vxe-column field="planEtime" title="计划完成时间" show-overflow-tooltip width="135" sortable>
          <template #default="{ row }">
            <span v-if="row.planEtime">
              {{ dayjs(row.planEtime).format("YYYY-MM-DD HH:mm") }}
            </span>
            <span v-else>{{ row.planEtime }}</span>

          </template>
        </vxe-column>
        <vxe-column field="rateProgress" title="进度" width="80" sortable>
          <template slot-scope="{ row }">
            <el-tooltip placement="top" :content="`${row.rateProgress}%`">
              <el-progress :percentage="row.rateProgress ? parseInt(row.rateProgress) :0" :color="'var(--main-theme-color,#3e7bfa)'" :show-text="false" />
            </el-tooltip>

          </template>
        </vxe-column>
        <vxe-column field="priorityCode" title="优先级" width="100" sortable>
          <template #default="{ row }">
            <vone-icon-select v-model="row.priorityCode" :data="prioritList" filterable clearable style="width:100%" class="userList" :no-permission="!$permission('reqm_center_require_priority')" @change="workitemChange(row,$event,'priorityCode')">
              <el-option v-for="item in prioritList" :key="item.key" :label="item.name" :value="item.code">
                <i :class="`iconfont ${item.icon}`" :style="{color: item.color, fontSize: '16px', paddingRight: '6px'}" />
                {{ item.name }}
              </el-option>
            </vone-icon-select>
          </template>
        </vxe-column>
        <vxe-column field="sourceCode" title="需求来源" show-overflow-tooltip width="100">
          <template #default="{ row }">
            <span
              v-if="
                row.sourceCode &&
                  row.sourceCode &&
                  row.echoMap.sourceCode
              "
            >
              {{ row.echoMap.sourceCode.name }}
            </span>
            <span v-else>{{ row.sourceCode }}</span>
          </template>
        </vxe-column>
        <vxe-column show-overflow-tooltip field="projectId" title="归属项目" width="90">
          <template #default="{ row }">
            <span
              v-if="
                row.projectId &&
                  row.echoMap &&
                  row.echoMap.projectId
              "
            >
              {{ row.echoMap.projectId.name }}
            </span>
            <span v-else>{{ row.projectId }}</span>
          </template>
        </vxe-column>
        <vxe-column show-overflow-tooltip field="planId" title="归属计划" width="90">
          <template #default="{ row }">
            <span v-if=" row.echoMap && row.echoMap.planId">
              {{ row.echoMap.planId.name }}
            </span>
            <span v-else>{{ row.planId }}</span>
          </template>
        </vxe-column>
        <vxe-column field="tag" title="标签" width="150" show-overflow-tooltip>
          <template #default="{ row }">
            <span v-for="(item,index) in row.tag" :key="index">
              <el-tag style="margin-right:6px" type="success">
                {{ item }}
              </el-tag>
            </span>

          </template>
        </vxe-column>
        <vxe-column title="操作" fixed="right" align="left" width="120">
          <template #default="{ row }">
            <template>
              <el-tooltip class="item" content="编辑" placement="top">
                <el-button type="text" :disabled="!$permission('reqm-center-require-edit')" icon="iconfont el-icon-application-edit" @click="editIssue(row)" />
              </el-tooltip>
              <el-divider direction="vertical" />
              <el-tooltip class="item" content="删除" placement="top">
                <el-button type="text" :disabled="!$permission('reqm-center-require-del')" icon="iconfont el-icon-application-delete" @click="deleteIssue(row)" />
              </el-tooltip>
              <el-divider direction="vertical" />
              <el-dropdown trigger="click" :hide-on-click="false" @command="e => e && e()">
                <el-button type="text" icon="iconfont el-icon-application-more icon_click" />
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item icon="iconfont el-icon-edit-character-b" :command="() => titleCopy(row, 'code')">
                    <span>复制编号</span>
                  </el-dropdown-item>
                  <el-dropdown-item icon="iconfont el-icon-application-copy-content" :command="() => titleCopy(row, 'title')">
                    <span>复制标题</span>
                  </el-dropdown-item>
                  <el-dropdown-item :disabled="!$permission('reqm_center_graph')" icon="iconfont el-icon-application-topology" :command="() => gotoTuoPu(row)">
                    <span>拓扑</span>
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </template>
          </template>
        </vxe-column>
      </vxe-table>

    </div>
    <vone-pagination ref="pagination" :total="tableData.total" @update="getInitTableData" />

    <!-- 编辑 -->
    <vone-custom-edit v-if="issueParam.editvisible" :key="issueParam.key" :visible.sync="issueParam.editvisible" v-bind="issueParam" :type-code="'ISSUE'" :left-tabs="leftTabs" :right-tabs="rightTabs" @success="getInitTableData" @hideTab="hideTab" />

    <!-- 详情 -->
    <vone-custom-info v-if="issueInfoParam.editvisible" :visible.sync="issueInfoParam.editvisible" v-bind="issueInfoParam" :type-code="'ISSUE'" :left-tabs="leftTabs" :right-tabs="rightTabs" @success="getInitTableData" @hideTab="hideTab" />
  </div>
</template>

<script>
import issueStatus from '@/views/vone/project/common/change-status/index.vue'
import { editById } from '@/api/vone/project/index'
import { requirementDel } from '@/api/vone/reqmcenter/require'
export default {
  components: {
    issueStatus
  },
  props: {
    tableData: {
      type: Object,
      default: () => {}
    },
    prioritList: {
      type: Array,
      default: () => []
    },
    extraData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      issueParam: {
        visible: false,
        demoDiolog: false
      },
      tableList: [],
      issueInfoParam: { // 详情
        visible: false
      },
      tableLoading: false,
      rightTabs: [
        {
          active: true,
          label: '评论',
          name: 'comment'
        },
        {
          label: '活动',
          name: 'active'
        },
        {
          label: '工时',
          name: 'workTime'
        }
      ],
      leftTabs: [
        {
          label: '需求',
          name: 'IssueToIssue'
        },
        {
          label: '测试用例',
          name: 'TestCase'
        }
        // {
        //   label: '关联代码',
        //   name: 'DevelopTab'
        // }
      ],
      copyRow: null
    }
  },
  computed: {
    tableHeight() {
      const height = (this.extraData?.height || 0) + 'px'
      return `calc(${this.$reduceTableHeight} - ${height})`
    }
  },
  mounted() {
    this.tableList = this.tableData.records // 用于编辑时切换上一个下一个
    // 如果是从别的页面跳转过来，例如用户需求拆分需求
    if (this.$route.query.showDialog) {
      this.issueInfoParam = {
        editvisible: true,
        title: '需求详情',
        id: this.$route.query.queryId,
        key: Date.now(),
        infoDisabled: true,
        tableList: this.tableList,
        rowTypeCode: this.$route.query.rowTypeCode,
        stateCode: this.$route.query.stateCode,
        rowProjectId: this.$route.query.projectId
      }
    }
  },
  methods: {
    resizableChangeEvent(column, refName) {
      if (column.field == 'name') {
        this.$refs[refName].refreshColumn()
      }
    },
    customCopy(command) {
      const _this = this
      const message = command == 'title' ? this.copyRow.code : this.copyRow.name
      this.$copyText(message).then(function(e) {
        _this.$message.success(' 已复制到剪贴板！')
      }, function(e) {
        _this.$message.warning(' 该浏览器不支持自动复制')
      })
      this.copyRow = null
    },
    visibleChange(e, row) {
      if (e) {
        this.copyRow = row
      } else {
        this.copyRow = null
      }
    },
    getInitTableData() {
      this.$emit('success')
    },
    showInfo(row) {
      this.issueInfoParam = {
        key: Date.now(),
        editvisible: true,
        title: '需求详情',
        id: row.id,
        infoDisabled: true,
        tableList: this.tableList,
        rowTypeCode: row.typeCode,
        stateCode: row.stateCode,
        rowProjectId: row.projectId
      }
      this.projectId = row.projectId
    },
    editRowStatus(row, index) {
      this.$emit('refreshStatus', row, index)
    },
    async workitemChange(row, e, t) {
      const params = {
        id: row.id
      }
      params[t] = e
      const res = await editById('requirement', params)
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.$set(row, t, e)
      this.$message.success('修改成功')
    },
    editIssue(row) {
      this.issueParam = {
        editvisible: true,
        title: '编辑需求',
        id: row.id,
        key: Date.now(),
        infoDisabled: false,
        tableList: this.tableList,
        rowTypeCode: row.typeCode,
        stateCode: row.stateCode,
        rowProjectId: row.projectId
      }
    },
    async deleteIssue(row) {
      await this.$confirm(`确定删除【${row.name}】吗?`, '删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        customClass: 'delConfirm',
        showClose: false,
        type: 'warning'
      })

      const { isSuccess, msg } = await requirementDel(
        row.length ? row : [row.id]
      )
      if (!isSuccess) {
        this.loading = false
        this.$message.error(msg)
        return
      }
      this.$message.success('删除成功')
      this.getInitTableData()
    },
    hideTab(type) {
      if (type == 0) {
        this.rightTabs = [
          {
            active: true,
            label: '评论',
            name: 'comment'
          },
          {
            label: '活动',
            name: 'active'
          }
        ]
      } else {
        this.rightTabs = [
          {
            active: true,
            label: '评论',
            name: 'comment'
          },
          {
            label: '活动',
            name: 'active'
          },
          {
            label: '工时',
            name: 'workTime'
          }
        ]
      }
    },
    // 复制标题到剪贴板
    titleCopy(row, type) {
      const _this = this
      const message = type == 'code' ? row.code : row.name
      this.$copyText(message).then(function(e) {
        _this.$message.success(' 已复制到剪贴板！')
      }, function(e) {
        _this.$message.warning(' 该浏览器不支持自动复制')
      })
    },
    gotoTuoPu(row) {
      this.$router.push({
        path: '/reqmcenter/require/requireList/graph/' + row.id,
        query: {
          code: row.code
        }
      })
    }
  }

}
</script>

<style lang="scss" scoped>
.remoteuser {
  ::v-deep .el-select .el-input .el-input__inner{
    border: none;
  }
  ::v-deep .el-input--small .el-input__inner {
    border: none;
  }
  ::v-deep .el-input__suffix {
    display: none;
  }
}
::v-deep .el-collapse-item__wrap {
  border-bottom: none;
}
::v-deep .el-tree-node__content {
  height: 40px;
}

::v-deep .el-main {
  padding: 0;
  margin-left: 12px;
}
::v-deep .vone-tabs .el-tabs__item {
  padding: 0 10px !important;
}
::v-deep .el-collapse-item__wrap {
  border-bottom: none;
}
::v-deep .el-tree-node__content {
  height: 40px;
}

::v-deep .el-main {
  padding: 0;
  margin-left: 12px;
}

::v-deep .name_col {

  .cell {
    display: flex;
    div.name_icon {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: normal;
      word-break: break-all;
      flex: 1;
      display: flex;
      align-items: center;
    }
  }
}
.table_title {
  // overflow: hidden;
  // white-space: nowrap;
  // text-overflow: ellipsis;
  display: inline-block;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.search {
  display: inline-block;
  // margin-right:20px;
}
.vone-tabs::v-deep .el-tabs__header {
  margin: 0 !important;
}

.nextBtn {
  width: 20px;
  height: 20px;
  line-height: 20px;
  border-radius: 3px;
  text-align: center;
  cursor: pointer;
  font-size: 13px;
  margin-right: 3px;
  &:hover {
    font-weight: bold;
  }
}
</style>
<style >
.userList .el-input__inner {
  border: 0;
}
.userList .el-input__icon {
  display: none;
}

</style>

