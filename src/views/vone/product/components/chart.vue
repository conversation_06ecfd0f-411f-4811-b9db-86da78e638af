<template>
  <div v-loading="chartLoading" class="chart">
    <div class="item chart-1">
      <div class="item-1 subTitleColor">
        <strong v-if="$route.query.planRtime">
          剩余 {{ dayjs($route.query.planRtime).fromNow(true) }}
        </strong>
        <span v-else class="noTime">
          未设置计划发布时间
        </span>
        <div>
          <span class="dots"><span class="dot" :style="{ background: '#3CB540'}" /> 已完成</span>
          <span class="dots"><span class="dot" :style="{ background: '#64BEFA'}" /> 进行中</span>
          <span class="dots"><span class="dot" :style="{ background: '#ADB0B8'}" /> 未开始</span>
          <span class="dots"><span class="dot" :style="{ background: '#fab7b7'}" /> 已归档</span>
        </div>
      </div>
      <div class="progress">
        <div v-if="progressData.totalSum" :style="{width:smokePercent + '%', background: '#3CB540'}" />
        <div v-if="progressData.totalSum" :style="{width:skipPercent + '%', background: '#64BEFA'}" />
        <div v-if="progressData.totalSum" :style="{width:undoPercent + '%', background: '#ADB0B8'}" />
        <div v-if="progressData.totalSum" :style="{width:archived + '%', background: '#fab7b7'}" />
        <div v-else :style="{width:100 + '%', background: '#EBEEF5'}" />
      </div>
    </div>
    <div class="item chart-2 subTitleColor">
      <div class="chart-item">
        <div>
          <span class="item-num" :style="{ color: '#3CB540'}">{{ progressData.done }}</span>
          <span>已完成</span>
        </div>
      </div>
      <div class="chart-item">
        <div>
          <span class="item-num" :style="{ color: '#64BEFA'}">{{ progressData.doing }}</span>
          <span>进行中</span>
        </div>
      </div>
      <div class="chart-item">
        <div>
          <span class="item-num" :style="{ color: '#ADB0B8'}">{{ progressData.noStart }}</span>
          <span>未开始</span>
        </div>
      </div>
      <div class="chart-item">
        <div>
          <span class="item-num" :style="{ color: ' #fab7b7'}">{{ progressData.archived }}</span>
          <span>已归档</span>
        </div>
      </div>

    </div>
  </div>
</template>
<script>

const stateList = [
  {
    state: 1, name: '未开始', color: '#3CB540', code: 'noStart'
  },
  {
    state: 2, name: '进行中', color: '#64BEFA', code: 'doing'
  },
  {
    state: 3, name: '已完成', color: '#ADB0B8', code: 'done'
  },
  {
    state: 4, name: '已归档', color: '#fab7b7', code: 'archived'

  }
]
import { projectPlanState } from '@/api/vone/product'

export default {
  components: {
  },
  data() {
    return {

      progressData: {},
      stateList,
      chartLoading: false
    }
  },
  computed: {
    // 已完成
    smokePercent() {
      return (this.progressData.done / this.progressData.totalSum * 100).toFixed(2)
    },
    // 进行中
    skipPercent() {
      return (this.progressData.doing / this.progressData.totalSum * 100).toFixed(2)
    },
    // 未开始
    undoPercent() {
      return (this.progressData.noStart / this.progressData.totalSum * 100).toFixed(2)
    },
    // 已归档
    archived() {
      return (this.progressData.archived / this.progressData.totalSum * 100).toFixed(2)
    }

  },

  mounted() {
    this.getStatus()
  },
  methods: {

    async getStatus() {
      this.chartLoading = true
      const params = {
        productVersionId: this.$route.params.versionId
      }
      const res = await projectPlanState(params)
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      const stateMap = stateList.reduce((r, v) => (r[v.state] = v) && r, {})
      const originMap = {
        done: 0,
        doing: 0,
        noStart: 0,
        archived: 0
      }
      if (!Object.keys(res.data).length) {
        this.progressData = originMap
      } else {
        for (var i in res.data) {
          this.$set(originMap, stateMap[i].code, res.data[i])
        }

        this.progressData = originMap

        const { done, doing, noStart, archived } = this.progressData
        this.progressData.totalSum = Number(done) + Number(doing) + Number(noStart) + Number(archived)
      }
      this.chartLoading = false
    }
  }
}
</script>

<style lang="scss" scoped>
.chart {
  margin: 0 -16px;
  padding: 16px;
  height: 126px;
  border-bottom: 1px solid #ebeef5;
  display: flex;
  .item {
    flex: 1;
    border: 1px solid #ebeef5;
    padding: 12px 16px;
  }
  .chart-1 {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    margin-right: 12px;
    .item-1 {
      display: flex;
      justify-content: space-between;
      .dots {
        color: #8A8F99;
        .dot {
          display: inline-block;
          width: 10px;
          height: 10px;
          border-radius: 5px;
          margin-right: 4px;
        }
      }
    }
    .dots + .dots {
      margin-left: 12px;
    }
    .progress {
      width: 100%;
      height: 20px;
      display: flex;
      border-radius: 3px;
      overflow: hidden;
      div {
        height: 100%;
      }
      div + div {
        border-left: 1px solid #fff;
      }
    }
  }
  .chart-2 {
    display: flex;
    .chart-item {
      flex: 1;
      display: flex;
      align-items: center;
      >div {
        height: 100%;
        width: 100%;
        text-align: center;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        .item-num {
          font-size: 28px;
          line-height: 40px;
        }
      }
    }
    .chart-item + .chart-item {
      &::before{
        content: '';
        display: inline-block;
        width: 1px;
        height: 32px;
        background: #C1C8D6;
      }
    }
  }
}
.flex {
  display: flex;
}
.noTime{
  color: var(--placeholder-color);

}
</style>
