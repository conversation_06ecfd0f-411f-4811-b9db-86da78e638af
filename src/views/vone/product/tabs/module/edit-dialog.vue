<template>
  <el-dialog class="dialogContainer" :title="title" :visible="visible" width="50%" :close-on-click-modal="false" :close-on-press-escape="false" @close="close">
    <!-- 表单部分 -->
    <el-form ref="moduleForm" :model="moduleForm" label-position="top" :rules="rules">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="名称" prop="name">
            <el-input v-model="moduleForm.name" placeholder="请输入名称" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="编码" prop="code">
            <el-input v-model="moduleForm.code" placeholder="请输入编码" :disabled="title=='编辑'" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="类型" prop="nodeType">
            <el-select v-model="moduleForm.nodeType" placeholder="请选择类型">
              <el-option label="模块" :value="1" />
              <el-option :disabled="moduleForm.parentId == 0" label="交易" :value="2" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col v-if="moduleForm.nodeType==2" :span="12">
          <el-form-item label="测试经理" prop="testManager">
            <vone-remote-user v-model="moduleForm.testManager" />
          </el-form-item>
        </el-col>
        <el-col v-if="moduleForm.nodeType==2" :span="12">
          <el-form-item
            label="执行方式"
            prop="executionMethod"
          >
            <el-select
              v-model="moduleForm.executionMethod"
              placeholder="请选择执行方式"
              style="width: 100%"
              clearable
            >
              <el-option
                v-for="item in executionMethodList"
                :key="item.value"
                :label="item.name"
                :value="item.id"
                :title="item.name"
              />
            </el-select>
          </el-form-item>
        </el-col>

        <el-col v-if="moduleForm.nodeType==2" :span="12">
          <el-form-item label="交易类型" prop="tradeType">
            <el-select v-model="moduleForm.tradeType" placeholder="请选择交易类型">
              <el-option
                v-for="item in nodeTypeList"
                :key="item.value"
                :label="item.name"
                :value="item.id"
                :title="item.name"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col v-if="moduleForm.nodeType==2" :span="12">
          <el-form-item label=" 交易状态" prop="tradeStatus">
            <el-select v-model="moduleForm.tradeStatus" placeholder="请选择交易状态">
              <el-option label="禁用" :value="1" />
              <el-option label="启用" :value="0" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col v-if="moduleForm.nodeType==1" :span="12">
          <el-form-item label="上级模块">
            <!-- <el-input v-model="moduleForm.parentId" /> -->
            <vone-tree-select v-model="moduleForm.parentId" search-nested :tree-data="treedata" placeholder="请选择" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="描述">
            <el-input v-model="moduleForm.description" show-word-limit maxlength="200" type="textarea" placeholder="请输入" />
          </el-form-item>
        </el-col>
      </el-row>

      <!--<el-col :span="12">
          <el-form-item label="研发经理" prop="developmentManager">
            <vone-remote-user v-model="moduleForm.developmentManager" :default-data="title=='编辑'&&moduleForm.echoMap&&moduleForm.developmentManager? [moduleForm.echoMap.developmentManager]: []" />
          </el-form-item>
        </el-col> -->

    </el-form>
    <!-- 表单结束，按钮操作部分 -->
    <span slot="footer" class="dialog-footer">
      <el-button size="small" @click="$emit('update:visible', false)">取消</el-button>
      <el-button type="primary" size="small" @click="save">确定</el-button>
    </span>
  </el-dialog>
</template>
<script>
import { operationModule } from '@/api/vone/product'
import { apiBaseDictNoPage } from '@/api/vone/base/dict'

import _ from 'lodash'
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: ''
    },
    rowData: {
      type: Object,
      default: null
    },
    tableData: {
      type: Array,
      default: () => []
    }
  },
  data() {
    var validatePass = (rule, value, callback) => {
      const landlinePtn = /[^\w_]/
      if (landlinePtn.test(value)) {
        callback(new Error('请输入正确的编码'))
      } else {
        callback()
      }
    }
    return {
      rules: {
        code: [
          { required: false, validator: validatePass, trigger: 'blur' }
        ],
        name: [
          { required: true, message: '请输入名称', trigger: 'blur' }
        ],
        nodeType: [
          { required: true, message: '请选择类型', trigger: 'change' }
        ],
        tradeType: [
          { required: true, message: '请选择交易类型', trigger: 'change' }
        ],
        tradeStatus: [
          { required: true, message: '请选择交易状态', trigger: 'change' }
        ]
      },
      moduleForm: {
        name: '',
        parentName: '',
        parentId: 0,
        investment: '',
        nodeType: '',
        description: '',
        productManager: '',
        developmentManager: '',
        tradeStatus: 0
      },
      orgData: [],
      treedata: [],
      executionMethodList: [],
      nodeTypeList: []
    }
  },
  mounted() {
    // 编辑时数据回显
    if ((Object.keys(this.rowData)).length > 0) {
      this.moduleForm = JSON.parse(JSON.stringify(this.rowData))
    }
    this.getTreeData()
    this.getExecutionMethodList()
    this.getNodeTypeList()
  },
  methods: {
    // 测试方法
    async getExecutionMethodList() {
      const res = await apiBaseDictNoPage({ type: 'EXECUTION_METHOD ' })
      if (!res.isSuccess) return
      this.executionMethodList = res.data
    },
    // 类型
    async getNodeTypeList() {
      const res = await apiBaseDictNoPage({ type: 'TRADE_TYPE' })
      if (!res.isSuccess) return
      this.nodeTypeList = res.data
    },
    getTreeData() {
      this.treedata = this.getTreeList(this.tableData)
    },
    getTreeList(data, obj = []) {
      data.map(item => {
        item.label = item.name
        if (item.id == this.rowData.id) {
          item.isDisabled = true
        }
        if (item.children && item.children.length > 0) {
          this.getTreeList(item.children)
        }
      })
      return data
    },
    // 保存
    save() {
      this.$refs.moduleForm.validate((valid) => {
        if (valid) {
          const param = _.cloneDeep(this.moduleForm)
          param.productId = this.$route.params.productId
          delete param.parentName
          if (this.title == '编辑') { // 编辑保存
            operationModule(param, 'put').then(res => {
              if (res.isSuccess) {
                this.$message.success('修改成功')
                this.close()
                this.$emit('success')
              } else {
                this.$message.warning(res.msg)
              }
            })
          } else { // 新增保存
            operationModule(param, 'post').then(res => {
              if (res.isSuccess) {
                this.$message.success('新增成功')
                this.close()
                this.$emit('success')
              } else {
                this.$message.warning(res.msg)
              }
            })
          }
        }
      })
    },
    close() {
      this.$refs.moduleForm.resetFields()
      this.$emit('update:visible', false)
    }
  }
}
</script>
