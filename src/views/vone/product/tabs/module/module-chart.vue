<!-- 矩形树图 -->
<template>
  <div>

    <vone-empty v-if="empty" class="container" />
    <vone-echarts v-else ref="moduleChart" class="module-chart" :options="options" :height="height" />

  </div>
</template>

<script>

export default {
  name: 'TreeChart',
  props: {
    // 切换图
    tabActive: {
      type: String,
      default: ''
    },
    height: {
      type: String,
      default: '320px'
    },
    data: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      empty: false,
      echartData: [],
      options: {},
      treemapOption: {
        color: ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de', '#3ba272', '#fc8452', '#9a60b4', '#ea7ccc'],
        stateAnimation: {
          duration: 300,
          easing: 'cubicOut'
        },
        series: [
          {
            type: 'treemap',
            id: 'echarts-package-size',
            data: [],
            visibleMin: 1,

            leafDepth: true,
            label: {
              show: true,
              formatter: '{b}'
            },
            itemStyle: {
              borderColor: '#fff'
            },
            levels: [
              {
                itemStyle: {
                  borderWidth: 0,
                  gapWidth: 5
                }
              },
              {
                itemStyle: {
                  gapWidth: 1
                }
              },
              {
                colorSaturation: [0.35, 0.5],
                itemStyle: {
                  gapWidth: 1,
                  borderColorSaturation: 0.6
                }
              }
            ],
            breadcrumb: {
              show: true,
              left: '10%',
              bottom: '3%',
              emptyItemWidth: 25,
              itemStyle: {
                normal: {
                  color: '#fff',
                  borderColor: 'rgba(255,255,255,0.7)',
                  borderWidth: 1,
                  shadowColor: 'rgba(150,150,150,1)',
                  shadowBlur: 3,
                  shadowOffsetX: 0,
                  shadowOffsetY: 0,
                  textStyle: {
                    color: '#000',
                    fontWeight: 'bold'
                  }
                }
              }
            }
          }
        ]
      },
      sunburstOption: {
        color: ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de', '#3ba272', '#fc8452', '#9a60b4', '#ea7ccc'],
        stateAnimation: {
          duration: 300,
          easing: 'cubicOut'
        },
        series: [
          {
            type: 'sunburst',
            data: [],
            radius: [0, '95%'],
            sort: undefined,
            emphasis: {
              focus: 'ancestor'
            },
            levels: [
              {}, {
                r0: '15%',
                r: '35%',
                itemStyle: {
                  borderWidth: 2
                },
                label: {
                  rotate: 'tangential'
                }
              }, {
                r0: '35%',
                r: '70%',
                label: {
                  align: 'right'
                }
              }, {
                r0: '70%',
                r: '72%',
                label: {
                  position: 'outside',
                  padding: 3,
                  silent: false
                },
                itemStyle: {
                  borderWidth: 3
                }
              }
            ]
          }
        ]
      }
    }
  },
  watch: {
    tabActive(val) {
      this.changeType()
    },
    data: {
      handler(val) {
        if (!val) return
        if (val) {
          this.getModuleTrees(val)
        }
      },
      deep: true,
      immediate: true
    }
  },
  mounted() {
    this.options = this.tabActive === 'sunChart' ? this.sunburstOption : this.treemapOption
  },
  methods: {
    changeType() {
      this.options = this.tabActive === 'sunChart' ? this.sunburstOption : this.treemapOption
      this.options.series[0].name = this.tabActive === 'treeChart' ? this.echartData[0].name : ''
      this.options.series[0].data = this.echartData[0].children
      this.$refs.moduleChart.updateChartView()
    },
    getModuleTrees(e) {
      this.$nextTick(() => {
        if (!e.length) {
          return this.empty == true
        }
        this.echartData = e
        this.options.series[0].name = this.tabActive === 'treeChart' ? e[0]?.name : ''
        this.options.series[0].data = e[0]?.children
        console.log()
        if (!e[0].children.length) {
          this.empty = true
        } else {
          this.empty = false
        }
        this.$refs.moduleChart.updateChartView()
      })
    }
  }
}
</script>
<style lang='scss' scoped>
.module-chart {
  height: 100%;
  width: 100%;
  padding-top: 48px;
}
.container{
  height:calc(100vh - 176px)
}
</style>
