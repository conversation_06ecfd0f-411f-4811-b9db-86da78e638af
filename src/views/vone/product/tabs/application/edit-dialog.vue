<template>
  <el-dialog class="dialogContainer" title="关联服务应用" :visible="visible" width="40%" :close-on-click-modal="false" :close-on-press-escape="false" @close="close">
    <!-- 表单部分 -->
    <el-form ref="appForm" :model="appForm">
      <el-form-item label="服务应用">
        <el-select v-model="appForm.applicationIds" multiple filterable placeholder="请选择服务应用">
          <el-option v-for="item in appList" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
      </el-form-item>
    </el-form>
    <!-- 表单结束，按钮操作部分 -->
    <span slot="footer" class="dialog-footer">
      <el-button size="small" @click="$emit('update:visible', false)">取消</el-button>
      <el-button type="primary" size="small" @click="save">确定</el-button>
    </span>
  </el-dialog>
</template>
<script>
import { operationApp, searchAppForProduct } from '@/api/vone/product'
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      appForm: {
        applicationIds: []
      },
      appList: []
    }
  },
  mounted() {
    this.searchAppForProduct()
  },
  methods: {
    // 获取可以关联的服务应用
    searchAppForProduct() {
      searchAppForProduct({ productId: this.$route.params.id }).then(res => {
        if (res.isSuccess) {
          const data = res.data.filter(item => !item.isproduct)
          this.appList = data
        } else {
          this.$message.warning(res.msg)
        }
      })
    },
    // 保存
    save() {
      const param = this.appForm
      const productId = this.$route.params.productId
      operationApp(param.applicationIds, productId, 'post').then(res => {
        if (res.isSuccess) {
          this.$message.success('关联成功')
          this.close()
          this.$emit('success')
        } else {
          this.$message.warning(res.msg)
        }
      })
    },
    close() {
      this.$refs.appForm.resetFields()
      this.$emit('update:visible', false)
    }
  }
}
</script>
