<template>
  <page-wrapper>
    <vone-search-wrapper>
      <template slot="actions">
        <el-button :disabled="!$permission('product_application_connent')" type="primary" icon="iconfont el-icon-tips-plus-circle" @click="addServer">关联服务应用</el-button>
      </template>
    </vone-search-wrapper>

    <vone-cards ref="pro-server-card" v-loading="loading" :data="tableData" :row-count="4" height="calc(100vh - 182px)" @updateData="getProAppList">
      <template v-slot="{ row }">
        <div :class="[ !$permission('product_application_details') ?'disabledLinkCls':'linkCls']" @click="toConfigDetails(row)">
          <vone-card :title="row.name" :title-content="row.name" :actions="rowActions">
            <template v-slot:icon>
              <i class="iconfont el-icon-application-setting" style="color:#409EFF" />
            </template>
            <el-row class="cardText">
              <div class="cardText">应用编号:&nbsp;&nbsp;{{ row.id }}</div>
              <div class="cardText">更新时间:&nbsp;&nbsp;{{ row.updateTime }}</div>
              <div class="cardText text-over">描述 :&nbsp;&nbsp;{{ row.description }}</div>
            </el-row>
            <template v-slot:desc>
              <span>
                <vone-user-avatar :avatar-path="getUserInfo(row)?getUserInfo(row).avatarPath :''" :name="getUserInfo(row)?getUserInfo(row).name :''" />
              </span>

            </template>
          </vone-card>
        </div>
      </template>
    </vone-cards>
    <edit-dialog v-if="visible" :visible.sync="visible" @success="getProAppList" />
  </page-wrapper>
</template>
<script>

import { searchApp, operationApp } from '@/api/vone/product'
import EditDialog from './edit-dialog.vue'

export default {
  components: {
    EditDialog
  },
  data() {
    return {
      loading: false,
      tableData: {},
      visible: false,
      rowActions: [
        {
          disabled: !this.$permission('product_application_del'),
          text: '取消关联',
          type: 'text',
          icon: 'iconfont el-icon-edit-unrelate',
          onClick: ({ row }) => this.deleteById(row)
        }
      ]
    }
  },
  computed: {
    getUserInfo() {
      return function(row) {
        return row?.responsible && row?.echoMap?.responsible
      }
    }
  },

  mounted() {
    this.getProAppList()
  },
  methods: {
    async getProAppList() {
      this.loading = true
      let params = {}
      const tableAttr = this.$refs['pro-server-card'].exportTableQueryData()
      params = {
        ...tableAttr,
        extra: {},
        model: { productId: this.$route.params.productId }
      }
      const res = await searchApp(params)
      this.loading = false
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }

      this.tableData = res.data
    },
    // 新建服务应用
    addServer() {
      this.visible = true
    },
    // 查看
    toConfigDetails(row) {
      if (!this.$permission('product_application_details')) return
      if (!this.$permission('cmdb_server_detail_view')) {
        this.$message.warning('当前登录用户没有【CMDB】模块【查看服务应用详情】的权限，请联系管理员授权')
        return
      }

      this.$router.push({
        path: `/cmdb/server/editServer/${row.id}/1`
        // name: 'product_application_details',
        // params: { systemId: row.id, type: 1 }
      })
    },
    // 删除
    async deleteById(row) {
      await this.$confirm(`确定取消关联该服务应用吗?`, '提示', {
        type: 'warning',
        closeOnClickModal: false
      }).then(async(actions) => {
        const res = await operationApp([row.id], this.$route.params.productId, 'delete')
        if (!res.isSuccess) {
          return this.$message.error(res.msg)
        }
        this.getProAppList()
        this.$message.success('取消关联成功')
      })
    }

  }
}
</script>

<style lang="scss" scoped>
.cardText {
  padding-left: 5px;
  padding-top: 5px;
  color: #909399;
  font-size: 12px;
}

.linkCls {
  cursor: pointer;
}
.disabledLinkCls {
  cursor: not-allowed;
}
.list-view {
  margin-top: -8px;
}
</style>
