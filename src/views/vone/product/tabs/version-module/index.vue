<template>
  <div class="sectionPageContent">
    <div class="leftSection" :style="{ width: openFlag ? '400px' : '240px' }">
      <div class="header">
        <span>版本信息</span>

        <el-dropdown trigger="click" @command="e => e && e()">
          <el-button class="btnMore"><i class="iconfont el-icon-application-more" /></el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item
              v-for="(item, index) in actions"
              :key="index"
              :icon="item.icon"
              :command="item.fn"
              :disabled="item.disabled"
            >
              {{ item.name }}
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>

        <div class="search">
          <el-popover
            v-model="visible"
            placement="bottom-start"
            width="300"
            popper-class="table-search-form org-search-form"
          >
            <div class="search-main">
              <div class="search-header">
                <span style="flex: 1">筛选</span>
              </div>
              <div class="search-form">
                <el-form inline label-position="top">
                  <el-form-item label="名称" prop="name">
                    <el-input v-model.trim="searchKey" style="width: 100%" placeholder="输入名称" />
                  </el-form-item>
                </el-form>
              </div>
              <div class="footer org-footer">
                <el-button plain @click="reset">重置</el-button>
                <el-button type="primary" @click="searchTree">确定</el-button>
              </div>
            </div>
            <span slot="reference">
              <el-tooltip class="item" effect="dark" content="筛选" placement="top">
                <i
                  :class="['iconfont', 'el-icon-application-filter', visible ? 'active' : '']"
                  style="margin-left: 12px; cursor: pointer"
                />
              </el-tooltip>
            </span>
          </el-popover>

          <el-tooltip class="item" effect="dark" content="新增版本" placement="top">
            <i
              style="margin-left: 8px; cursor: pointer"
              class="iconfont el-icon-tips-plus"
              @click="addVersion"
              :disabled="!$permission('product_version_add')"
            />
          </el-tooltip>
          <el-divider style="margin: 0 10px 0 6px" direction="vertical" />
          <el-tooltip v-if="!openFlag" class="item" effect="dark" content="展开" placement="top">
            <i class="iconfont el-icon-direction-menu-unfold" @click="openFlag = !openFlag" />
          </el-tooltip>
          <el-tooltip v-else class="item" effect="dark" content="收起" placement="top">
            <i class="iconfont el-icon-direction-menu-fold" @click="openFlag = !openFlag" />
          </el-tooltip>
        </div>
      </div>
      <div class="treeContent">
        <vone-empty v-if="treeData.length == 0" />
        <el-tree
          v-else
          ref="teamTree"
          v-loading="treeLoading"
          class="tree custom-tree-icon"
          :data="treeData"
          node-key="id"
          default-expand-all
          :highlight-current="true"
          :expand-on-click-node="false"
          :current-node-key="currentNodeData.id"
          :filter-node-method="filterNode"
          @node-click="changeNode"
        >
          <span slot-scope="{ node, data }" class="custom-tree-node">
            <el-tooltip
              v-if="{ width: openFlag ? '400' : '240' }"
              :content="data.label"
              placement="top"
              class="node-label"
            >
              <span class="label">{{ data.label }}</span>
            </el-tooltip>
            <span class="manager">
              <el-tag effect="dark">{{
                data.nodeType === 0 ? convertStateName(data.stateId) : data.nodeType === 1 ? '模块' : '功能'
              }}</el-tag>
            </span>
            <span class="operation-tree-icon">
              <template v-if="data.nodeType === 0">
                <el-tooltip content="新增功能模块" placement="top">
                  <el-button
                    type="text"
                    size="mini"
                    icon="iconfont el-icon-tips-plus-circle"
                    :disabled="!$permission('product_module_add')"
                    @click.stop="addModule(data)"
                  />
                </el-tooltip>
                <el-tooltip content="编辑版本" placement="top">
                  <el-button
                    type="text"
                    size="mini"
                    icon="iconfont el-icon-application-edit"
                    :disabled="!$permission('product_version_edit')"
                    @click.stop="editVersion(data)"
                  />
                </el-tooltip>
                <el-tooltip content="删除版本" placement="top">
                  <el-button
                    type="text"
                    size="mini"
                    icon="iconfont el-icon-application-delete"
                    :disabled="!$permission('product_version_del')"
                    @click.stop="removeVersion(data)"
                  />
                </el-tooltip>
              </template>
              <template v-else>
                <el-tooltip content="新增功能模块" placement="top">
                  <el-button
                    type="text"
                    size="mini"
                    icon="iconfont el-icon-tips-plus-circle"
                    :disabled="!$permission('product_module_add')"
                    @click.stop="addChildModule(node, data)"
                  />
                </el-tooltip>
                <el-tooltip content="编辑功能模块" placement="top">
                  <el-button
                    type="text"
                    size="mini"
                    icon="iconfont el-icon-application-edit"
                    :disabled="!$permission('product_module_edit')"
                    @click.stop="editModule(data)"
                  />
                </el-tooltip>
                <el-tooltip content="删除功能模块" placement="top">
                  <el-button
                    type="text"
                    size="mini"
                    icon="iconfont el-icon-application-delete"
                    :disabled="!$permission('product_module_del')"
                    @click.stop="removeModule(data)"
                  />
                </el-tooltip>
              </template>
            </span>
          </span>
        </el-tree>
      </div>
    </div>

    <div class="rightSection">
      <div class="rightTopSection">
        <div class="title">
          {{ currentNodeData && currentNodeData.name }}
        </div>
        <div class="detail">
          <template v-if="currentVersionInfo">
            <span>
              版本状态
              <p>
                {{ convertStateName(currentVersionInfo.stateId) }}
              </p>
            </span>
            <span>
              计划开始时间
              <p>
                {{ currentVersionInfo.planStime }}
              </p>
            </span>
            <span>
              计划发布时间
              <p>
                {{ currentVersionInfo.planRtime }}
              </p>
            </span>
            <span>
              发布时间
              <p>
                {{ currentVersionInfo.releaseTime }}
              </p>
            </span>
          </template>

          <template v-if="currentModuleInfo">
            <span>
              类型
              <p>
                {{ currentModuleInfo.nodeType === 1 ? '模块' : '功能' }}
              </p>
            </span>
            <span>
              描述
              <p>
                {{ currentModuleInfo.description || '' }}
              </p>
            </span>
          </template>
        </div>
      </div>
      <div class="rightBottomSection">
        <vone-search-wrapper>
          <template slot="search">
            <vone-search-dynamic
              v-if="defaultFileds.length"
              table-search-key="verson-module-issue-table"
              :model.sync="formData"
              :extra.sync="extraData"
              :table-ref="$refs['issue-table']"
              :hide-columns="tableOptions.hideColumns"
              :default-fileds.sync="defaultFileds"
              show-basic
              :show-column-sort="false"
              @getTableData="getTableData"
            />
          </template>
        </vone-search-wrapper>

        <main :style="{ height: tableHeight }">
          <vxe-table
            ref="issue-table"
            class="vone-vxe-table"
            border
            resizable
            height="auto"
            show-overflow="tooltip"
            :loading="tableLoading"
            :empty-render="{ name: 'empty' }"
            :data="tableData.records"
            :column-config="{ minWidth: '120px' }"
            row-id="id"
          >
            <vxe-column
              title="标题"
              field="name"
              min-width="480"
              fixed="left"
              class-name="name_col custom-title-style"
              show-overflow="ellipsis"
            >
              <template #default="{ row }">
                <span v-if="row.delay" style="position: absolute; left: 3px">
                  <el-tooltip :open-delay="500" content="当前工作项已延期" placement="top">
                    <i class="el-icon-warning-outline color-danger ml-2" />
                  </el-tooltip>
                </span>
                <el-tooltip
                  v-showWorkItemTooltips
                  :content="row.code + ' ' + row.name"
                  placement="top-start"
                  :visible-arrow="false"
                >
                  <span class="custom-title-main" @click="toRouter(row)" style="padding-left: 10px">
                    <i
                      v-if="row.typeCode && row.echoMap && row.echoMap.typeCode"
                      :class="`iconfont ${row.echoMap.typeCode.icon} custom-title-style-icon `"
                      :style="{
                        color: `${row.echoMap.typeCode ? row.echoMap.typeCode.color : '#ccc'}`
                      }"
                    />
                    <span class="custom-title-style-text">{{ row.code + ' ' + row.name }}</span>
                  </span>
                </el-tooltip>
              </template>
            </vxe-column>
            <vxe-column field="stateCode" title="状态" width="100" sortable>
              <template #default="{ row, rowIndex }">
                <issueStatus
                  v-if="row"
                  :workitem="row"
                  :no-permission="true"
                  @changeFlow="editRowStatus(row, rowIndex)"
                />
              </template>
            </vxe-column>

            <vxe-column title="负责人" field="leadingBy" min-width="150">
              <template #default="{ row }">
                <span>
                  <vone-remote-user
                    v-model="row.leadingBy"
                    class="remoteuser"
                    :default-data="[row.echoMap.leadingBy]"
                    :disabled="true"
                    @change="workitemChange(row, $event, 'leadingBy')"
                  />
                </span>
              </template>
            </vxe-column>
            <vxe-column field="handleBy" title="处理人" width="120">
              <template #default="{ row }">
                <span>
                  <vone-remote-user
                    v-model="row.handleBy"
                    class="remoteuser"
                    :default-data="[row.echoMap.handleBy]"
                    :disabled="true"
                    @change="workitemChange(row, $event, 'handleBy')"
                  />
                </span>
              </template>
            </vxe-column>
            <vxe-column field="putBy" title="提出人" width="120">
              <template #default="{ row }">
                <span>
                  <vone-remote-user
                    v-model="row.putBy"
                    class="remoteuser"
                    :default-data="[row.echoMap.putBy]"
                    :disabled="true"
                    @change="workitemChange(row, $event, 'putBy')"
                  />
                </span>
              </template>
            </vxe-column>
            <vxe-column field="createTime" title="提出时间" width="120" show-overflow-tooltip sortable>
              <template #default="{ row }">
                <span v-if="row.createTime">
                  {{ dayjs(row.createTime).format('YYYY-MM-DD') }}
                </span>
                <span v-else>{{ row.createTime }}</span>
              </template>
            </vxe-column>
            <vxe-column field="priorityCode" title="优先级" width="100" sortable>
              <template #default="{ row }">
                <vone-icon-select
                  v-model="row.priorityCode"
                  :data="prioritList"
                  filterable
                  clearable
                  style="width: 100%"
                  class="userList"
                  :no-permission="true"
                >
                  <el-option v-for="item in prioritList" :key="item.key" :label="item.name" :value="item.code">
                    <i
                      :class="`iconfont ${item.icon}`"
                      :style="{
                        color: item.color,
                        fontSize: '16px',
                        paddingRight: '6px'
                      }"
                    />
                    {{ item.name }}
                  </el-option>
                </vone-icon-select>
              </template>
            </vxe-column>
            <vxe-column field="sourceCode" title="需求来源" show-overflow-tooltip width="100">
              <template #default="{ row }">
                <span v-if="row.sourceCode && row.sourceCode && row.echoMap.sourceCode">
                  {{ row.echoMap.sourceCode.name }}
                </span>
                <span v-else>{{ row.sourceCode }}</span>
              </template>
            </vxe-column>
            <vxe-column show-overflow-tooltip field="projectId" title="归属项目" width="90">
              <template #default="{ row }">
                <span v-if="row.projectId && row.echoMap && row.echoMap.projectId">
                  {{ row.echoMap.projectId.name }}
                </span>
                <span v-else>{{ row.projectId }}</span>
              </template>
            </vxe-column>

            <vxe-column title="功能模块" field="typeCode" min-width="100">
              <template v-slot="{ row }">
                {{ row.echoMap.typeCode ? row.echoMap.typeCode.name : row.typeCode }}
              </template>
            </vxe-column>

            <vxe-column field="tag" title="标签" width="150" show-overflow-tooltip>
              <template #default="{ row }">
                <span v-for="(item, index) in row.tag" :key="index">
                  <el-tag style="margin-right: 6px" type="success">
                    {{ item }}
                  </el-tag>
                </span>
              </template>
            </vxe-column>
          </vxe-table>
        </main>
        <vone-pagination ref="pagination" :total="tableData.total" @update="getTableData" />
      </div>
    </div>

    <!-- 版本弹框 -->
    <version-dialog
      v-if="versionDialog.visible"
      :visible.sync="versionDialog.visible"
      :title="versionDialog.title"
      :row-data="versionDialog.rowData"
      :isShowInheritanceVersion="versionDialog.isShowInheritanceVersion"
      :versionList="treeData"
      @success="getTreeData"
    />

    <!-- 功能模块弹框 -->
    <module-dialog
      v-if="moduleDialog.visible"
      :visible.sync="moduleDialog.visible"
      :title="moduleDialog.title"
      :row-data="moduleDialog.rowData"
      :treedata="treeData"
      @success="getTreeData"
    />
    <vone-import-file
      v-if="importParam.visible"
      v-bind="importParam"
      :visible.sync="importParam.visible"
      @success="getTreeData({}, 'edit')"
    />
  </div>
</template>
<script>
import { queryFieldList } from '@/api/common'
import { operationVersion, operationModule } from '@/api/vone/product'
import { apiAlmIssuePage } from '@/api/vone/project/issue'
import { getAlmGetTypeNoPage, apiAlmPriorityNoPage } from '@/api/vone/alm/index'
import { download, gainTreeList } from '@/utils'
import { apiBaseFileLoad } from '@/api/vone/base/file'
import { getWorkItemState } from '@/api/vone/project/index'
import setDataMixin from '@/mixin/set-data'
import issueStatus from '@/views/vone/project/common/change-status/index.vue'
import VersionDialog from './version-dialog.vue'
import ModuleDialog from './module-dialog.vue'
import { getVersionModuleTreeByProductId, getProductDetailbyId, getVersionModuleInfo } from '@/api/vone/product/index'

export default {
  components: {
    VersionDialog,
    ModuleDialog,
    issueStatus
  },
  mixins: [setDataMixin],
  data() {
    return {
      flieLoading: false,
      importParam: { visible: false }, // 导入
      actions: [
        {
          name: '导入',
          icon: 'iconfont el-icon-edit-import',
          fn: this.imPort,
          disabled: !this.$permission('product_version_module_import')
        },
        {
          name: '导出',
          icon: 'iconfont el-icon-edit-export',
          fn: this.exportFlie,
          disabled: !this.$permission('product_version_module_export')
        }
      ],
      treeLoading: false,
      treeData: [],
      formData: {
        orgId: '',
        name: '',
        account: ''
      },
      visible: false,
      searchKey: '',
      currentNodeData: {},
      openFlag: false,
      versionDialog: {
        visible: false,
        title: '',
        isShowInheritanceVersion: true,
        rowData: {}
      },
      moduleDialog: {
        visible: false,
        title: '',
        rowData: {}
      },
      defaultFileds: [],
      prioritList: [],
      formData: {},
      extraData: {},
      tableLoading: false,
      tableData: {
        records: []
      },
      tableOptions: {
        isOperation: true,
        isSelection: true,
        hideColumns: [
          'files',
          'code',
          'description',
          'delay',
          'estimatePoint',
          'planStime',
          'projectId',
          'ideaId',
          'sourceCode',
          'rateProgress',
          'typeCode',
          'putBy',
          'leadingBy'
        ]
      },
      projectId: this.$route.params?.id || '',
      productId: this.$route.params?.productId || '',
      // 当前选中的版本的详情
      currentVersionInfo: null,
      // 当前选中的模块的详情
      currentModuleInfo: null
    }
  },
  computed: {
    tableHeight() {
      const height = (this.extraData?.height || 0) + 'px'
      return `calc(${this.$reduceTableHeight} - ${height} - 94px)`
    }
  },
  created() {
    this.getTreeData()
    this.getPrioritList()
    this.getIssueType()
    // this.getAllStatus()
    this.getQueryFieldList()
  },
  methods: {
    toRouter(val) {
      const newpage = this.$router.resolve({
        path: `/project/issue/${val.code}/${val.typeCode}/${val.projectId}`,
        query: {
          showDialog: true,
          queryId: val.id,
          rowTypeCode: val.typeCode,
          stateCode: val.stateCode,
          projectId: val.projectId,
          type: 'info'
        }
      })
      window.open(newpage.href, '_blank')
    },
    async getQueryFieldList() {
      const fixedField = ['name', 'handleBy', 'stateCode', 'createTime', 'typeCode']
      const form = {
        productId: this.productId,
        typeClassify: 'ISSUE'
      }
      const res = await queryFieldList(form)
      if (!res.isSuccess) {
        return
      }
      const vId = ['productId', 'planId']
      const filter = res.data.filter(r => r.isSearch && r.key != 'projectId')
      filter.forEach(element => {
        if (element.key == 'productModuleFunctionId') {
          element.type.code = 'TREE'
        }
        element.isBasicFilter = !fixedField.includes(element.key)
        element.multiple = element.type.code != 'ICON'

        element.valueType = vId.includes(element.key) ? 'id' : null
      })
      this.defaultFileds = filter
    },
    convertStateName(flag) {
      return ['', '规划中', '进行中', '发布成功', '发布失败'][flag] || ''
    },
    filterNode(value, data) {
      if (!value) return true
      return data.label.indexOf(value) !== -1
    },
    resizableChangeEvent(column, refName) {
      if (column.field == 'name') {
        this.$refs[refName].refreshColumn()
      }
    },
    // 查询需求类型
    async getIssueType() {
      const res = await getAlmGetTypeNoPage('ISSUE')
      if (!res.isSuccess) {
        return
      }
      this.setData(this.defaultFileds, 'typeCode', res.data)
    },
    // 查优先级
    async getPrioritList() {
      const res = await apiAlmPriorityNoPage()
      if (!res.isSuccess) {
        return
      }
      this.prioritList = res.data
    },
    // 查询全部工作流状态
    async getAllStatus() {
      const res = await getWorkItemState({
        typeClassify: 'ISSUE'
      })
      if (!res.isSuccess) {
        return
      }
      this.setData(this.defaultFileds, 'stateCode', res.data)
    },
    // 初始化进入页面列表
    async getTableData() {
      const tableAttr = this.$refs?.pagination?.pageObj || {
        current: 1,
        size: 20
      }

      const params = {
        ...tableAttr,
        extra: {
          ...this.extraData
        },
        model: { ...this.formData, productId: [this.productId] }
      }

      // 当前选中的是版本节点
      if (this.currentNodeData.nodeType === 0) {
        params.model.productVersionId = [this.currentNodeData.id]
      }
      // 当前选中的是功能模块
      else {
        params.model.productModuleFunctionId = [this.currentNodeData.id]
      }

      this.tableLoading = true
      const res = await apiAlmIssuePage(params)
      this.tableLoading = false
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      res.data.records.forEach(element => {
        element.tag =
          element.tagId && element.tagId.length && element.echoMap && element.echoMap.tagId
            ? element.echoMap.tagId.map(r => r.name)
            : []
      })
      this.tableData = res.data
    },
    getTreeData() {
      this.treeLoading = true
      getVersionModuleTreeByProductId(this.productId).then(res => {
        this.treeLoading = false
        this.treeData = gainTreeList(res.data)
        this.changeNode(res.data[0])
        this.getTableData()
      })
    },
    reset() {
      this.visible = false
      this.searchKey = ''
      this.$refs.teamTree.filter()
    },
    searchTree() {
      this.visible = false
      this.$refs.teamTree.filter(this.searchKey)
    },
    addVersion() {
      this.versionDialog.visible = true
      this.versionDialog.title = '新增版本'
      this.versionDialog.isShowInheritanceVersion = true
      this.versionDialog.rowData = null
    },
    editVersion(data) {
      getProductDetailbyId(data.id).then(res => {
        this.versionDialog.visible = true
        this.versionDialog.title = '编辑版本'
        this.versionDialog.isShowInheritanceVersion = false
        this.versionDialog.rowData = Object.assign({}, res.data, {
          productsetVersionId: res.data.echoMap?.productset
        })
      })
    },
    removeVersion(data) {
      if (data.children) {
        return this.$message.warning('当前下有子级,不允许直接删除，请先删除子级')
      }
      this.$confirm(`是否删除版本【${data.name}】?`, '删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        customClass: 'delConfirm',
        showClose: false
      })
        .then(() => {
          operationVersion([data.id], 'delete').then(res => {
            if (res.isSuccess) {
              this.$message.success('删除成功')
              this.getTreeData()
            } else {
              this.$message.warning(res.msg)
            }
          })
        })
        .catch(() => {})
    },
    addModule(data) {
      this.moduleDialog.visible = true
      this.moduleDialog.title = '新增功能模块'
      this.moduleDialog.rowData = {
        productId: this.productId,
        productVersionId: data.id,
        isShowParent: false
      }
    },
    addChildModule(node, data) {
      this.moduleDialog.visible = true
      this.moduleDialog.title = '新增功能模块'
      this.moduleDialog.rowData = {
        productId: this.productId,
        productVersionId: this.findProductVersionId(node),
        isShowParent: true,
        parentId: data.id
      }
    },
    editModule(data) {
      getVersionModuleInfo(data.id).then(res => {
        this.moduleDialog.visible = true
        this.moduleDialog.title = '编辑功能模块'
        this.moduleDialog.rowData = res.data
      })
    },
    removeModule(data) {
      this.$confirm(`是否删除功能模块【${data.name}】?`, '删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        customClass: 'delConfirm',
        showClose: false
      })
        .then(() => {
          operationModule([data.id], 'delete').then(res => {
            if (res.isSuccess) {
              this.$message.success('删除成功')
              this.getTreeData()
            } else {
              this.$message.warning(res.msg)
            }
          })
        })
        .catch(() => {})
    },
    changeNode(nodeData) {
      this.currentNodeData = nodeData

      // 版本信息
      if (nodeData.nodeType === 0) {
        getProductDetailbyId(nodeData.id).then(res => {
          this.currentVersionInfo = res.data
        })
      } else {
        this.currentVersionInfo = null
      }

      // 功能模块信息
      if (nodeData.nodeType !== 0) {
        getVersionModuleInfo(nodeData.id).then(res => {
          this.currentModuleInfo = res.data
        })
      } else {
        this.currentModuleInfo = null
      }

      this.getTableData()
    },
    // 找到底层的module的版本id
    findProductVersionId(node) {
      let id = ''
      if (node.parent.data.nodeType === 0) {
        id = node.parent.data.id
      } else {
        id = this.findProductVersionId(node.parent)
      }
      return id
    },
    // 导入
    imPort() {
      this.importParam = {
        visible: true,
        title: '产品-版本功能模块',
        url: '/api/product/product/version/modlueFunction/excel/downloadImportTemplate',
        importUrl: `/api/product/product/version/modlueFunction/excel/import/${this.productId}`
      }
    },
    // 导出
    async exportFlie() {
      try {
        this.flieLoading = true

        download(
          `版本功能模块信息.xls`,
          await apiBaseFileLoad(
            `/api/product/product/version/modlueFunction/excel/export/${this.productId}`,
            Object.entries(this.formData)
          )
        )

        this.flieLoading = false
      } catch (e) {
        this.flieLoading = false
        return
      }
    }
  }
}
</script>
<style lang="scss" scoped>
@import '~@/styles/variables.scss';
.sectionPageContent {
  display: flex;
  justify-content: center;
}
.org-search-form .search-main {
  .search-header {
    padding: 12px 20px;
    font-weight: 500;
    color: var(--font-main-color);
    border-bottom: 1px solid var(--solid-border-color);
  }
  .search-form {
    padding: 16px;
    .el-form-item {
      width: 100%;
    }
  }
  .org-footer {
    text-align: right;
    padding: 12px 20px;
    border-top: 1px solid var(--disabled-bg-color);
  }
}
.leftSection {
  width: 240px;
  .header {
    padding: 0px 16px;
    height: 48px;
    line-height: 48px;
    border-bottom: 1px solid var(--solid-border-color);
    display: flex;
    span {
      color: var(--font-main-color);
      font-size: 16px;
      font-weight: 500;
      flex: 1;
    }
    .search {
      display: flex;
      align-items: center;
      ::v-deep.el-divider--vertical {
        margin: 0 10px 0 10px;
      }
      .iconfont {
        cursor: pointer;
        color: var(--font-second-color);
      }
      .iconfont:hover {
        color: var(--main-theme-color);
      }
      .iconfont.active {
        color: var(--main-theme-color);
      }
    }
  }
  .treeContent {
    margin-top: 8px;
    height: calc(100vh - #{$nav-top-height} - #{$main-margin} - #{$main-margin} - 57px);
    overflow-y: overlay;
    .custom-tree-node {
      flex: 1;
      width: calc(100% - 90px);
      height: 100%;
      display: flex;
      align-items: center;
    }
    .el-tree-node__content {
      height: 36px;
      color: var(--font-main-color);
      display: inline-block;
      .node-label {
        display: inline-block;
        width: calc(100% - 90px);
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        .svg-icon {
          width: 16px;
          height: 16px;
          vertical-align: -0.2em;
        }
      }
      .manager {
        position: absolute;
        right: 16px;
      }
      .operation-tree-icon {
        .el-button {
          padding: 0px;
          height: unset;
          line-height: unset;
          min-width: unset;
          font-size: 16px;
          color: var(--font-second-color);
        }

        .el-button.is-disabled {
          background-color: unset;
          border: unset;
        }
        .el-button:hover {
          color: var(--main-theme-color);
        }
        opacity: 0;
        position: absolute;
        right: 16px;
      }

      &:hover {
        .manager {
          display: none;
        }
        .operation-tree-icon {
          opacity: 1;
          background: var(--hover-bg-color);
        }
      }
      .svg-icon {
        margin-right: 4px;
      }
    }
  }
}
.rightSection {
  background: none;
  padding: 0;
  box-shadow: none;
}
.rightTopSection {
  border-radius: 4px;
  padding: 16px;
  box-shadow: var(--main-bg-shadow);
  background: var(--main-bg-color);
  height: 84px;
  color: var(--font-main-color);
  .title {
    font-weight: 500;
    line-height: 22px;
    display: flex;
    .svg-icon {
      width: 20px;
      height: 20px;
      margin-right: 8px;
    }
  }
  .detail {
    margin-top: 8px;
    line-height: 22px;
    span {
      color: var(--font-second-color);
      margin-right: 56px;
      display: inline-flex;
    }
    p {
      display: inline-block;
      margin: 0px;
      margin-left: 12px;
      color: var(--font-main-color);
    }
    ::v-deep .avatar span {
      margin-left: 4px;
    }
  }
}
.rightBottomSection {
  position: relative;
  border-radius: 4px;
  padding: 16px 16px 10px 16px;
  box-shadow: var(--main-bg-shadow);
  background: var(--main-bg-color);
  margin-top: 10px;
  height: calc(100vh - #{$nav-top-height} - #{$main-margin} - #{$main-margin} - 94px);
}
// 44px-分页 48px-筛选 36px-表格头
.vone-vxe-table {
  ::v-deep .vxe-table--body-wrapper {
    overflow-y: auto;
  }
}
.tableHeight {
  height: calc(
    100vh - #{$main-margin} - #{$main-margin} - #{$main-padding} - #{$main-padding} - #{$nav-top-height} - 40px - 40px -
      114px
  );
}
::v-deep .el-tree-node__expand-icon {
  color: #777f8e;
}
</style>

<style lang="scss" scoped>
.remoteuser {
  ::v-deep .el-select .el-input .el-input__inner {
    border: none;
  }
  ::v-deep .el-input--small .el-input__inner {
    border: none;
  }
  ::v-deep .el-input__suffix {
    display: none;
  }
}
::v-deep .el-collapse-item__wrap {
  border-bottom: none;
}
::v-deep .el-tree-node__content {
  height: 40px;
}

.table_title {
  display: inline-block;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.noSetting {
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}
::v-deep .hide-checkbox {
  .el-checkbox__input {
    display: none;
  }
}
.count-num {
  display: inline-block;
  height: 18px;
  line-height: 18px;
  font-size: 12px;
  color: #838a99;
  padding: 0px 6px;
  background: #f2f3f5;
  border-radius: 9px;
  margin-left: 8px;
}
.child-table {
  font-size: 14px;
}
</style>
<style>
.userList .el-input__inner {
  padding: 0;
  border: 0;
  padding-left: 25px;
}
.userList .el-input__icon {
  display: none;
}
.userList .el-input__prefix {
  left: 0px;
}
</style>
