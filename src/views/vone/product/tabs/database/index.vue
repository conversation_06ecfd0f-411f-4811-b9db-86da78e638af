<template>
  <page-wrapper>
    <vone-search-wrapper>
      <template slot="search">
        <vone-search-dynamic
          ref="searchForm"
          table-search-key="productDatabase"
          :model.sync="formData"
          :default-fileds.sync="defaultFileds"
          show-basic
          :extra.sync="extraData"
          :table-ref="$refs['productDatabase']"
          @getTableData="getTableData"
        />
      </template>
      <template slot="actions">
        <el-row type="flex" justify="space-between">
          <el-button :disabled="!$permission('product_database_del')" class="buttonMap" @click="deleteAll">批量删除</el-button>
          <el-button class="ml-16" type="primary" icon="iconfont el-icon-tips-plus-circle" :disabled="!$permission('product_database_add')" @click="addDatabase">新增</el-button>
        </el-row>
      </template>
      <template slot="fliter">
        <vone-search-filter
          :extra.sync="extraData"
          :model.sync="formData"
          :default-fileds.sync="defaultFileds"
          @getTableData="getTableData"
        />
      </template>
    </vone-search-wrapper>
    <div :style="{ height: tableHeight }">
      <vxe-table
        ref="productDatabase"
        class="vone-vxe-table"
        border
        height="auto"
        show-overflow="tooltip"
        :loading="pageLoading"
        :empty-render="{ name: 'empty' }"
        :data="tableData.records"
        :column-config="{ minWidth: '120px' }"
        :checkbox-config="{ reserve: true }"
        row-id="id"
      >
        <vxe-column type="checkbox" width="36" align="center" fixed="left" />
        <vxe-column title="文件名称" field="name" min-width="180" />
        <vxe-column title="文件类型" field="type" width="120">
          <template #default="{ row }">
            {{ row.echoMap&& row.echoMap.type&&row.echoMap.type.name }}
          </template>
        </vxe-column>
        <vxe-column title="描述" field="description" min-width="150" />
        <vxe-column title="附件" field="files" min-width="180">
          <template #default="{ row }">
            <el-progress v-if="row.fileId == currentFileInfo(row).fileId" type="circle" :width="32" :stroke-width="4" :percentage="currentFileInfo(row).percentage" />
            <a :style="row.fileId == currentFileInfo(row).fileId ? fileSty : ''" @click="downLoad(row)">{{ row.echoMap&&row.echoMap.fileId&&row.echoMap.fileId.originalFileName }}</a>
          </template>
        </vxe-column>
        <vxe-column title="操作" fixed="right" align="left" width="120">
          <template #default="{ row }">
            <template>
              <el-tooltip class="item" content="编辑" placement="top">
                <el-button type="text" :disabled="!$permission('product_database_edit')" icon="iconfont el-icon-application-edit icon_click" @click="editById(row)" />
              </el-tooltip>
              <el-divider direction="vertical" />
              <el-tooltip class="item" content="删除" placement="top">
                <el-button type="text" :disabled="!$permission('product_database_del')" icon="iconfont el-icon-application-delete icon_click" @click="deleteById(row)" />
              </el-tooltip>
            </template>
          </template>
        </vxe-column>
      </vxe-table>
    </div>
    <vone-pagination
      ref="pagination"
      :total="tableData.total"
      @update="getTableData"
    />
    <!-- 导入数据 -->
    <vone-import-file v-if="importParam.visible" v-bind="importParam" :visible.sync="importParam.visible" @success="getTableData()" />
    <edit-dialog v-bind="databaseParam" :visible.sync="databaseParam.visible" @success="getTableData" />
  </page-wrapper>
</template>

<script>
import { getProductDocumentPage, operationDocument } from '@/api/vone/product/database'
import EditDialog from './edit-dialog.vue'
import { download } from '@/utils'
import { apiBaseFileLoadById } from '@/api/vone/base/file'

export default {
  components: {
    EditDialog
  },
  props: {
    id: {
      type: String,
      default: null
    }
  },
  data() {
    return {
      extraData: {}, defaultFileds: [
        {
          key: 'name',
          name: '名称',
          type: {
            code: 'INPUT'
          },
          placeholder: '请输入名称'
        }
      ],
      formData: {
        productId: this.$route.params.productId
      },
      tableData: {},
      importParam: { visible: false },
      pageLoading: false,

      databaseParam: {
        visible: false
      },
      fileInfo: [],
      fileSty: {
        lineHeight: '32px',
        paddingLeft: '10px'
      }
    }
  },
  computed: {
    tableHeight() {
      const height = (this.extraData?.height || 0) + 'px'
      return `calc(${this.$reduceTableHeight} - ${height})`
    },
    currentFileInfo() {
      return function(row) {
        const currentFileInfo = this.fileInfo.find(item => item.fileId == row.fileId) || {}
        return currentFileInfo
      }
    }
  },
  mounted() {
    // this.getTableData()
  },
  methods: {
    // 批量删除
    async deleteAll() {
      const dateList = this.getVxeTableSelectData('productDatabase')
      if (!dateList.length) {
        this.$message.warning('请选择要删除的数据')
        return
      }
      try {
        await this.$confirm(`是否删除 ${dateList.length} 条数据?`, '批量删除', {
          type: 'warning',
          customClass: 'delConfirm',
          showClose: false
        })
        this.pageLoading = true
        const selectId = dateList.map(r => r.id)
        const res = await operationDocument(selectId, 'delete')
        this.pageLoading = false
        if (!res.isSuccess) {
          this.$message.error(res.msg)
          return
        }
        this.$message.success('删除成功')
        this.getTableData()
      } catch (e) {
        this.pageLoading = false
      }
    },
    // 单个下载
    async downLoad(row) {
      const isFileDownload = this.fileInfo.filter(item => item.fileId == row.fileId)
      if (isFileDownload.length > 0) {
        this.$message.warning('当前附件正在下载，请稍后尝试')
        return
      }
      this.fileInfo.push({
        fileId: row?.fileId,
        fileSize: row?.echoMap?.fileId?.size,
        percentage: 0
      })
      const fileName = `${row.name}.${row.echoMap?.fileId?.suffix || 'doc'}`
      download(fileName, await apiBaseFileLoadById(
        [row.fileId], this.downloadFun, row
      ))
    },
    downloadFun(progressEvent, row) {
      const fileInfoIndex = this.fileInfo.findIndex(item => item.fileId == row.fileId)
      const progressBar = Math.round(progressEvent.loaded / this.fileInfo[fileInfoIndex]?.fileSize * 100)
      if (progressBar >= 100) {
        this.$set(this.fileInfo[fileInfoIndex], 'percentage', 100)
        this.fileInfo.splice(fileInfoIndex, 1)
      } else {
        progressBar && this.$set(this.fileInfo[fileInfoIndex], 'percentage', progressBar)
      }
    },
    toMap() {
      const { productId, productCode } = this.$route.params
      this.$router.push({
        path: `/product/tabs/edition/view/${productCode}/${productId} `
      })
    },
    async getTableData() {
      this.pageLoading = true
      let params = {}
      const pageObj = this.$refs.pagination?.pageObj || { current: 1, size: 20 }
      const sortObj = this.$refs.searchForm?.sortObj
      params = {
        ...pageObj,
        ...sortObj,
        extra: {
          ...this.extraData
        },
        model: { ...this.formData }
      }
      if (this.$route.params.productId) {
        params.model.productId = this.$route.params.productId
      }
      const { data, isSuccess, msg } = await getProductDocumentPage(params)
      this.pageLoading = false
      if (!isSuccess) {
        this.$message.warning(msg)
        return
      }
      this.tableData = data
      if (this.$refs['productDatabase']) this.$refs['productDatabase'].clearCheckboxRow()
    },
    // 编辑
    async editById(row) {
      this.databaseParam = { visible: true, title: '编辑资料', id: row.id }
    },
    // 删除
    async deleteById(row) {
      await this.$confirm(`确定删除【${row.name}】吗？`, '删除', {
        type: 'warning',
        closeOnClickModal: false,
        customClass: 'delConfirm',
        showClose: false
      }).then(async res => {
        const { isSuccess, msg } = await operationDocument([row.id], 'delete')
        if (!isSuccess) {
          this.$message.warning(msg)
          return
        }
        this.$message.success('删除成功')
        this.getTableData()
      })
    },
    // 新增
    addDatabase() {
      this.databaseParam = { visible: true, title: '新增资料' }
    }
  }
}

</script>
<style lang="scss" scoped>
.buttonMap{
	color:#6B7385;
	border: 1px solid #CED1D9;
}
.el-progress {
  float: left;
}
</style>

