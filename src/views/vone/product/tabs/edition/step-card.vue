<template>

  <el-card class="card-wrapper" :class="{'cardSetting':index!==0&&index%2===0,'cardo':index%2!==0,'card1':index===0}">
    <div class="card-header">
      <header>
        <span class="title">{{ form.productVersionName }}</span>
      </header>

      <div class="versionItem">
        <el-button type="text" class="collapse-status" :class="{purple:form.stateId =='1',blue:form.stateId =='2',green:form.stateId =='3',orange:form.stateId =='4'}">
          <span v-if="form.stateId == 1">规划中</span>
          <span v-else-if="form.stateId == 2">进行中</span>
          <span v-else-if="form.stateId == 3">发布成功</span>
          <span v-else-if="form.stateId == 4">发布失败</span>
        </el-button>
        <div>

          <span v-if="form.planRtime" class="timeSize">
            {{ dayjs(form.planRtime).format("YYYY-MM-DD") }}
          </span>
          <span v-else class="noTime">未设置</span>

        </div>
      </div>

      <header>
        <div class="total" @click="toWorkItem(form.issueItemCount )">
          <span class="num">{{ form.issueItemCount }}</span> 条工作项
        </div>
        <div class="total" @click="toReleaseRecord(form)">
          <span class="num">{{ form.releaseList.length }}</span> 条发布记录
        </div>
      </header>
    </div>

    <div class="rowLine" />

    <section>
      <ul v-if="form.releaseList.length">
        <li v-for="item in form.releaseList" :key="item.id">
          <div class="releaseItem">
            <span class="code-text">
              {{ dayjs(item.createTime).format("YYYY-MM-DD") }}
            </span>

            <el-tag type="success">
              {{ item.envKey }}
            </el-tag>
          </div>

          <div class="text-over">
            {{ item.echoMap.pipelineList.map(r=>r.name).join("、") }}
          </div>
        </li>
      </ul>
      <vone-empty v-else />
    </section>
  </el-card>

</template>

<script>
export default {
  props: {
    form: {
      type: Object,
      default: () => { }
    },
    index: {
      type: Number,
      default: null
    }
  },
  data() {
    return {

    }
  },
  methods: {
    toWorkItem(val) {
      if (val == 0) {
        this.$message.warning('当前版本暂无关联工作项')
        return
      }
      const { productId, productCode } = this.$route.params
      this.$router.push({
        path: `/product/tabs/workItem/view/${productCode}/${productId}`
      })
    },
    toReleaseRecord(data) {
      if (!data.releaseList.length) {
        this.$message.warning('当前版本暂无发布记录')
        return
      }
      const { productId, productCode } = this.$route.params
      this.$router.push({
        path: `/product/version/detail/${productCode}/${productId}/${data.productVersion.id}`,
        query: { active: 'record' }
      })
    }
  }

}
</script>

<style lang="scss" scoped>
::v-deep {
  .el-card__body {
    padding: 0;
  }
}
.card1 {
  margin-top: 10px;
  margin-left: -40px;
}
.cardSetting {
  margin-top: 10px;
  margin-left: -120px;
}
.cardo {
  margin-top: -380px;
  margin-left: -120px;
}
.card-header {
  padding: 12px 16px;
}
.card-wrapper {
  width: 240px;
  .versionItem {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    height: 24px;
    gap: 0 8px;
    margin: 8px 0;
		.timeSize{
			font-size: 14px;
			font-weight: 400;
		}
  }

  header {
    display: flex;
    height: 24px;
    line-height: 24px;
    .title {
      color: #1d2129;
      font-weight: 500;
			font-size: 14px;
    }
    .total {
			height: 22px;
			line-height: 22px;
      font-size: 12px;
      font-weight: 400;
      cursor: pointer;
      margin-right: 5px;
      background-color: var(--hover-bg-color);
      padding: 0 6px;
      border-radius: 2px;
      color: var(--auxiliary-font-color);
      &:hover {
        color: #fff;
        background-color: var(--main-theme-color);
        .num {
          color: #fff;
        }
      }
      .num {
        font-weight: 500;
        color: var(--main-theme-color);
      }
    }
  }

  .rowLine {
    height: 4px;
    width: calc(100% + 32px);
    background-color: var(--el-divider);
  }

  .collapse-status {
    background: #ffeecf;
    color: #fa962b;
    height: 24px;
		font-size: 12px;
		font-weight: 500;
		padding:0 8px;
		min-width: auto;
		border-radius: 2px;

    &.blue {
      color: #64befa;
      background: rgba(100, 190, 250, 0.12);
    }
    &.purple {
      color: #bd7ffa;
      background: rgba(189, 127, 250, 0.12);
    }
    &.green {
      color: #3cb540;
      background: rgba(60, 181, 64, 0.12);
    }
    &.orange {
      color: #fc9772;
      background: rgba(252, 151, 114, 0.12);
    }
  }
  section {
    height: 115px;
    margin: 12px 16px;
    overflow-y: auto;
    ul {
      li {
			line-height: 22px;
       padding-bottom: 8px;
        .code-text {
          color: var(--auxiliary-font-color);
        }
        & + li {
					padding-bottom: 0;
          padding-top: 8px;
        }
      }
      li:not(:last-child) {

        border-bottom: 1px solid var(--el-divider);
      }
    }
  }
}
.releaseItem {
	height: 22px;
	line-height: 22px;
	margin-bottom:4px;
  display: flex;
  justify-content: space-between;
  align-items: center;
	.el-tag{
		padding:0 6px;
		height: 18px;
		display: flex;
		align-items: center;
	}
}
.noTime {
  color: var(--placeholder-color);
}
</style>
