<template>
  <page-wrapper>
    <vone-search-wrapper>
      <template slot="search">
        <vone-search-dynamic
          ref="searchForm"
          table-search-key="productVersion"
          :model.sync="formData"
          :default-fileds.sync="defaultFileds"
          show-basic
          :extra.sync="extraData"
          :table-ref="$refs['productVersion']"
          @getTableData="getTableData"
        />
      </template>
      <template slot="actions">
        <el-row type="flex" justify="space-between">
          <el-button class="buttonMap" @click="toMap">版本路线图</el-button>
          <!-- <el-dropdown class="ml-16" trigger="click" @command="(e) => e && e()">
            <el-button class="optionBtn">操作<i class="iconfont el-icon-direction-down el-icon--right" /></el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item v-for="(item, index) in actions" :key="index" :icon="item.icon" :command="item.fn" :disabled="item.disabled">
                {{ item.name }}
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown> -->
          <el-button
            class="ml-16"
            type="primary"
            icon="iconfont el-icon-tips-plus-circle"
            :disabled="!$permission('product_version_add')"
            @click="addPlain"
            >新增</el-button
          >
        </el-row>
      </template>
      <template slot="fliter">
        <vone-search-filter
          :extra.sync="extraData"
          :model.sync="formData"
          :default-fileds.sync="defaultFileds"
          @getTableData="getTableData"
        />
      </template>
    </vone-search-wrapper>
    <div :style="{ height: tableHeight }">
      <vxe-table
        ref="productVersion"
        class="vone-vxe-table"
        border
        height="auto"
        show-overflow="tooltip"
        :loading="pageLoading"
        :empty-render="{ name: 'empty' }"
        :data="tableData.records"
        :column-config="{ minWidth: '120px' }"
        :checkbox-config="{ reserve: true }"
        row-id="id"
      >
        <vxe-column title="版本名称" field="name" min-width="150">
          <template #default="{ row }">
            <a @click="jumpDetail(row)">{{ row.name }}</a>
          </template>
        </vxe-column>
        <vxe-column title="版本状态" field="stateId" min-width="150">
          <template slot-scope="scope">
            <el-button
              type="text"
              class="collapse-status"
              :class="{
                purple: scope.row.stateId == '1',
                blue: scope.row.stateId == '2',
                green: scope.row.stateId == '3',
                orange: scope.row.stateId == '4',
              }"
              ><span v-if="scope.row.stateId == 1">规划中</span>
              <span v-if="scope.row.stateId == 2">进行中</span>
              <span v-if="scope.row.stateId == 3">发布成功</span>
              <span v-if="scope.row.stateId == 4">发布失败</span>
            </el-button>
          </template>
        </vxe-column>
        <vxe-column title="计划开始时间" field="planStime" min-width="150">
          <template slot-scope="scope">
            {{ scope.row.planStime ? dayjs(scope.row.planStime).format('YYYY-MM-DD') : '' }}
          </template>
        </vxe-column>
        <vxe-column title="发布时间" field="planRtime" min-width="150">
          <template slot-scope="scope">
            {{ scope.row.releaseTime ? dayjs(scope.row.releaseTime).format('YYYY-MM-DD') : '' }}
          </template>
        </vxe-column>
        <vxe-column title="操作" fixed="right" align="left" width="120">
          <template #default="{ row }">
            <template>
              <el-tooltip class="item" content="编辑" placement="top">
                <el-button
                  type="text"
                  :disabled="!$permission('product_version_edit')"
                  icon="iconfont el-icon-application-edit icon_click"
                  @click="editById(row)"
                />
              </el-tooltip>
              <el-divider direction="vertical" />
              <el-tooltip class="item" content="删除" placement="top">
                <el-button
                  type="text"
                  :disabled="!$permission('product_version_del')"
                  icon="iconfont el-icon-application-delete icon_click"
                  @click="deleteById(row)"
                />
              </el-tooltip>
            </template>
          </template>
        </vxe-column>
      </vxe-table>
    </div>
    <vone-pagination ref="pagination" :total="tableData.total" @update="getTableData" />
    <!-- 导入数据 -->
    <vone-import-file
      v-if="importParam.visible"
      v-bind="importParam"
      :visible.sync="importParam.visible"
      @success="getTableData()"
    />
    <edit-dialog :visible.sync="visible" :row-data="rowData" @success="getTableData" />
  </page-wrapper>
</template>

<script>
import { getProductVersion, operationVersion, getProductDetailbyId } from '@/api/vone/product'
import EditDialog from './edit-dialog.vue'
import { download } from '@/utils'
import { dataExport } from '@/api/vone/base/file'

export default {
  components: {
    EditDialog,
  },
  props: {
    id: {
      type: String,
      default: null,
    },
  },
  data() {
    return {
      extraData: {},
      defaultFileds: [
        {
          key: 'name',
          name: '名称',
          type: {
            code: 'INPUT',
          },
          placeholder: '请输入版本名称',
        },
      ],
      formData: {
        productId: this.$route.params.productId,
      },
      visible: false,
      rowData: {},
      tableData: {},
      actions: [
        {
          name: '导入',
          icon: 'iconfont el-icon-edit-import',
          fn: this.imPort,
          disabled: !this.$permission('source_data_Import'),
        },
        {
          name: '导出',
          icon: 'iconfont el-icon-edit-export',
          disabled: !this.$permission('source_data_export'),
          fn: this.exportFlie,
        },
      ],
      importParam: { visible: false },
      pageLoading: false,
    }
  },
  computed: {
    tableHeight() {
      const height = (this.extraData?.height || 0) + 'px'
      return `calc(${this.$reduceTableHeight} - ${height})`
    },
  },
  mounted() {
    // this.getTableData()
  },
  methods: {
    // 导入
    imPort() {
      this.importParam = {
        visible: true,
        title: '版本信息',
        url: `/api/product/product/version/excel/downloadImportTemplate`,
        importUrl: `/api/product/product/version/excel/import`,
        data: { productId: this.$route.params.productId },
      }
    },
    // 导出
    async exportFlie() {
      try {
        this.pageLoading = true

        download(
          `版本信息.xls`,
          await dataExport(`/api/product/product/version/excel/export`, { productId: this.$route.params.productId })
        )

        this.pageLoading = false
      } catch (e) {
        this.tableLoading = false
        return
      }
    },
    toMap() {
      const { productId, productCode } = this.$route.params
      this.$router.push({
        path: `/product/tabs/edition/view/${productCode}/${productId} `,
      })
    },
    async getTableData() {
      this.pageLoading = true
      let params = {}
      const pageObj = this.$refs.pagination?.pageObj || { current: 1, size: 20 }
      const sortObj = this.$refs.searchForm?.sortObj
      params = {
        ...pageObj,
        ...sortObj,
        extra: {
          ...this.extraData,
        },
        model: { ...this.formData },
      }
      if (this.$route.params.productId) {
        params.model.productId = this.$route.params.productId
      }
      const { data, isSuccess, msg } = await getProductVersion(params)
      this.pageLoading = false
      if (!isSuccess) {
        this.$message.warning(msg)
        return
      }
      this.tableData = data
    },
    jumpDetail(row) {
      const { productCode, productId } = this.$route.params
      this.$router.push({
        path: `/product/version/detail/${productCode}/${productId}/${row.id}`,
        query: {
          planRtime: row.planRtime,
        },
      })
    },
    // 编辑
    async editById(row) {
      const res = await getProductDetailbyId(row.id)
      if (!res.isSuccess) {
        this.$message.error(res.msg)
      }
      this.visible = true
      const params = { ...res.data }
      if (res.data.echoMap && res.data.echoMap.productset) {
        params['productsetVersionId'] = res.data.echoMap.productset
      }
      this.rowData = params
    },
    // 删除
    async deleteById(row) {
      await this.$confirm(`确定删除【${row.name}】吗？`, '删除', {
        type: 'warning',
        closeOnClickModal: false,
        customClass: 'delConfirm',
        showClose: false,
      }).then(async res => {
        const { isSuccess, msg } = await operationVersion([row.id], 'delete')
        if (!isSuccess) {
          this.$message.warning(msg)
          return
        }
        this.$message.success('删除成功')
        this.getTableData()
      })
    },
    // 新增
    addPlain() {
      this.visible = true
      this.rowData = {}
    },
  },
}
</script>
<style lang="scss" scoped>
.buttonMap {
  color: #6b7385;
  border: 1px solid #ced1d9;
}
.collapse-status {
  background: #ffeecf;
  color: #fa962b;
  height: 28px;

  &.blue {
    color: #64befa;
    background: rgba(100, 190, 250, 0.12);
  }
  &.purple {
    color: #bd7ffa;
    background: rgba(189, 127, 250, 0.12);
  }
  &.green {
    color: #3cb540;
    background: rgba(60, 181, 64, 0.12);
  }
  &.orange {
    color: #fc9772;
    background: rgba(252, 151, 114, 0.12);
  }
}
</style>
