<template>
  <div>
    <chart />
    <div :style="{height: $tableHeight}">
      <vxe-table
        ref="baseUserTable"
        class="vone-vxe-table"
        border
        height="auto"
        show-overflow="tooltip"
        :loading="pageLoading"
        :empty-render="{ name: 'empty' }"
        :data="tableData.records"
        :column-config="{ minWidth:'120px' }"
        :checkbox-config="{ reserve: true }"
        row-id="id"
      >

        <vxe-column title="迭代名称" field="name" min-width="150" />
        <vxe-column title="负责人" field="leadingBy" min-width="150">
          <template slot-scope="scope">
            <vone-user-avatar :avatar-path="getUserInfo(scope.row)?getUserInfo(scope.row).avatarPath :''" :name="getUserInfo(scope.row)?getUserInfo(scope.row).name :''" />
          </template>
        </vxe-column>
        <vxe-column title="所属项目" field="project" min-width="150">
          <template #default="{ row }">
            <span v-if="row.echoMap && row.echoMap.projectId">{{ row.echoMap.projectId.name }}</span>
            <span v-else>{{ row.projectId }}</span>
          </template>
        </vxe-column>
        <vxe-column title="迭代目标" field="description" min-width="150" />
        <vxe-column title="状态" field="stateCode" min-width="150">
          <template slot-scope="scope">
            <span v-if="scope.row.stateCode && stateMap[scope.row.stateCode]">
              {{ stateMap[scope.row.stateCode] }}
            </span>
            <span v-else>
              {{ scope.row.stateCode }}
            </span>
          </template>
        </vxe-column>
      </vxe-table>
      <vone-pagination ref="pagination" :total="tableData.total" @update="gettablelist" />
    </div>
  </div>
</template>
<script>
const stateMap = {
  1: '未开始',
  2: '进行中',
  3: '已完成'
}
import Chart from '../../../components/chart.vue'
import { projectPlan } from '@/api/vone/product'

export default {
  components: {
    Chart
  },
  data() {
    return {
      stateMap,
      pageLoading: false,
      tableData: {},
      tableOptions: {
        isOperation: false,
        isSelection: false
      },
      visible: false,
      formData: {
        productVersionId: this.$route.params.versionId
      }
    }
  },
  computed: {
    getUserInfo() {
      return function(row) {
        return row?.leadingBy && row?.echoMap?.leadingBy
      }
    }
  },

  mounted() {
    this.gettablelist()
  },
  methods: {
    async gettablelist() {
      this.pageLoading = true

      const pageObj = this.$refs.pagination?.pageObj || { current: 1, size: 20 }
      const params = {
        ...pageObj,
        extra: {},
        model: { ...this.formData }
      }
      const res = await projectPlan(params)
      this.pageLoading = false
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }

      this.tableData = res.data
    }

  }
}
</script>

<style lang="scss" scoped>
.m-r{
  &-16{
    margin-right: 16px;
  }
  &-26{
    margin-right: 26px;
  }
}
.header {
  display: flex;
  justify-content: flex-end;
  margin: 0 -16px;
  padding: 0 16px 16px;
  border-bottom: 1px solid #ebeef5;
  .header-l {
    flex: 1;
    display: flex;
    align-items: center;
  }
  .header-r {
    flex: 1;
    display: flex;
  }
}
</style>
