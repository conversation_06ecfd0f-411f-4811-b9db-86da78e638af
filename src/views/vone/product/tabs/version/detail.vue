<template>
  <page-wrapper>
    <el-tabs v-model="active" type="card" class="flowTab">
      <el-tab-pane v-for="item in types" :key="item.key" :name="item.name" :label="item.label" />
      <component
        :is="active"
      />
    </el-tabs>

  </page-wrapper>
</template>
<script>
import plan from './components/plan.vue'
import record from './components/record.vue'

export default {
  components: {
    plan,
    record
  },
  data() {
    return {
      active: 'plan',
      types: [
        { name: 'plan', key: 1, label: '迭代' },
        { name: 'record', key: 2, label: '发布记录' }
      ]
    }
  },
  mounted() {
    if (this.$route.query.active) {
      this.active = this.$route.query.active
    }
  },
  methods: {

  }

}
</script>

<style lang="scss" scoped>
::v-deep .el-tabs__content {
  overflow: unset;
}
</style>
