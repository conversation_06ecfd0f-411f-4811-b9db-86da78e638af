
<template>
  <page-wrapper id="graph" ref="graph" class="graph" />
</template>
<script>

import G6 from '@antv/g6'
import customNode from './registerNode'
import customEdge from './registerEdge'
export default {
  name: 'Graph',
  props: {
    graphData: {
      type: Object,
      default: () => ({})
    },
    icon: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      data: {},
      graph: null,
      container: null
    }
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.handleWindowResize)
    this.graph = null
    this.container = null
  },
  mounted() {
    customNode.init()

    customEdge.init()
    this.initGraph(this.graphData)
    window.addEventListener('resize', this.handleWindowResize)
    this.$bus.$on('refreshChart', () => {
      this.graph.refresh()
    })
  },
  methods: {
    initGraph(data) {
      const tooltip = new G6.Tooltip({
        offsetX: 10,
        offsetY: 10,
        // the types of items that allow the tooltip show up
        // 允许出现 tooltip 的 item 类型
        itemTypes: ['node'],
        // custom the tooltip's content
        // 自定义 tooltip 内容
        getContent: (e) => {
          const outDiv = document.createElement('div')
          outDiv.style.maxwidth = 'fit-content'
          // outDiv.style.padding = '0px 0px 20px 0px';
          outDiv.innerHTML = `
      <ul>
        <li> ${e.item.getModel().name}</li>
      </ul>`
          return outDiv
        }

      })

      const container = this.$refs.graph.$el
      const width = container.clientWidth - 100
      const height = container.clientHeight - 100 || 500
      const dark = !!document.querySelector('.custom-theme-dark')
      const graph = new G6.Graph({
        container: container,
        width,
        height,
        modes: {
          default: ['drag-canvas', 'zoom-canvas', 'drag-node'] // 允许拖拽画布、放缩画布、拖拽节点、设置高亮
        },
        center: [10, 50],
        renderer: 'svg',
        fitCenter: true,
        plugins: [tooltip], // 配置 Tooltip 插件
        layout: {
          type: 'dagre',
          rankdir: 'LR',
          nodesep: 10,
          ranksep: 10
        },
        animate: false,
        defaultNode: {
          icon: {
            show: true,
            img: this.icon, // 可更换为其他图片地址
            width: 28,
            height: 28,
            y: -30,
            x: -12
          },
          labelCfg: {
            style: {
              fill: dark ? '#fff' : '#202124'
            }
          },
          style: {
            fill: dark ? '#252933' : '#fff',
            stroke: '#3E7BFA',
            lineWidth: 2
          }
        },
        defaultEdge: {
          type: 'cubic-horizontal',
          style: {
            stroke: '#3E7BFA',
            lineWidth: 2
          }
        }
      })
      this.container = container
      this.graph = graph
      const params = data
      graph.data(params)
      graph.render()
      graph.fitView()
      // graph.on('node:mouseenter', (e) => {
      //   graph.setItemState(e.item, 'active', true)
      // })
      // graph.on('node:mouseleave', (e) => {
      //   graph.setItemState(e.item, 'active', false)
      // })
    },

    handleWindowResize() {
      if (typeof window !== 'undefined') {
        window.onresize = () => {
          if (!this.graph || this.graph.get('destroyed')) return
          if (!this.container || !this.container.scrollWidth || !this.container.scrollHeight) return
          this.graph.changeSize(this.container.scrollWidth, this.container.scrollHeight)
        }
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.graph{
 overflow: hidden;
}

  .g6-component-tooltip {
    background-color: rgba(255, 255, 255, 0.8);
    padding: 0px 10px 24px 10px;
    box-shadow: rgb(174, 174, 174) 0px 0px 10px;
  }

</style>
