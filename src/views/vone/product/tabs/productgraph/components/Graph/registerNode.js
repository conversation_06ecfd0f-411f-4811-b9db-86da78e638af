
import G6 from '@antv/g6'
// const colors = {
//   B: '#5B8FF9',
//   R: '#F46649',
//   Y: '#EEBC20',
//   G: '#5BD8A6',
//   DI: '#A7A7A7'
// }
import img from '@/assets/iconsteps/icon-product.png'
const customNode = {
  init() {
    G6.registerNode('dom-node', {
      draw: (cfg, group) => {
        const dark = !!document.querySelector('.custom-theme-dark')
        if (cfg.iscore) {
          const center = group.addShape('circle', {
            attrs: {
              x: 0,
              y: 0,
              r: 64,
              fill: dark ? '#252933' : '#fff',
              stroke: '#3E7BFA',
              lineWidth: 2
            },
            name: 'circle-shape'
          })
          const { width, height, x, y } = cfg.icon
          group.addShape('image', {
            attrs: {
              width: width,
              height: height,
              x: x - 2,
              y: y,
              textAlign: 'center',
              img: cfg.icon.img
            },
            name: 'image-shape'
          })
          group.addShape('text', {
            attrs: {
              fontSize: 14,
              fontWeight: 500,
              textAlign: 'center',
              y: x + 28,
              text: cfg.name.length > 8 ? cfg.name.slice(0, 8) + '...' : cfg.name,
              fill: dark ? '#fff' : '#202124'
            },
            name: 'text-shape'
          })
          return center
        }
        const shape = group.addShape('dom', {
          attrs: {
            width: 15 * (cfg.name.length + 1) + 40,
            height: 32,
            html: `
            <div
} class='dom-node' style="width:100%;height:100%;background-color: var(--main-bg-color,#fff); border: 2px solid var(--main-theme-color,#3e7bfa); border-radius: 2px; display: flex;font-size:14px;padding:0 12px">
              <div style="height: 100%; background-color: var(--main-bg-color,#fff)">
                <img  style="line-height: 100%; margin:4px 4px 0;" src="${cfg.img ? cfg.img : img}" width="20" height="20" />
              </div>
              <span style="margin:auto 0;color:${dark ? '#fff' : '#202124'}" >${cfg.label}</span>
            </div>
          `
          }

        })

        // title text

        // The content list

        return shape
      },
      getAnchorPoints: function getAnchorPoints() {
        return [
          [0, 0.5],
          [1, 0.5]
        ]
      }
    })
  }

}

export default customNode
