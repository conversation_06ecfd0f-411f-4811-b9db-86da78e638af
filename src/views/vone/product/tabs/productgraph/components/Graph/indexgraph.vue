
<template>
  <div id="graph" ref="graph" class="graph" />
</template>
<script>

import G6 from '@antv/g6'
const fittingString = (str, maxWidth, fontSize) => {
  const ellipsis = '...'
  const ellipsisLength = G6.Util.getTextSize(ellipsis, fontSize)[0]
  let currentWidth = 0
  let res = str
  const pattern = new RegExp('[\u4E00-\u9FA5]+') // distinguish the Chinese charactors and letters
  str.split('').forEach((letter, i) => {
    if (currentWidth > maxWidth - ellipsisLength) return
    if (pattern.test(letter)) {
      currentWidth += fontSize
    } else {
      currentWidth += G6.Util.getLetterWidth(letter, fontSize)
    }
    if (currentWidth > maxWidth - ellipsisLength) {
      res = `${str.substr(0, i)}${ellipsis}`
    }
  })
  return res
}
export default {
  name: 'Graph',
  props: {
    graphData: {
      type: Object,
      default: () => ({})
    },
    icon: {
      type: String,
      default: ''
    },
    layoutType: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      data: {},
      graph: null,
      container: null,
      timer: null
    }
  },
  watch: {
    'layoutType.type': {
      handler: function(val) {
        this.graph.updateLayout(val)
        this.graph.layout()
      },
      deep: true
    },
    'layoutType.nodeType': {
      handler: function(val) {
        this.graph.render()
      },
      deep: true
    }
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.handleWindowResize)
    this.graph = null
    this.container = null
  },
  mounted() {
    this.initGraph(this.graphData)
    window.addEventListener('resize', this.handleWindowResize)
  },
  methods: {
    initGraph(data) {
      const _this = this
      const container = document.getElementById('graph')
      const width = container.scrollWidth
      const height = container.scrollHeight || 500
      const tooltip = new G6.Tooltip({
        offsetX: 10,
        offsetY: 20,
        getContent(e) {
          const outDiv = document.createElement('div')
          outDiv.style.minWidth = '100px'
          outDiv.innerHTML = e.item.getModel().name
          return outDiv
        },
        itemTypes: ['node']
      })
      _this.graph = new G6.Graph({
        container: 'graph',
        width,
        height,
        modes: {
          default: ['drag-canvas', 'zoom-canvas', 'drag-node']
        },
        fitView: true,
        animate: true,
        plugins: [tooltip],
        layout: {
          type: _this.layoutType.type,
          nodeSize: 50,
          nodeSpacing: 20,
          preventOverlap: true
        },
        defaultNode: {
          type: 'tracking',
          style: {
            fill: '#FFFFFF',
            stroke: '#3E7BFA',
            lineWidth: 1
          },
          labelCfg: {
            position: 'center',
            style: {
              fill: '#202124',
              fontSize: 10
            }
          }
        }
      })
      _this.registerFn()
      _this.graph.data(data)
      _this.graph.render()
      if (_this.layoutType.type == 'force') {
        _this.graph.on('node:dragstart', function(e) {
          _this.graph.layout()
          _this.refreshDragedNodePosition(e)
        })
        _this.graph.on('node:drag', function(e) {
          const forceLayout = _this.graph.get('layoutController').layoutMethods[0]
          forceLayout.execute()
          _this.refreshDragedNodePosition(e)
        })
        // _this.graph.on('node:dragend', function(e) {
        //   e.item.get('model').fx = null
        //   e.item.get('model').fy = null
        // })
      }
    },
    registerFn() {
      const _this = this
      G6.registerNode(
        'tracking',
        {
          shapeType: 'tracking',
          draw(cfg, group) {
            if (_this.layoutType.nodeType == 'rect') {
              const rect = group.addShape('rect', {
                attrs: {
                  x: 0,
                  y: 0,
                  stroke: '#3E7BFA',
                  lineWidth: 1,
                  width: 150,
                  height: 30,
                  fill: '#FFFFFF',
                  radius: 2
                },
                name: 'box'
              })
              // 规定文字位置，对齐方式
              const textConfig = {
                textAlign: 'left',
                textBaseline: 'top'
              }
              // 名称
              const name = group.addShape('text', {
                attrs: {
                  ...textConfig,
                  x: 12,
                  y: 8,
                  width: 100,
                  height: 14,
                  fontSize: 12,
                  fill: '#202124',
                  text: fittingString(cfg.name, 150, 14)
                },
                draggable: true,
                name: 'title'
              })
              var nameBBox = {
                width: 0
              }
              nameBBox = name.getBBox()
              rect.attr({
                width: nameBBox.width + 24
              })
              return rect
            } else if (_this.layoutType.nodeType == 'circle') {
              const circle = group.addShape('circle', {
                attrs: {
                  x: 0,
                  y: 7,
                  r: cfg.iscore ? 30 : 10,
                  stroke: '#3E7BFA',
                  lineWidth: 1,
                  fill: '#FFFFFF',
                  radius: 2
                },
                name: 'box'
              })
              // 名称
              const name = group.addShape('text', {
                attrs: {
                  textAlign: 'left',
                  textBaseline: 'top',
                  x: 0,
                  y: 0,
                  width: 100,
                  height: 14,
                  fontSize: 12,
                  fill: '#202124',
                  text: fittingString(cfg.name, 150, 14)
                },
                draggable: true,
                name: 'title'
              })
              var nameBBoxs = {
                // width: 0
              }
              nameBBoxs = name.getBBox()
              name.attr({
                x: -nameBBoxs.width / 2
              })
              if (cfg.iscore) {
                circle.attr({
                  r: nameBBoxs.width / 2 + 4
                })
              }
              return circle
            }
          }
        }
      )
    },
    refreshDragedNodePosition(e) {
      const model = e.item.get('model')
      model.fx = e.x
      model.fy = e.y
    },
    handleWindowResize() {
      if (!this.graph || this.graph.get('destroyed')) return
      if (this.timer) clearTimeout(this.timer)
      this.timer = setTimeout(() => {
        this.graph.changeSize(this.$refs.graph.clientWidth, this.$refs.graph.clientHeight)
        this.graph.fitView()
      }, 200)
    }
  }
}
</script>
<style lang="scss" scoped>
  .graph {
    min-height: calc(100vh - 86px - 32px - 50px);
    width: 100%;
  }
  .g6-component-tooltip {
    background-color: rgba(255, 255, 255, 0.8);
    padding: 0px 10px 24px 10px;
    box-shadow: rgb(174, 174, 174) 0px 0px 10px;
  }
</style>
