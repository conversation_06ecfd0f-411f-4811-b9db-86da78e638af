
import G6 from '@antv/g6'

const customEdge = {
  init() {
    G6.registerEdge('fund-polyline', {
      itemType: 'edge',
      draw: function draw(cfg, group) {
        const startPoint = cfg.startPoint
        const endPoint = cfg.endPoint

        const Ydiff = endPoint.y - startPoint.y

        const slope = Ydiff !== 0 ? Math.min(500 / Math.abs(Ydiff), 20) : 0

        const cpOffset = slope > 15 ? 0 : 16
        const offset = Ydiff < 0 ? cpOffset : -cpOffset

        const line1EndPoint = {
          x: startPoint.x + slope,
          y: endPoint.y + offset
        }
        const line2StartPoint = {
          x: line1EndPoint.x + cpOffset,
          y: endPoint.y
        }

        // 控制点坐标
        const controlPoint = {
          x:
            ((line1EndPoint.x - startPoint.x) * (endPoint.y - startPoint.y)) /
              (line1EndPoint.y - startPoint.y) +
            startPoint.x,
          y: endPoint.y
        }

        const path = [
          ['M', startPoint.x, startPoint.y],
          ['L', line1EndPoint.x, line1EndPoint.y],
          ['Q', controlPoint.x, controlPoint.y, line2StartPoint.x, line2StartPoint.y],
          ['L', endPoint.x, endPoint.y]
        ]

        const endArrow = cfg?.style && cfg.style.endArrow ? cfg.style.endArrow : false

        const line = group.addShape('path', {
          attrs: {
            path,
            stroke: '#3E7BFA',
            lineWidth: 2,
            endArrow
          },
          name: 'path-shape'
        })
        return line
      }
    })
  }

}

export default customEdge
