<template>
  <section class="wrapper">
    <header>
      <span class="title">产品相关工作项</span>
      <div
        v-for="item in types"
        :key="item.key"
        class="tab-content"
        @click.stop="sendId(item)"
      >
        <span
          class="tab"
          :class="{
            selected: currentType === item.key,
          }"
        >
          {{ item.tab }}
        </span>
      </div>
    </header>

    <section class="content-section">
      <TtemTale :current-type="currentType" />

    </section>
  </section>
</template>
<script>
import TtemTale from './table.vue'

export default {
  components: {
    TtemTale
  },
  data() {
    return {

      currentType: 'issue',
      types: [
        {
          key: 'idea',
          tab: '用户需求'
        },
        {
          key: 'issue',
          tab: '需求'
        },
        {
          key: 'bug',
          tab: '缺陷'
        }
      ]
    }
  },

  mounted() {

  },
  methods: {
    sendId(val) {
      this.currentType = val.key
    }

  }
}
</script>

<style lang="scss" scoped>
.wrapper {
  height: calc(100vh - 86px);
  background-color: var(--main-bg-color);
  border-radius: 4px;
  header {
    display: flex;
    line-height: 56px;
    height: 56px;
    padding: 0 20px;
    border-bottom: 1px solid #ebeef5;
    align-items: center;
    .title{
      font-weight: 600;
      margin-right: 30px;
    }
    .tab-content{
      height: 40px;
      line-height: 40px;
      background-color: var(--tab-bg-color);
      padding:0 10px;
      border-radius: 2px;
      color: var(--auxiliary-font-color);
    }
    .tab{
      cursor: pointer;
    }
    & .selected{
      background-color: var(--main-bg-color);
      color: var(--main-theme-color);
      padding: 5px 10px;
      border-radius: 2px;
    }

  }
  .content-section{
    padding: 0 20px;
  }
}

</style>
