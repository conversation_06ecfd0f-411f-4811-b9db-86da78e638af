import request from '@/utils/axios-api'

// 查询项目模板列表
export function getProjectTemplate(data) {
  return request({
    url: `/api/alm/alm/projectType/page`,
    method: 'post',
    data
  })
}
// 删除模版
export function deleteProjectTemplate(data) {
  return request({
    url: `/api/alm/alm/projectType`,
    method: 'DELETE',
    data
  })
}
// 修改模版
export function updateProjectTemplate(data) {
  return request({
    url: `/api/alm/alm/projectType`,
    method: 'put',
    data
  })
}
// 复制模版
export function copyProjectTemplate(data) {
  return request({
    url: `/api/alm/alm/projectType/copyProjectType`,
    method: 'put',
    data
  })
}
// 获取可绑定的一级菜单
export function getOneMenu(data) {
  return request({
    url: `/api/alm/alm/projectMenuDic/queryOneMenu`,
    method: 'post',
    data
  })
}
// 新增（所有字段）
export function addAllProjectType(data) {
  return request({
    url: `/api/alm/alm/projectType/addAllProjectType`,
    method: 'post',
    data
  })
}
// 查询配置面板
export function getProjectMenus(id) {
  return request({
    url: `/api/alm/alm/projectTypeMenuConf/getProjectMenus/${id}`,
    method: 'get'
  })
}
// 新增/修改配置面板
export function addOrUpdateProjectMenus(id, data) {
  return request({
    url: `/api/alm/alm/projectTypeMenuConf/addOrUpdateProjectMenus/${id}`,
    method: 'put',
    data
  })
}
// 查询角色
export function getProjectRole(data) {
  return request({
    url: `/api/alm/alm/projectRole/query`,
    method: 'post',
    data
  })
}
// 模板处修改角色
export function updateProjectRoles(data) {
  return request({
    url: `/api/alm/alm/projectRole/updateProjectRoles`,
    method: 'put',
    data
  })
}
// 查询已关联的权限
export function getMenuRole(id) {
  return request({
    url: `/api/alm/alm/menu/role/${id}`,
    method: 'get'
  })
}
// 查询已关联的权限
export function queryMenuByMenuIds(data) {
  return request({
    url: `/api/alm/alm/projectMenuDic/queryMenuByMenuIds`,
    method: 'post',
    data
  })
}
