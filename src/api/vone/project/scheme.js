import request from '@/utils/axios-api'

// 获取方案列表
export function getTestSchemeList(data) {
  return request({
    url: '/api/testm/testScheme/newPage',
    method: 'post',
    data
  })
}
// 删除
export function delTestScheme(data) {
  return request({
    url: '/api/testm/testScheme',
    method: 'delete',
    data
  })
}
// 新增
export function addTestScheme(data) {
  return request({
    url: '/api/testm/testScheme',
    method: 'post',
    data
  })
}
// 查询
export function getTestSchemeDetail(id) {
  return request({
    url: `/api/testm/testScheme/${id}`,
    method: 'get'
  })
}
// 编辑
export function editTestScheme(data) {
  return request({
    url: '/api/testm/testScheme',
    method: 'put',
    data
  })
}
// 获取spaceId
export function getSpaceId(projectId) {
  return request({
    url: `/api/wiki/wiki/space/getTestSchemeSpace/${projectId}`,
    method: 'get'
  })
}
// 获取方案可流转状态
export function getFlowStatus(schemeId) {
  return request({
    url: `/api/testm/testScheme/findNextNode/${schemeId}`,
    method: 'get'
  })
}
// 流转方案状态
export function flowSchemeStatus(schemeId, sourceStateCode, targetStateCode) {
  return request({
    url: `/api/testm/testScheme/transitionState/${schemeId}/${sourceStateCode}/${targetStateCode}`,
    method: 'put'
  })
}
// 测试需求列表全量
export function getTestReq(data) {
  return request({
    url: '/api/alm/alm/testreq/queryList',
    method: 'post',
    data
  })
}
// 查询系统交易树
export function getTradeTree(data) {
  return request({
    url: '/api/product/product/productModuleFunction/wiki/tree',
    method: 'post',
    data
  })
}
// 根据模块查询交易
export function getTradeForModule(data) {
  return request({
    url: '/api/product/product/productModuleFunction/query',
    method: 'post',
    data
  })
}
