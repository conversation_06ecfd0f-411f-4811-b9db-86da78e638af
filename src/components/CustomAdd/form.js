import { apiAlmAdd } from '@/api/vone/project/issue'
import { planListByCondition } from '@/api/vone/project/iteration'

import { apiAlmSourceNoPage } from '@/api/vone/project/issue'
import {
  apiAlmPriorityNoPage,
  apiAlmGetTypeNoPage,
  apiAlmTypeNoPage,
  getAllProductInfoList
} from '@/api/vone/alm/index'

import { productListByCondition, queryListByCondition } from '@/api/vone/project/index'

import { requirementListByCondition, apiAlmGetInfo } from '@/api/vone/project/issue'
import { bugListByCondition } from '@/api/vone/project/defect'
import { getProjectPlans } from '@/api/vone/testmanage/case'

import {
  apiVaBaseCustomFormField,
  apiVaBaseCustomFormFieldProgram,
  apiVaBaseCustomFormFieldProject
} from '@/api/vone/base/customForm'

import { getProductVersionList, getModule } from '@/api/vone/product/index'
import { getModule as getModuleTree } from '@/api/vone/product'
import { apiProductData } from '@/api/vone/product'

import tagSelect from '@/components/CustomEdit/components/tag-select'
import dataSelect from '@/components/CustomEdit/components/data-select'

import projectRemoteUser from '@/components/CustomEdit/components/project-user-remote.vue'
import storage from 'store'

import { gainTreeList } from '@/utils'
import { orgList } from '@/api/vone/base/org'
import { getSourceById } from '@/api/vone/base/source'
import { apiBaseDictNoPage } from '@/api/vone/base/dict'

import _ from 'lodash'
import dayjs from 'dayjs'

const fileMap = {
  ISSUE: 'ISSUE_FILE_UPLOAD',
  BUG: 'BUG_FILE_UPLOAD',
  TASK: 'TASK_FILE_UPLOAD',
  RISK: 'RISK_FILE_UPLOAD',
  IDEA: 'IDEA_FILE_UPLOAD',
  TESTREQ: 'TESTREQ_FILE_UPLOAD'
}
export default {
  name: 'VoneCustomAdd',
  components: {
    tagSelect,
    dataSelect,
    projectRemoteUser
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: undefined
    },
    id: {
      type: String,
      default: undefined
    },
    sprintId: {
      type: String,
      default: undefined
    },
    typeCode: {
      type: String,
      default: undefined
    },
    formId: {
      type: String,
      default: undefined
    },
    spaceInfo: {
      // 文档库，如果文档库的type==3，标识是根据产品自动创建的，新增时默认回显所属产品
      type: Object,
      default: () => {}
    },
    rowTypeCode: {
      type: String,
      default: undefined
    },
    // 补充字段
    formData: {
      type: Object,
      default: () => {}
    },
    // 表单校验必填项提示信息
    isTooltip: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      fileMap,
      fixdForm: {
        // files: []
      }, // 基本属性
      fixdFormRules: {}, // 固定属性校
      formRules: {},
      form: {
        estimateHour: 1
      },
      fixedProperty: [], // 固定属性
      customList: [], // 自定义属性
      fileList: [],
      saveLoading: false,
      pageLoading: false,
      typeCodeList: [], // 分类
      planIdList: [], // 迭代计划
      selectOptionWidth: '',
      orgData: [], // 组织机构
      isChange: false,
      firstCode: null,
      custom: {},
      typeMap: {},
      userMap: {},
      systemList: [],
      productModuleFunctionList: [],
      functionList: [],
      tradeProjectId: false, // 判断是否有交易字段
      projectId: this.$route.params.id
    }
  },
  watch: {
    'form.projectId': {
      handler(value) {
        if (!value) {
          this.$set(this.form, 'planId', '')
          this.setData(this.customList, 'planId', [])
          return
        }
        this.getPlanList(value)
      },
      immediate: true
    },
    'form.productId': {
      handler(value) {
        if (!value) {
          this.$set(this.form, 'productVersionId', '')
          this.$set(this.form, 'productRepairVersionId', '')
          this.$set(this.form, 'productModuleFunctionId', null)
          this.setData(this.customList, 'productVersionId', [])
          this.setData(this.customList, 'productRepairVersionId', [])
          this.setData(this.customList, 'productModuleFunctionId', [])
          return
        }
        this.getVersion(value)
        this.getModel(value)
      },
      immediate: true
    },
    'form.productModuleFunctionId': {
      handler(value) {
        if (value) {
          this.functionIdList(value)
        } else {
          this.setData(this.customList, 'functionId', [])
        }
      },
      immediate: true
    }
  },
  mounted() {
    // 接口请求顺序为:1.获取类型,拿到第一个类型作为参数 2.根据第一个类型查表单 3.查数据源
    this.pageLoading = true
    this.getFirstTypecode()
  },
  methods: {
    pickerOptions(e) {
      var _this = this
      if (e == 'planEtime') {
        return {
          disabledDate(time) {
            if (_this.form['planStime']) {
              return time.getTime() < new Date(_this.form['planStime']).getTime()
            }
          }
        }
      } else if (e == 'planStime') {
        return {
          disabledDate(time) {
            if (_this.fixdForm['planEtime']) {
              return time.getTime() > new Date(_this.fixdForm['planEtime']).getTime()
            }
          }
        }
      }
    },
    remoteMethod: _.debounce(function(e, t) {
      if (t == 'requirementId') {
        this.getRequirementList(e)
      } else if (t == 'planId') {
        this.getPlanList(this.form.projectId, e)
      } else if (t == 'projectId') {
        this.getProjectList(e)
      } else if (t == 'bugId') {
        this.getBugList(e)
      }
    }, 1000),
    async getInfo(val) {
      this.drawerLoading = true
      this.infoLoading = true

      const urlMap = {
        ISSUE: `/api/alm/alm/requirement/${val || this.id}`,
        BUG: `/api/alm/alm/bug/${val || this.id}`,
        TASK: `/api/alm/alm/task/${val || this.id}`,
        RISK: `/api/alm/alm/risk/${val || this.id}`,
        IDEA: `/api/alm/alm/idea/${val || this.id}`,
        TESTREQ: `/api/alm/alm/testreq/${val || this.id}`
      }

      const res = await apiAlmGetInfo(urlMap[this.typeCode])

      if (!res.isSuccess) {
        return
      }

      if (!res.data) {
        return
      }
      // 判断key里面有没有C1到C30的，有的话，如果是多选,就是分隔成数组
      var arr = Array.from(Array(30), (v, k) => `c${k + 1}`)
      const multipleList = this.customList.filter(r => r.multiple).map(r => r.key)
      for (var item in res.data) {
        if (arr.includes(item)) {
          if (multipleList.includes(item)) {
            res.data[item] = res.data[item] ? res.data[item].split(',') : []
          }
        }
      }
      if (res.data.tagId && res.data.tagId.length && res.data.echoMap && res.data.echoMap.tagId) {
        this.$set(res.data, 'tagId', res.data.echoMap.tagId.map(r => r.name) || [])
      }
      if (this.form && this.form.productId) {
        this.isChange = false

        this.getVersion(this.form.productId)
        this.getModel(this.form.productId)
      }
      this.form = res.data
      this.fixdForm = res.data

      this.infoLoading = false
      this.drawerLoading = false
      this.customList.forEach(e => {
        if (e.key == 'requirementId' || e.key == 'planId' || e.key == 'projectId' || e.key == 'bugId') {
          this.$set(e, 'options', res.data.echoMap[e.key] ? [res.data.echoMap[e.key]] : [])
        }
      })
    },
    async getQuoteType() {
      this.customList.forEach((e, index) => {
        if (e.type == 'QUOTE') {
          const config = JSON.parse(e.config)
          this.getTableConfig(config.relationShipsheet, e, item => {
            e.quoteType = item.type
            this.$set(this.customList, index, e)
          })
        }
      })
    },
    async getTableConfig(id, e, callback) {
      const config = JSON.parse(e.config)
      const res = await getSourceById(id)
      if (res.isSuccess) {
        const list = res.data.fields.map(v => {
          v.prop = v.id
          const obj = JSON.parse(v.config)
          return {
            ...v,
            ...obj
          }
        })
        const objs = list.find(e => e.id == config.relationField && !e.primary)
        callback(objs)
      }
    },
    dataSelectChange({ item, list, user }, key) {
      this.customList.forEach(e => {
        if (e.type == 'QUOTE') {
          const config = JSON.parse(e.config)
          if (key == config.queryCriteriaC) {
            this.form[e.key] = item[config.relationField]
            const obj = list.find(e => e.id == config.relationField && !e.primary)
            e.quoteType = obj.type
            // if (e.quoteType == 'user') {
            //   e.user = user[item[config.relationField]]
            // }
          }
        }
      })
    },
    // 数据源接口返回数据以后,把值塞到表单模板里
    setData(list, key, data) {
      list.forEach((e, index) => {
        if (e.key == key) {
          this.$set(e, 'options', data)
        }
        if (key == 'planId') {
          e.disabled = false
        }
      })
    },
    getFirstTypecode() {
      // 项目下查询指定接口，查询开启状态的类型
      if (this.$route.params.projectKey) {
        this.getAllTypeCode()
      } else {
        this.getIssueType()
      }
    },
    getOption(val) {
      this.getOrgList()
      this.getSourceList()
      this.getPrioritList()
      this.getProjectList()
      this.setTypeCode(val)
      this.systemIdList()
      const keys = this.customList.map(r => r.key)
      const fixedKeys = this.fixedProperty.map(r => r.key)
      console.log(keys)
      console.log(fixedKeys)

      if (keys.includes('envCode')) {
        this.getEnvList()
      }
      if (keys.includes('tradeProjectId')) {
        this.tradeProjectId = true
      }
      // 判断固定属性是否有系统
      if (fixedKeys.includes('systemId')) {
        this.getSystemIdList(this.fixedProperty, 'systemId')
      }
      if (fixedKeys.includes('bugClass')) {
        this.getBugClassList()
      }
      if (keys.includes('severityCode')) {
        this.getSeverityCodeList()
      }
      // 缺陷分类
      if (fixedKeys.includes('bugToSystem')) {
        this.getSystemIdList(this.fixedProperty, 'bugToSystem')
      }
      // 主板系统
      if (keys.includes('productId')) {
        this.getSystemIdList(this.customList, 'productId')
      }
      // 辅办系统
      if (keys.includes('assistProductIds')) {
        this.getSystemIdList(this.customList, 'assistProductIds')
      }
      if (keys.includes('reportType')) {
        this.getReportTypeList()
      }
      if (keys.includes('reportStatus')) {
        this.getReportStatusList()
      }

      // 项目下查询指定接口，查询开启状态的类型
      if (this.$route.params.projectKey) {
        // this.getAllTypeCode(val)
        this.getRequirementList()
        this.getBugList()
        this.getPlanList(this.$route.params.id)
        this.getProjectProductList()
      } else {
        this.productList()
      }

      this.typeCode == 'BUG' ? this.getProjectTestPlan() : null

      // 测试计划
      if (keys.includes('testPlanId')) {
        this.getTestPlanList(this.$route.params.id)
      }
      // 归属计划
      if (keys.includes('milestionePlan')) {
        this.getmilestionePlanList(this.$route.params.id)
      }
    },
    // 下拉框弹出时，设置弹框的宽度
    setOptionWidth(event) {
      this.$nextTick(() => {
        this.selectOptionWidth = event.target.parentNode.parentNode.clientWidth + 'px'
      })
    },
    async changeProject(key) {
      if (key == 'projectId') {
        this.$set(this.form, 'requirementId', '')
        const res = await requirementListByCondition({
          projectId: this.form[key]
        })
        if (!res.isSuccess) {
          return
        }
        this.setData(this.customList, 'requirementId', res.data)
      } else if (key == 'systemId') {
        this.productModuleFunctionIdList()
      }
    },
    changeSelect(key, value) {
      this.isChange = true
      if (key == 'typeCode') {
        this.getFormTemplete(value)
      }
    },
    resetFixedForm() {
      this.$set(this.fixdForm, 'planEtime', null)
      this.$set(this.fixdForm, 'expectedTime', null)
      this.$set(this.fixdForm, 'tagId', [])
      this.$set(this.fixdForm, 'description', '')
    },
    // 工作项显示
    async getFormTemplete(val) {
      this.resetFixedForm()
      this.form = {}
      // this.customList = []
      // 需求中心，项目，项目集，查询表单项时调不同的接口url，参数格式也不同
      this.pageLoading = true
      const activeApp = this.$route.meta.activeApp
      const params = {
        typeCode: this.id ? this.rowTypeCode : val || this.firstCode,
        stateCode: this.id ? this.stateCode : '',
        projectId: activeApp == 'project' ? this.$route.params.id : null
      }
      const res =
        activeApp == 'projectm'
          ? await apiVaBaseCustomFormFieldProgram(this.$route.params.id, this.typeCode, params)
          : activeApp == 'project'
            ? await apiVaBaseCustomFormFieldProject(this.$route.params.id, this.typeCode, params)
            : await apiVaBaseCustomFormField(this.typeCode, params)

      this.pageLoading = false
      if (!res.isSuccess) {
        return
      }

      res.data.forEach(element => {
        element.placeholder = JSON.parse(element.config)?.placeholder
        element.multiple = JSON.parse(element.config)?.multiple
        element.message = JSON.parse(element.config)?.message
        element.validator = JSON.parse(element.config)?.validator
        element.defaultTime = JSON.parse(element.config)?.defaultTime
        element.options = !element.isBuilt ? JSON.parse(element.config)?.options : []
        element.disabled = !!(element.key == 'planId' && this.sprintId && this.sprintId != -1)
        element.type = element.type.code
        element.precision = JSON.parse(element.config)?.precision
        element.defaultValue = JSON.parse(element.config)?.defaultValue
        element.relationShipsheet = JSON.parse(element.config)?.relationShipsheet

        if (element.type == 'SELECT' || element.type == 'LINKED' || element.type == 'INT') {
          this.$set(this.form, element.key, JSON.parse(element.config)?.defaultValue)
        } else if (element.type == 'DATE') {
          this.$set(
            this.form,
            element.key,
            JSON.parse(element.config)?.defaultValue == 'now' ? dayjs().format('YYYY-MM-DD HH:mm:ss') : ''
          )
        } else if (element.key == 'tagId' || element.key == 'assistProductIds') {
          element.multiple = true
        }
        if (this.id && element.key == 'typeCode') {
          element.disabled = true
        }
      })
      // 所有基本属性
      const basicAll =
        res.data && res.data.length
          ? res.data.filter(r => r.isBasic && r.isShow && r.state && r.key != 'stateCode')
          : []

      // 固定基本属性
      this.fixedProperty = basicAll.sort(function(a, b) {
        return a.sort - b.sort
      })
      this.fixedProperty = this.fixedProperty.filter(r => r.key != 'stateCode')

      // 排序
      // 自定义属性
      const custom = res.data && res.data.length ? res.data.filter(r => !r.isBasic && r.isShow && r.state) : []
      this.customList = custom.sort(function(a, b) {
        return a.sort - b.sort
      })
      this.setData(this.customList, 'failAddBug', [{ id: '1', name: '是', value: '1' }, { id: '0', name: '否', value: '0' }])
      // 固定属性的校验规则
      const fixedRoule = this.fixedProperty.map(r => ({
        required: r.isRequired,
        message: r.message,
        max: r.max,
        pattern: r.validator,
        key: r.key,
        type: r.multiple || r.type == 'FILE' || r.key == 'tagId' ? 'array' : 'string'
      }))

      fixedRoule.forEach(item => {
        if (!this.fixdFormRules[item.key]) {
          this.fixdFormRules[item.key] = [item]
        } else {
          this.fixdFormRules[item.key].push(item)
        }
      })
      // 自定义属性的校验规则
      this.customList.forEach(r => {
        const rulesObj = {
          required: r.isRequired,
          message: r.message ? r.message : '该字段为必填项',
          max: r.max,
          pattern: r.validator,
          key: r.key,
          type: r.multiple ? 'array' : r.type == 'INT' ? 'number' : 'string'
        }

        if (r.type == 'LINKED') {
          this.formRules[r.key] = [{ required: r.isRequired, message: r.message }]
        } else {
          this.formRules[r.key] = [rulesObj]
        }
      })
      // 新增时默认赋值当前登录人
      const userInfo = storage.get('user')
      this.$set(this.form, 'putBy', userInfo.id)
      this.$set(this.form, 'leadingBy', userInfo.id)
      this.$set(this.fixdForm, 'handleBy', userInfo.id)
      // 查询数据源
      this.getOption(val)
      if (this.id) {
        this.getQuoteType()
        this.getInfo(val)
      }
    },
    // bug分类
    async getBugClassList() {
      const res = await apiBaseDictNoPage({
        type: 'BUG_CLASS'
      })
      if (!res.isSuccess) {
        return
      }
      this.setData(this.fixedProperty, 'bugClass', res.data)
    },
    // 严重程度Code
    async getSeverityCodeList() {
      const res = await apiBaseDictNoPage({
        type: 'SEVERITY_CODE'
      })
      if (!res.isSuccess) {
        return
      }
      this.setData(this.customList, 'severityCode', res.data)
    },
    // 上报类型

    async getReportTypeList() {
      const res = await apiBaseDictNoPage({
        type: 'REPORT_TYPE'
      })
      if (!res.isSuccess) {
        return
      }
      this.setData(this.customList, 'reportType', res.data)
    },
    async getReportStatusList() {
      const res = await apiBaseDictNoPage({
        type: 'REPORT_STATUS'
      })
      if (!res.isSuccess) {
        return
      }
      this.setData(this.customList, 'reportStatus', res.data)
    },
    async getEnvList() {
      const res = await apiBaseDictNoPage({
        type: 'ENVIRONMENT'
      })
      if (!res.isSuccess) {
        return
      }
      this.setData(this.customList, 'envCode', res.data)
    },
    // 系统下拉列表
    async getSystemIdList(form, val) {
      const params = { current: 1,
        extra: { tableId: '1', tableSave: true, boardField: [], fixedView: [] },
        boardField: [],
        fixedView: [],
        tableId: '1',
        tableSave: true,
        model: {},
        order: 'descending',
        size: 12,
        sort: 'createTime' }
      apiProductData(params).then((res) => {
        this.setData(form, val, res.data.records)
      })
    },
    // 查询所有机构
    async getOrgList() {
      const res = await orgList()
      if (!res.isSuccess) {
        return
      }
      const orgTree = gainTreeList(res.data)
      this.orgData = orgTree
    },
    // 查询项目下开启了的类型
    async getAllTypeCode() {
      const res = await apiAlmGetTypeNoPage(this.$route.params.id, this.typeCode)
      if (!res.isSuccess) {
        return
      }
      this.firstCode = res.data && res.data.length ? res.data[0].code : null
      this.typeCodeList = res.data

      this.getFormTemplete() // 查询表单字段
    },
    // 查询所有类型
    async getIssueType() {
      const res = await apiAlmTypeNoPage({
        classify: this.typeCode
      })
      if (!res.isSuccess) {
        return
      }
      this.firstCode = res.data && res.data.length ? res.data[0].code : null
      this.typeCodeList = res.data

      this.getFormTemplete() // 查询表单字段
    },
    setTypeCode(val) {
      this.setData(this.fixedProperty, 'typeCode', this.typeCodeList)
      // 默认值
      if (this.typeCodeList.length) {
        this.$set(this.fixdForm, 'typeCode', this.isChange ? val : this.typeCodeList[0].code)
      }
    },
    // 查项目下需求
    async getRequirementList(e) {
      const res = await requirementListByCondition({
        projectId: this.$route.params.id || this.form.projectId,
        name: e
      })
      if (!res.isSuccess) {
        return
      }
      this.setData(this.customList, 'requirementId', res.data)
    },
    // 查项目下缺陷
    async getBugList(e) {
      const res = await bugListByCondition({
        projectId: this.$route.params.id,
        name: e
      })
      if (!res.isSuccess) {
        return
      }

      this.setData(this.customList, 'bugId', res.data)
    },

    // 查询来源
    async getSourceList() {
      const res = await apiAlmSourceNoPage({
        typeClassify: this.typeCode
      })
      if (!res.isSuccess) {
        return
      }

      this.setData(this.customList, 'sourceCode', res.data)
    },
    // 查询项目关联的产品，用于缺陷和任务表单关联产品【主办/辅办】
    async getProjectProductList() {
      const res = await getAllProductInfoList(this.$route.params.id)
      if (!res.isSuccess) {
        return
      }
      this.setData(this.customList, 'productId', res.data)

      if (res.data.find(r => r.echoMap.isHost)) {
        this.$set(this.form, 'productId', res.data.find(r => r.echoMap.isHost).id)
        this.getVersion(this.form.productId)
        this.getModel(this.form.productId)
      }
    },
    // 关联产品版本
    async getVersion(val) {
      this.$set(this.form, 'productVersionId', '')
      this.$set(this.form, 'productRepairVersionId', '')

      const res = await getProductVersionList({ productId: val })
      if (!res.isSuccess) {
        return
      }
      this.setData(this.customList, 'productVersionId', res.data)
      this.setData(this.customList, 'productRepairVersionId', res.data)
    },
    // 关联产品功能
    async getModel(val) {
      const form = {}
      // this.$set(form, 'productModuleFunctionId', null)
      this.$set(form, 'productId', val)
      const parameter = {
        model: { ...form },
        extra: { tableSave: false }
      }
      const res = await getModule(parameter)
      if (!res.isSuccess) return
      this.setData(this.customList, 'productModuleFunctionId', this.getTreeList(res.data))
    },
    getTreeList(data, obj = []) {
      data.map(item => {
        const type = item.nodeType == 1 ? ' (模块)' : item.nodeType == 2 ? ' (功能)' : ''
        item.label = item.name + type
        if (item.children && item.children.length > 0) {
          this.getTreeList(item.children)
        }
      })
      return data
    },
    // 查优先级
    async getPrioritList() {
      const res = await apiAlmPriorityNoPage()

      if (!res.isSuccess) {
        return
      }

      this.prioritList = res.data

      this.setData(this.customList, 'priorityCode', res.data)
    },
    // 测试计划
    async getTestPlanList(val, e) {
      this.$set(this.form, 'testPlanId', '')
      const res = await planListByCondition({
        isFiled: false,
        projectId: val,
        name: e
      })
      if (!res.isSuccess) {
        return
      }
      res.data = this.sprintId ? res.data.filter(r => r.stateCode != 4) : res.data
      this.setData(this.customList, 'testPlanId', res.data)
    },
    // 归属计划
    async getmilestionePlanList(val, e) {
      this.$set(this.form, 'milestionePlan', '')
      const res = await planListByCondition({
        isFiled: false,
        projectId: val,
        name: e
      })
      if (!res.isSuccess) {
        return
      }
      res.data = this.sprintId ? res.data.filter(r => r.stateCode != 4) : res.data
      this.setData(this.customList, 'milestionePlan', res.data)
    },
    // 迭代计划
    async getPlanList(val, e) {
      this.$set(this.form, 'planId', '')
      const res = await planListByCondition({
        isFiled: false,
        projectId: val,
        name: e
      })
      if (!res.isSuccess) {
        return
      }

      this.planIdList = this.sprintId ? res.data.filter(r => r.stateCode != 4) : res.data
      this.setData(this.customList, 'planId', this.planIdList)
      // 项目下迭代默认回显当前迭代,[非未规划事项]
      if (this.sprintId && this.sprintId != -1) {
        this.$set(this.form, 'planId', this.sprintId)
      }
    },
    // 测试计划
    async getProjectTestPlan() {
      const res = await getProjectPlans({
        projectId: this.$route.params.id
      })
      if (!res.isSuccess) {
        return
      }
      this.setData(this.customList, 'testPlanId', res.data)

      if (this.$route.params.planId) {
        this.$set(this.form, 'testPlanId', this.$route.params.planId)
      }
    },
    // 归属产品
    async productList() {
      const res = await productListByCondition()

      if (!res.isSuccess) {
        return
      }

      this.setData(this.customList, 'productId', res.data)

      // 文档库，默认回显当前 文档库所属产品
      if (this.spaceInfo && this.spaceInfo.typeId == '3') {
        this.$set(this.form, 'productId', this.spaceInfo.bizId)
      }
    },
    // 关联模块
    async productModuleFunctionIdList() {
      const form = { 'productId': this.form.systemId }
      // this.$set(form, 'productModuleFunctionId', null)
      const parameter = {
        model: { ...form },
        extra: { tableSave: false }
      }
      const res = await getModuleTree(parameter)
      if (!res.isSuccess) {
        return
      }
      // 递归遍历res.data
      const processTreeData = (nodes) => {
        if (!nodes || !Array.isArray(nodes)) return []

        return nodes.map(item => {
          // 为当前节点设置label
          if (item.nodeType == 2) {
            item.isDisabled = true
          }
          const processedItem = {
            ...item,
            label: item.name
          }

          // 如果有子节点，递归处理子节点
          if (item.children && item.children.length > 0) {
            processedItem.children = processTreeData(item.children)
          }

          return processedItem
        })
      }
      // 处理完整的树形数据
      this.productModuleFunctionList = processTreeData(res.data)
    },

    // 关联交易
    async functionIdList(val) {
      const form = { 'productId': this.form.systemId, 'parentId': val }
      // this.$set(form, 'productModuleFunctionId', null)
      const parameter = {
        model: { ...form },
        extra: { tableSave: false }
      }
      const res = await getModuleTree(parameter)
      if (!res.isSuccess) {
        return
      }
      // 递归遍历res.data
      const processTreeData = (nodes) => {
        if (!nodes || !Array.isArray(nodes)) return []

        return nodes.map(item => {
          // 为当前节点设置label
          if (item.nodeType == 1) {
            item.isDisabled = true
          } else {
            item.isDisabled = false
          }
          const processedItem = {
            ...item,
            label: item.name
          }

          // 如果有子节点，递归处理子节点
          if (item.children && item.children.length > 0) {
            processedItem.isDisabled = false
            processedItem.children = processTreeData(item.children)
          }

          return processedItem
        })
      }
      // 处理完整的树形数据
      this.functionList = processTreeData(res.data)
    },

    // 关联系统
    async systemIdList() {
      const params = { current: 1,
        extra: { tableId: '1', tableSave: true, boardField: [], fixedView: [] },
        boardField: [],
        fixedView: [],
        tableId: '1',
        tableSave: true,
        model: {},
        order: 'descending',
        size: 12,
        sort: 'createTime' }
      const res = await apiProductData(params)
      if (!res.isSuccess) {
        return
      }
      this.systemList = res.data.records
    },

    // 归属项目
    async getProjectList(e) {
      const res = await queryListByCondition({ name: e })

      if (!res.isSuccess) {
        return
      }

      this.setData(this.customList, 'projectId', res.data)
      // 项目下默认回显当前项目
      if (this.$route.params.projectKey) {
        this.$set(this.form, 'projectId', this.$route.params.id)
      }
    },
    // 关联项目change事件，根据项目id查询迭代
    changeItem(item, key) {
      // if (item.key == 'projectId') {
      //   this.getPlanList(key)
      // }
      // if (item.key == 'productId') {
      //   this.getVersion(key)
      //   this.getModel(key)
      // }
    },
    eventDisposalRangeChange(value) {
      const textLength = this.$refs.editor[0].$el.innerText.replace(/[|]*\n/, '').length
      if (textLength >= 1000) {
        this.$refs.fixdForm.validateField(['description'])
      } else {
        this.$refs.fixdForm.clearValidate(['description'])
      }
    },
    onClose() {
      this.$emit('update:visible', false)
    },
    validatePromise(component, message) {
      return new Promise((resolve, reject) => {
        component.validate((valid, rules) => {
          if (!Object.keys(rules)?.length) {
            resolve(valid)
          } else {
            const firstRule = rules[Object.keys(rules)?.[0]]
            const errorMessage = firstRule?.[0]?.message || message
            this.$message.warning(errorMessage)
            reject({ valid, rules })
          }
        })
      })
    },
    // 保存
    async saveInfo() {
      try {
        if (this.isTooltip) {
          const fixdForm_promise = this.validatePromise(this.$refs.fixdForm, '请填写必填项')
          await fixdForm_promise
          const form_promise = this.validatePromise(this.$refs.form, '请填写必填项')
          await form_promise
        } else {
          await Promise.all([this.$refs.fixdForm.validate(), this.$refs.form.validate()])
        }
        this.saveLoading = true
        // 固定属性中的文件
        this.$set(this.fixdForm, 'files', this.$refs['uploadFile'][0].uploadFiles)

        // 自定义属性下的文件
        const customFile = this.customList.find(r => r.type == 'FILE')?.key
        if (customFile) {
          this.$set(this.form, customFile, this.$refs['formUploadFile'][0].uploadFiles.map(r => r.id).join(','))
        }

        // 项目下传projectId
        if (this.$route.params.projectKey) {
          this.$set(this.form, 'projectId', this.$route.params.id)
        }
        // 项目集下传programId
        if (this.$route.params.projectmCode) {
          this.$set(this.form, 'programId', this.$route.params.id)
        }
        const params = {
          ...this.form,
          ...this.fixdForm
        }

        // 判断key里面有没有C1到C30的，有的话，他的值如果是[],就是用逗号分隔成字符串传给后端
        var arr = Array.from(Array(30), (v, k) => `c${k + 1}`)
        const multipleList = this.customList.filter(r => r.multiple).map(r => r.key)
        for (var item in params) {
          if (this.id && item == 'id') {
            delete params[item]
          }
          if (arr.includes(item)) {
            if (multipleList.includes(item) && Array.isArray(params[item])) {
              params[item] = params[item].map(r => r).join(',') || null
            }
          }
        }
        const urlMap = {
          ISSUE: '/api/alm/alm/requirement',
          BUG: '/api/alm/alm/bug',
          TASK: '/api/alm/alm/task',
          RISK: '/api/alm/alm/risk',
          IDEA: '/api/alm/alm/idea',
          TESTREQ: '/api/alm/alm/testreq'
        }

        const finalParams = {
          ...params,
          ...this.formData
        }

        const res = await apiAlmAdd(urlMap[this.typeCode], finalParams)
        this.saveLoading = false
        if (!res.isSuccess) {
          this.$message.warning(res.msg)
          return
        }

        this.$message.success('保存成功')
        this.$emit('success')
        this.onClose()
      } catch (e) {
        this.saveLoading = false
      }
    },
    changeFlow() {
      this.$emit('success')
      this.onClose()
    }
  }
}
