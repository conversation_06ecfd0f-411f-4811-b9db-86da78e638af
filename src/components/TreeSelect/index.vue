<template>
  <Treeselect
    :append-to-body="true"
    :options="treeData"
    no-results-text="没有匹配项"
    no-children-text="没有子节点"
    no-options-text="没有可用选项"
    :normalizer="normalizerData||normalizer"
    v-bind="$attrs"
    :default-expand-level="Infinity"
    :z-index="zIndex"
    v-on="$listeners"
  >
    <slot />
    <template v-for="name in treeSlots" slot-scope="scope">
      <slot :name="name" v-bind="scope" />
    </template>
  </Treeselect>
</template>
<script>
import Treeselect from '@riophae/vue-treeselect'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'

export default {
  components: { Treeselect },
  props: {
    // 树结构数据
    treeData: {
      type: Array,
      default: () => []
    },
    zIndex: {
      type: Number,
      default: 9999
    },
    normalizerData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      treeSlots: ['option-label', 'value-label', 'before-list', 'after-list']
    }
  },
  methods: {
    normalizer(node) {
      if (node.children == null || node.children == 'null' || node.children.length == 0) {
        delete node.children
      }
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep.vue-treeselect{

    // line-height: 32px;
  .vue-treeselect__control {
  //  line-height: 32px;
   border-radius: 2px;
  }
	// .vue-treeselect__multi-value-item {
	// 	background-color: #f4f4f5;
	// 	border-color: #e9e9eb;
	// 	color: #909399;
	// 	font-weight: 500;
	// 	height: 24px;
	// 	line-height: 24px;
	// 	padding: 0 8px;
	// 	display: flex;
	// 	align-items: center;
	// }
	// .vue-treeselect__value-remove {
	// 	border-left: 0;
	// 	color: #909399;
	// 	height: 16px;
	// 	width: 16px;
	// 	line-height: 16px;
	// 	border-radius: 50%;
	// 	background-color: #c0c4cc;
	// }
	.vue-treeselect__menu {
		font-size: 14px;
	}

  .vue-treeselect__value-container{
    height: 30px;
  }

}
</style>
