<template>
  <vone-div-wrapper :title="'关联缺陷'">
    <div slot="actions">
      <el-button
        class="con-btn"
        type="text"
        icon="el-icon-link"
        @click="changeTask('1')"
      >关联</el-button>
      <el-button
        class="con-btn"
        type="text"
        icon="el-icon-plus"
        @click="changeTask('0')"
      >新增</el-button>
    </div>

    <div v-if="newTask || connectTask">
      <div class="row_host">
        <simpleAddIssue
          v-if="newTask"
          no-file
          :type-code="'BUG'"
          :issue-id="issueId"
          class="mt-10"
          @success="init"
          @cancel="newTask = false"
        />

        <el-row v-if="connectTask" :gutter="24">
          <el-col :span="24">
            <el-select
              v-model="taskForm.bugId"
              placeholder="请选择缺陷"
              multiple
              filterable
              clearable
              style="margin-right: 10px"
            >
              <el-option
                v-for="item in bugList"
                :key="item.id"
                :title="item.name"
                :label="`${item.code}  ${item.name}`"
                :value="item.id"
              />
            </el-select>
          </el-col>
        </el-row>
        <div v-if="connectTask" class="minbtns">
          <el-button
            class="miniBtn"
            @click="connectTask = false"
          >取消</el-button>
          <el-button
            type="primary"
            class="miniBtn"
            style="margin-left: 8px"
            @click="saveTask"
          >确定</el-button>
        </div>
      </div>
    </div>

    <!-- 关联任务列表 -->
    <div style="height: calc(100vh - 260px);">
      <vxe-table
        ref="issue-bug"
        :loading="loading"
        class="vone-vxe-table"
        row-id="id"
        :data="tableData.records"
        style="margin-top: 10px"
        border
        auto-resize
        height="auto"
        show-overflow="tooltip"
        :empty-render="{ name: 'empty' }"
        :column-config="{ minWidth: '120px' }"
        :checkbox-config="{ reserve: true }"
        @getTableData="getTableData"
      >
        <vxe-column
          field="name"
          title="名称"
          show-overflow-tooltip
          width="300"
          class-name="name_col"
        >
          <template slot-scope="scope">
            <a class="table_title" @click="toRouter(scope.row)">
              <svg class="icon svg-icon" aria-hidden="true">
                <use xlink:href="#el-icon-icon-quexian" />
              </svg>
              {{ scope.row.code }}
              {{ scope.row.name }}
            </a>
          </template>
        </vxe-column>
        <vxe-column
          field="planEtime"
          show-overflow-tooltip
          min-width="160"
          title="计划完成时间"
        />
        <vxe-column
          field="handleBy"
          title="处理人"
          min-width="100"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <span>
              <vone-user-avatar
                :avatar-path="
                  getUserInfo(scope.row)
                    ? getUserInfo(scope.row).avatarPath
                    : ''
                "
                :name="
                  getUserInfo(scope.row) ? getUserInfo(scope.row).name : ''
                "
              />
            </span>
          </template>
        </vxe-column>
        <vxe-column title="操作" fixed="right" align="left" width="55">
          <template #default="{ row }">
            <template>
              <el-tooltip class="item" content="取消关联" placement="top">
                <el-button
                  type="text"
                  icon="iconfont el-icon-icon-line-jiechuguanlian"
                  @click="tableDelete(row)"
                />
              </el-tooltip>
            </template>
          </template>
        </vxe-column>
      </vxe-table>
    </div>

    <vone-pagination
      ref="pagination"
      :total="tableData.total"
      style="position: static"
      @update="getTableData"
    />
    <!-- 编辑缺陷 -->
    <vone-custom-edit
      v-if="defectInfoParam.visible"
      :key="defectInfoParam.key"
      :visible.sync="defectInfoParam.visible"
      v-bind="defectInfoParam"
      :type-code="'BUG'"
      :left-tabs="leftTabs"
      :right-tabs="rightTabs"
    />
  </vone-div-wrapper>

</template>

<script>
import { getTodoBugList, creatBugForProject } from '@/api/vone/project/issue'
import { apiAlmGetDefectData } from '@/api/vone/project/defect'
import simpleAddIssue from '../components/simple-add-issue.vue'

export default {
  name: 'BugTab',
  components: {
    simpleAddIssue
  },
  props: {
    issueId: {
      type: String,
      default: undefined
    },
    assistantSystem: {
      type: String,
      default: null
    },
    doneTime: {
      type: Number,
      default: null
    },
    noEdit: {
      type: Boolean,
      default: false
    },
    isNotLink: {
      type: Boolean,
      default: false
    },
    projectKey: {
      type: Array,
      default: () => []
    },
    // 拆分子项是否弹框
    showPopupForSplit: {
      type: Boolean,
      default: false
    },
    jumpTo: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      newTask: false,
      connectTask: false,
      tableData: {
        records: []
      },
      formData: {},
      bugList: [],
      taskForm: {
        bugId: []
      },
      loading: false,
      total: 0,
      rowData: {},
      defectInfoParam: { visible: false, title: '' },
      tableList: [],
      leftTabs: [
        {
          label: '关联缺陷',
          name: 'DefectToDefect'
        }
      ],
      rightTabs: [
        {
          active: true,
          label: '评论',
          name: 'comment'
        },
        {
          label: '活动',
          name: 'active'
        },
        {
          label: '变更记录',
          name: 'activityRecord'
        },
        {
          label: '工时',
          name: 'workTime'
        }
      ]

    }
  },
  computed: {
    getUserInfo() {
      return function(row) {
        return row?.handleBy && row?.echoMap?.handleBy
      }
    }
  },
  watch: {
    issueId() {
      if (this.issueId) {
        this.getTableData()
        this.getBugList()
      }
    },
    immediate: true
  },
  mounted() {
    this.getTableData()
    this.getBugList()
  },
  methods: {
    toRouter(val) {
      if (this.jumpTo == false) {
        this.rowData = val
        this.defectInfoParam = {
          visible: true,
          title: '缺陷详情',
          key: Date.now(),
          id: val.id,
          infoDisabled: true,
          tableList: this.tableList,
          rowTypeCode: val.typeCode,
          stateCode: val.stateCode
        }
      } else {
        if (!this.$permission('reqm-center-require-list')) {
          this.$message.warning(
            '当前登录账户没有查看需求的权限,请联系管理员授权'
          )
          return
        }
        const newpage = this.$router.resolve({
          path: `/project/defect/${this.$route.params.projectKey}/${this.$route.params.projectTypeCode}/${this.$route.params.id}`,
          query: {
            showDialog: true,
            queryId: val.id,
            rowTypeCode: val.typeCode,
            stateCode: val.stateCode
          }
        })
        window.open(newpage.href, '_blank')
      }
    },
    changeTask(val) {
      if (val == 0) {
        if (this.showPopupForSplit) {
          this.$emit('add-child', 'bug')
        } else {
          this.newTask = true
          this.connectTask = false
        }
      } else {
        this.getBugList()
        this.newTask = false
        this.connectTask = true
      }
    },
    async getTableData() {
      this.loading = true
      let params = {}

      this.$set(this.formData, 'requirementId', [this.issueId])
      this.$set(this.formData, 'projectId', [this.$route.params.id])
      const tableAttr = this.$refs.pagination?.pageObj || {
        current: 1,
        size: 20
      }
      params = {
        ...tableAttr,
        extra: {},
        model: { ...this.formData }
      }
      const res = await apiAlmGetDefectData(params)
      this.loading = false
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.tableData = res.data
      this.tableList = res.data.records || []
      this.total = res.data.total
    },
    async tableDelete(val) {
      this.$confirm(`你确定要取消【 ${val.name} 】和需求的关联吗?`, '删除', {
        confirmButtonText: '确认',
        type: 'warning',
        closeOnClickModal: false
      })
        .then(async() => {
          const { isSuccess, msg } = await creatBugForProject({
            correlatedType: 'DELETE',
            requirementId: this.issueId,
            bugIds: [val.id]
          })
          if (!isSuccess) {
            this.$message.error(msg)
            return
          }
          this.$message.success('删除成功')
          this.getTableData()
          // this.$emit('initList')
        })
        .catch(() => {})
    },
    // 查询任务列表
    async getBugList() {
      const { data, isSuccess, msg } = await getTodoBugList(
        this.$route.params.id
      )
      if (!isSuccess) {
        this.$message.warning(msg)
        return
      }
      this.bugList = data
    },
    async saveTask() {
      // try {
      //   await this.$refs.taskFormRef.validate()
      // } catch (error) {
      //   return
      // }
      const { isSuccess, msg } = await creatBugForProject({
        correlatedType: 'ADD',
        requirementId: this.issueId,
        bugIds: this.taskForm.bugId
      })
      if (!isSuccess) {
        this.$message.warning(msg)
        return
      }

      this.$message.success('保存成功')
      this.connectTask = false
      this.$set(this.taskForm, 'bugId', [])
      this.getTableData()
      this.$emit('initList')
    },
    init() {
      this.newTask = false
      this.getTableData()
    }
  }
}
</script>
<style lang="scss" scoped>
.con-btn {
  font-size: 14px;
  font-weight: 400;
  padding: 0 12x;
  min-width: auto;
  font-weight: 400;
}
.mt-10 {
  width: 100%;
  margin-bottom: 10px;
}

.shadow_bottom {
  // text-align: center;
  margin: 0 -38px 16px;
  padding-bottom: 16px;
  // padding: 0 16px 16px;
  box-shadow: 0px 15px 10px -15px #ccc;
  .row_host {
    padding: 0px 48px 0px 32px;
    .opeartionBtn {
      text-align: right;
      padding-right: 0px;
      padding-left: 0px;
    }
  }
}
.empty-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 0 8px;
  // height: 100%;
  height: calc(60vh - 158px);
  .empty-svg {
    width: 50px;
    height: 40px;
  }
}
::v-deep .el-button + .el-button {
  margin: 0;
}
::v-deep .name_col {
  .cell {
    display: flex;
    div.name_icon {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: normal;
      word-break: break-all;
      flex: 1;
      display: flex;
      align-items: center;
    }
  }
}
.table_title {
  display: inline-block;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
::v-deep .el-button {
  line-height: 24px;
  height: 24px;
}
.minbtns {
  text-align: right;
  ::v-deep .el-button + .el-button {
    margin: 12px 0px 12px 12px;
  }
}
</style>
