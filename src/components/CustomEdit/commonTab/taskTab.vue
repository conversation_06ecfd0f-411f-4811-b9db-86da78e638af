<template>
  <vone-div-wrapper :title="'关联任务'">
    <div slot="actions">
      <el-button
        class="con-btn"
        type="text"
        icon="el-icon-link"
        @click="changeTask('1')"
      >关联</el-button>
      <!-- <el-divider direction="vertical" /> -->
      <el-button
        class="con-btn"
        type="text"
        icon="el-icon-plus"
        @click="changeTask('0')"
      >新增</el-button>
    </div>

    <div v-if="newTask || connectTask">
      <div class="row_host">
        <simpleAddIssue
          v-if="newTask"
          no-file
          :type-code="'TASK'"
          :issue-id="issueId"
          class="mt-10"
          @success="init"
          @cancel="newTask = false"
        />

        <el-row v-if="connectTask" :gutter="24">
          <el-col :span="24">
            <el-select
              v-model="taskForm.taskId"
              placeholder="请选择任务"
              multiple
              filterable
              clearable
              style="margin-right: 10px"
            >
              <el-option
                v-for="item in taskList"
                :key="item.id"
                :title="item.name"
                :label="`${item.code}  ${item.name}`"
                :value="item.id"
              />
            </el-select>
          </el-col>
        </el-row>
        <div v-if="connectTask" class="minbtns" style="text-align: right">
          <el-button
            class="miniBtn"
            @click="connectTask = false"
          >取消</el-button>
          <el-button
            type="primary"
            class="miniBtn"
            style="margin-left: 8px"
            @click="saveTask"
          >确定</el-button>
        </div>
      </div>
    </div>

    <!-- 关联任务列表 -->
    <div style="height: calc(100vh - 260px);">
      <vxe-table
        ref="issue-task"
        :loading="loading"
        class="vone-vxe-table"
        row-id="id"
        border
        auto-resize
        height="auto"
        show-overflow="tooltip"
        :empty-render="{ name: 'empty' }"
        :data="tableData.records"
        :column-config="{ minWidth: '120px' }"
        :checkbox-config="{ reserve: true }"
        @getTableData="getTableData"
      >
        <vxe-column field="name" title="名称">
          <template slot-scope="scope">
            <a @click="toRouter(scope.row, 'info')">
              <svg class="icon svg-icon" aria-hidden="true">
                <use xlink:href="#el-icon-icon-renwu" />
              </svg>
              {{ scope.row.code }}
              {{ scope.row.name }}
            </a>
          </template>
        </vxe-column>
        <vxe-column field="planEtime" width="160" title="计划完成时间" />
        <vxe-column field="handleBy" title="处理人" width="110">
          <template slot-scope="scope">
            <vone-user-avatar
              :avatar-path="
                getUserInfo(scope.row) ? getUserInfo(scope.row).avatarPath : ''
              "
              :name="getUserInfo(scope.row) ? getUserInfo(scope.row).name : ''"
            />
          </template>
        </vxe-column>
        <vxe-column title="操作" fixed="right" align="left" width="120">
          <template #default="{ row }">
            <template>
              <el-tooltip class="item" content="编辑" placement="top">
                <el-button
                  style="padding: 0 10px; min-width: 20px"
                  type="text"
                  :disabled="
                    row.stateCode == 'DONE' ||
                      !$permission('project_issue_edit')
                  "
                  icon="iconfont el-icon-application-edit"
                  @click="toRouter(row, 'edit')"
                />
              </el-tooltip>
              <el-divider direction="vertical" />
              <el-tooltip class="item" content="取消关联" placement="top">
                <el-button
                  style="padding: 0 10px; min-width: 20px"
                  type="text"
                  icon="iconfont el-icon-icon-line-jiechuguanlian"
                  @click="tableDelete(row)"
                />
              </el-tooltip>
            </template>
          </template>
        </vxe-column>
      </vxe-table>
    </div>
    <vone-pagination
      ref="pagination"
      :total="tableData.total"
      style="position: static"
      @update="getTableData"
    />
    <!-- 详情 -->
    <vone-custom-info
      v-if="taskInfoParam.visible"
      ref="vone-custom-info"
      :key="taskInfoParam.key"
      :visible.sync="taskInfoParam.visible"
      v-bind="taskInfoParam"
      :type-code="'TASK'"
      :left-tabs="leftTabs"
      :right-tabs="rightTabs"
      @success="getTableData"
      @initList="getTableData"
    />
    <vone-custom-edit
      v-if="taskEditParam.visible"
      ref="vone-custom-info"
      :key="taskEditParam.key"
      :visible.sync="taskEditParam.visible"
      v-bind="taskEditParam"
      :type-code="'TASK'"
      :left-tabs="leftTabs"
      :right-tabs="rightTabs"
      @success="getTableData"
      @initList="getTableData"
    />
  </vone-div-wrapper>
</template>

<script>
import {
  apiAlmFindTodoByProjectId,
  apiGetTaskList,
  apiAlmCorrelatedTask
} from '@/api/vone/project/task'
import simpleAddIssue from '../components/fast-add-task.vue'
export default {
  name: 'TaskTab',
  components: {
    simpleAddIssue
  },
  props: {
    issueInfo: {
      type: Object,
      default: null
    },
    issueId: {
      type: String,
      default: undefined
    },
    assistantSystem: {
      type: String,
      default: null
    },
    doneTime: {
      type: Number,
      default: null
    },
    noEdit: {
      type: Boolean,
      default: false
    },
    isNotLink: {
      type: Boolean,
      default: false
    },
    projectKey: {
      type: Array,
      default: () => []
    },
    // 拆分子项是否弹框
    showPopupForSplit: {
      type: Boolean,
      default: false
    },
    jumpTo: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      newTask: false,
      connectTask: false,
      tableData: {
        records: []
      },
      createSimple: false,
      typeId: '',
      formData: {},
      taskList: [],
      addTask: null,
      taskForm: {
        taskId: []
      },
      rules: {
        taskId: [{ required: true, message: '请选择任务', trigger: 'change' }]
      },
      loading: false,
      aiVisible: false,
      taskInfoParam: { visible: false },
      taskEditParam: { visible: false },
      tableList: [],
      leftTabs: [
        {
          label: '任务',
          name: 'TaskTask'
        }
      ],
      rightTabs: [
        {
          active: true,
          label: '评论',
          name: 'comment'
        },
        {
          label: '活动',
          name: 'active'
        },
        {
          label: '变更记录',
          name: 'activityRecord'
        },
        {
          label: '工时',
          name: 'workTime'
        }
      ]
    }
  },
  computed: {
    getUserInfo() {
      return function(row) {
        return row?.handleBy && row?.echoMap?.handleBy
      }
    }
  },
  watch: {
    issueId() {
      if (this.issueId) {
        this.getTableData()
        this.getTaskList()
      }
    },
    immediate: true
  },
  mounted() {
    this.getTableData()
    this.getTaskList()
  },
  methods: {
    creactIssueSuccess() {
      this.getTableData()
    },
    toRouter(val, type) {
      // if (!this.$permission('reqm-center-require-list')) {
      //   this.$message.warning('当前登录账户没有查看需求的权限,请联系管理员授权')
      //   return
      // }
      if (this.jumpTo == false) {
        if (type == 'info') {
          this.taskInfoParam = {
            visible: true,
            title: '任务详情',
            id: val.id,
            key: Date.now(),
            infoDisabled: true,
            tableList: this.tableList,
            rowTypeCode: val.typeCode,
            stateCode: val.stateCode,
            showPopupForSplit: false
          }
        } else {
          this.taskEditParam = {
            visible: true,
            title: '编辑任务',
            id: val.id,
            key: Date.now(),
            infoDisabled: true,
            tableList: this.tableList,
            rowTypeCode: val.typeCode,
            stateCode: val.stateCode,
            showPopupForSplit: false
          }
        }
      } else {
        const newpage = this.$router.resolve({
          path: `/project/task/${this.$route.params.projectKey}/${this.$route.params.projectTypeCode}/${this.$route.params.id}`,
          query: {
            showDialog: true,
            queryId: val.id,
            rowTypeCode: val.typeCode,
            stateCode: val.stateCode,
            type: type
          }
        })
        window.open(newpage.href, '_blank')
      }
    },
    changeTask(val) {
      if (val == 0) {
        if (this.showPopupForSplit) {
          this.$emit('add-child', 'task')
        } else {
          this.newTask = true
          this.connectTask = false
        }
      } else {
        this.getTaskList()
        this.newTask = false
        this.connectTask = true
      }
    },
    async getTableData() {
      this.loading = true
      let params = {}

      this.$set(this.formData, 'requirementId', [this.issueId])
      this.$set(this.formData, 'projectId', [this.$route.params.id])
      const tableAttr = this.$refs.pagination?.pageObj || {
        current: 1,
        size: 20
      }
      params = {
        ...tableAttr,
        extra: {},
        model: { ...this.formData }
      }
      const res = await apiGetTaskList(params)
      this.loading = false
      if (!res.isSuccess) {
        this.$message.warning(res.msg)
        return
      }
      this.tableData = res.data
      this.tableList = res.data.records || []
    },
    async tableDelete(val) {
      this.$confirm(`你确定要取消【 ${val.name} 】和需求的关联吗?`, '删除', {
        confirmButtonText: '确认',
        type: 'warning',
        closeOnClickModal: false
      })
        .then(async() => {
          const { isSuccess, msg } = await apiAlmCorrelatedTask({
            correlatedType: 'DELETE',
            requirementId: this.issueId,
            taskIds: [val.id]
          })
          if (!isSuccess) {
            this.$message.error(msg)
            return
          }
          this.$message.success('删除成功')
          this.getTableData()
          // this.$emit('initList')
        })
        .catch(() => {})
    },
    // 查询任务列表
    async getTaskList() {
      const { data, isSuccess, msg } = await apiAlmFindTodoByProjectId(
        this.$route.params.id
      )
      if (!isSuccess) {
        this.$message.warning(msg)
        return
      }
      this.taskList = data
    },
    async saveTask() {
      // try {
      //   await this.$refs.taskFormRef.validate()
      // } catch (error) {
      //   return
      // }
      const { isSuccess, msg } = await apiAlmCorrelatedTask({
        correlatedType: 'ADD',
        requirementId: this.issueId,
        taskIds: this.taskForm.taskId
      })
      if (!isSuccess) {
        this.$message.warning(msg)
        return
      }
      this.$message.success('保存成功')
      this.connectTask = false
      this.$set(this.taskForm, 'taskId', [])
      this.getTableData()
      this.$emit('initList')
    },
    async init() {
      this.newTask = false
      await this.getTableData()
    }
  }
}
</script>
<style lang="scss" scoped>
.con-btn {
  font-size: 14px;
  padding: 0 12x;
  min-width: auto;
  font-weight: 400;
}
::v-deep .el-button + .el-button {
  margin: 0px;
}
.minbtns {
  ::v-deep .el-button + .el-button {
    margin: 12px 0px 12px 12px;
  }
}

.mt-10 {
  width: 100%;
  margin-bottom: 10px;
}

::v-deep .el-button {
  line-height: 24px;
  height: 24px;
}
</style>
