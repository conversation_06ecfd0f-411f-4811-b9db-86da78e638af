<template>
  <!-- 编辑 -->
  <div class="drawerBox">
    <el-dialog top="5vh" :visible.sync="visible" width="1000px" :before-close="onClose" :close-on-click-modal="false">
      <div slot="title">
        <div>
          {{ title }}【
          <span class="drawerCode">
            {{ fixedForm.code }}
          </span>
          】
          <span v-if="!hidePrv">
            <i class="iconfont el-icon-yibiaopan-shangyi nextBtn" @click="dataPrev" />
            <i class="iconfont el-icon-yibiaopan-xiayi nextBtn" @click="dataNext" />
          </span>
        </div>
      </div>
      <div>
        <el-form ref="fixedForm" :model="fixedForm" :rules="fixdFormRules" label-position="top">
          <div class="basicHeader">
            <el-skeleton :loading="drawerLoading" style="width: 100%" animated>
              <template slot="template">
                <el-skeleton-item variant="p" style="width: 30%; margin-bottom: 14px" />
                <div style="display: flex; align-items: center; justify-content: space-between">
                  <div v-for="index in 5" :key="index" style="flex: 1; display: flex; align-items: center">
                    <el-skeleton-item variant="image" style="width: 30px; height: 30px" />
                    <div style="width: 190px; margin-left: 10px">
                      <el-skeleton-item variant="p" style="width: 50%; margin-bottom: 6px" />
                      <el-skeleton-item variant="p" style="width: 70%" />
                    </div>
                  </div>
                </div>
              </template>
            </el-skeleton>
            <div v-if="!drawerLoading">
              <el-row v-for="item in fixedProperty.filter(r => r.key == 'name')" :key="item.id" class="name-row">
                <el-col :span="24">
                  <!-- 输入框 -->
                  <el-input v-model="fixedForm[item.key]" :placeholder="item.placeholder" :disabled="!item.isUpdate" />
                </el-col>
              </el-row>

              <div class="basica-form">
                <div
                  v-for="item in fixedProperty.filter(r => r.key != 'name')"
                  :key="item.id"
                  class="fixedItem"
                  :class="{ fixedItemType: item.key == 'typeCode' }"
                >
                  <!-- 表单项 -->
                  <el-form-item :label="item.name" :prop="item.key">
                    <!-- 图标 -->
                    <span v-if="item.key == 'typeCode'">
                      <span v-if="fixedForm.echoMap && fixedForm.echoMap[item.key]">
                        <i
                          :style="{ color: fixedForm.echoMap[item.key].color }"
                          :class="['iconfont', `${fixedForm.echoMap[item.key].icon}`]"
                          style="font-size: 24px"
                        />
                      </span>
                      <span v-else>
                        <svg class="icon" aria-hidden="true" style="font-size: 24px">
                          <use :xlink:href="`#el-icon-icon-yixiang`" />
                        </svg>
                      </span>
                    </span>

                    <span v-else-if="item.key == 'handleBy'">
                      <span v-if="fixedForm.echoMap && fixedForm.echoMap[item.key]">
                        <vone-user-avatar
                          :avatar-path="fixedForm.echoMap[item.key].avatarPath"
                          :avatar-type="true"
                          :show-name="false"
                          height="24px"
                          width="24px"
                        />
                      </span>
                      <span v-else>
                        <i class="iconfont el-icon-icon-light-avatar" style="font-size: 24px" />
                      </span>
                    </span>

                    <span v-else>
                      <svg class="icon" aria-hidden="true" style="font-size: 24px">
                        <use :xlink:href="`#${iconMap[item.key]}`" />
                      </svg>
                    </span>
                    <!-- 人员组件 -->
                    <vone-remote-user
                      v-if="item.type == 'USER' || item.type == 'PROJECTUSER'"
                      v-model="fixedForm[item.key]"
                      :project-id="
                        item.key == 'leadingBy' || item.key == 'putBy' || item.key == 'handleBy' ? projectId : ''
                      "
                      :disabled="!item.isUpdate"
                      :no-name="false"
                      :multiple="item.multiple"
                    />
                    <!-- 标签 -->
                    <tagSelect
                      v-else-if="item.type == 'SELECT' && item.key == 'tagId'"
                      v-model="fixedForm[item.key]"
                      multiple
                      collapse-tags
                    />

                    <!-- 输入框 -->
                    <el-input
                      v-else-if="item.type == 'INPUT'"
                      v-model="fixedForm[item.key]"
                      :placeholder="item.placeholder"
                      :disabled="!item.isUpdate"
                    />

                    <!-- 状态 -->
                    <span v-else-if="item.key == 'stateCode'">
                      <StateCode
                        v-if="fixedForm[item.key]"
                        :code="fixedForm[item.key]"
                        :target-id="id"
                        :form="fixedForm"
                        @changeFlow="editStatus(fixedForm[item.key])"
                      />
                    </span>
                    <!-- 下拉框 -->
                    <el-select
                      v-else-if="item.type == 'SELECT' && item.key != 'stateCode'"
                      v-model="fixedForm[item.key]"
                      :placeholder="item.placeholder"
                      :disabled="!item.isUpdate || item.key === 'typeCode'"
                    >
                      <el-option
                        v-for="i in item.options"
                        :key="i.id"
                        :label="i.name"
                        :value="item.key == 'planId' ? i.id : i.code"
                      >
                        <span v-if="item.key == 'typeCode'">
                          <i :class="['iconfont', `${i.icon}`]" :style="{ color: i.color }" />
                          {{ i.name }}
                        </span>
                      </el-option>
                    </el-select>
                    <!-- 日期组件 -->
                    <el-date-picker
                      v-else-if="item.type == 'DATE'"
                      v-model="fixedForm[item.key]"
                      class="dataPicker"
                      type="datetime"
                      format="yyyy-MM-dd HH:mm"
                      value-format="yyyy-MM-dd HH:mm:ss"
                      :default-time="`${item.defaultTime ? item.defaultTime : '00:00'}:00`"
                      :placeholder="item.placeholder"
                      :disabled="!item.isUpdate"
                      :picker-options="pickerOptions(item.key)"
                      style="min-width: 155px"
                    />
                  </el-form-item>
                </div>
              </div>
            </div>
          </div>
        </el-form>
        <el-row>
          <el-col :span="16" class="centerBox">
            <el-tabs v-model="tabActive" class="vone-tab-line" @tab-click="handleClick($event, leftTabs)">
              <el-tab-pane label="基本信息" class="contentBox" name="basic">
                <div class="pContent">
                  <el-skeleton v-if="infoLoading" style="width: 100%" :loading="infoLoading" animated :count="8">
                    <template slot="template">
                      <div style="padding: 14px">
                        <el-row type="flex" style="margin-top: 6px">
                          <el-skeleton-item variant="p" style="width: 50%; margin-right: 16px" />
                          <el-skeleton-item variant="p" style="width: 50%" />
                        </el-row>
                      </div>
                    </template>
                  </el-skeleton>
                  <div v-else class="centerBasic">
                    <el-form ref="otherForm" :model="otherForm" label-position="top">
                      <el-row>
                        <el-col v-for="item in basicProperty" :key="item.id" :span="24">
                          <el-form-item :prop="item.key" class="custom-label">
                            <span slot="label" class="custom-label-wrapper">
                              <span style="flex: 1">{{ item.name }}</span>
                            </span>
                            <!-- 文本编辑器 -->
                            <span v-if="item.type == 'EDITOR'">
                              <vone-editor
                                ref="editor"
                                v-model="otherForm[item.key]"
                                :preview="!item.isUpdate"
                                @input.native="eventDisposalRangeChange(otherForm[item.key])"
                              />
                            </span>

                            <!-- 文件 -->
                            <vone-upload
                              v-else-if="item.type == 'FILE'"
                              ref="uploadFile"
                              :biz-type="fileMap[typeCode]"
                              :files-data="otherForm.files"
                              :disabled="!item.isUpdate"
                            />
                          </el-form-item>
                        </el-col>
                      </el-row>
                    </el-form>

                    <el-divider />
                    <vone-div-wrapper :title="'基本属性'">
                      <el-form ref="customForm" :model="customForm" label-position="top" :rules="formRules">
                        <el-row :gutter="24" type="flex" class="row-box">
                          <div v-for="item in customList" :key="item.id" style="width: 50%;">
                            <el-col v-if="item.key != 'tradeProjectId'" :span="24">
                              <el-form-item :label="item.name" :prop="item.key">
                                <!-- 人员组件 -->
                                <vone-remote-user
                                  v-if="item.type == 'USER'"
                                  v-model="customForm[item.key]"
                                  :project-id="
                                    item.key == 'leadingBy' || item.key == 'putBy' || item.key == 'handleBy'
                                      ? projectId
                                      : ''
                                  "
                                  :disabled="!item.isUpdate"
                                  :multiple="item.multiple"
                                />

                                <!-- 文件 -->
                                <vone-upload
                                  v-else-if="item.type == 'FILE'"
                                  ref="formUploadFile"
                                  :files-data="
                                    fixedForm.echoMap && fixedForm.echoMap[item.key]
                                      ? fixedForm.echoMap[item.key]
                                      : fixedForm[item.key]
                                  "
                                  :biz-type="fileMap[typeCode]"
                                  @onSuccess="files => updateFiles(files, item.key)"
                                  @remove="file => removeFile(file, item.key)"
                                />

                                <!-- 项目人员组件 -->
                                <projectRemoteUser
                                  v-if="item.type == 'PROJECTUSER'"
                                  v-model="customForm[item.key]"
                                  :multiple="item.multiple"
                                />

                                <!-- 数 -->
                                <el-input-number
                                  v-else-if="item.type == 'INT'"
                                  v-model="customForm[item.key]"
                                  :min="0"
                                  :max="1000"
                                  :precision="item.precision"
                                  controls-position="right"
                                  :disabled="!item.isUpdate"
                                  style="width: 100%"
                                  :placeholder="item.placeholder"
                                  @input.native="e => eventDisposalRangeChangeINT(e, customForm[item.key])"
                                />

                                <!-- 日期组件 -->
                                <el-date-picker
                                  v-else-if="item.type == 'DATE'"
                                  v-model="customForm[item.key]"
                                  prefix-icon="el-icon-date"
                                  type="datetime"
                                  format="yyyy-MM-dd HH:mm"
                                  value-format="yyyy-MM-dd HH:mm:ss"
                                  :default-time="`${item.defaultTime}:00`"
                                  :placeholder="item.placeholder"
                                  :disabled="!item.isUpdate"
                                  :picker-options="pickerOptions(item.key)"
                                />
                                <!-- 输入框 -->
                                <el-input
                                  v-if="item.type == 'INPUT'"
                                  v-model="customForm[item.key]"
                                  :placeholder="item.placeholder"
                                  :disabled="!item.isUpdate"
                                />
                                <!-- 组织机构 -->
                                <vone-tree-select
                                  v-else-if="item.type == 'ORG'"
                                  v-model="customForm[item.key]"
                                  search-nested
                                  :tree-data="orgData"
                                  placeholder="请选择机构"
                                  :disabled="!item.isUpdate"
                                  :multiple="item.multiple"
                                />

                                <!-- 输入文本框 -->
                                <el-input
                                  v-else-if="item.type == 'TEXTAREA'"
                                  v-model="customForm[item.key]"
                                  type="textarea"
                                  :placeholder="item.placeholder"
                                  :disabled="!item.isUpdate"
                                  autosize
                                />
                                <template v-else-if="item.type == 'LINK'">
                                  <el-tooltip class="item" effect="dark" :content="customForm[item.key]" placement="top">
                                    <span
                                      v-if="!item.isShowLink"
                                      class="name file-name"
                                      @click="fileClick(customForm[item.key])"
                                    >{{ customForm[item.key] }}</span>
                                  </el-tooltip>
                                  <el-input
                                    v-if="item.isShowLink"
                                    v-model="customForm[item.key]"
                                    v-focus
                                    :placeholder="item.placeholder"
                                    @blur="editFn(item, false)"
                                  />
                                  <el-button
                                    v-if="!item.isShowLink"
                                    class="buttons"
                                    icon="iconfont el-icon-application-edit"
                                    type="text"
                                    size="small"
                                    @click="editFn(item, true)"
                                  />
                                </template>
                                <template v-else-if="item.type == 'SELECT' && item.key != 'productModuleFunctionId'">
                                  <!-- 远程搜索下拉 -->
                                  <el-select
                                    v-if="
                                      item.key == 'requirementId' ||
                                        item.key == 'planId' ||
                                        item.key == 'projectId' ||
                                        item.key == 'bugId'
                                    "
                                    v-model="customForm[item.key]"
                                    remote
                                    :placeholder="item.placeholder"
                                    :multiple="item.multiple"
                                    clearable
                                    filterable
                                    :disabled="!item.isUpdate"
                                    :remote-method="e => remoteMethod(e, item.key)"
                                    @focus="setOptionWidth"
                                    @change="changeProject(item.key)"
                                  >
                                    <el-option
                                      v-for="i in item.options"
                                      :key="i.id"
                                      :label="i.name"
                                      :value="i.id"
                                      :style="{ width: selectOptionWidth }"
                                    >
                                      {{ i.code }}
                                      {{ i.name }}
                                    </el-option>
                                  </el-select>
                                  <!-- 下拉单选框 -->
                                  <el-select
                                    v-else
                                    v-model="customForm[item.key]"
                                    :placeholder="item.placeholder"
                                    clearable
                                    filterable
                                    :multiple="item.multiple"
                                    :disabled="!item.isUpdate"
                                    @focus="setOptionWidth"
                                    @change="changeSelect"
                                  >
                                    <el-option
                                      v-for="i in item.options"
                                      :key="i.id"
                                      :label="i.name"
                                      :value="
                                        item.key == 'sourceCode' ||
                                          item.key == 'typeCode' ||
                                          item.key == 'priorityCode'||
                                          item.key == 'reportStatus'||
                                          item.key == 'reportType'
                                          ? i.code
                                          : i.id
                                      "
                                      :style="{ width: selectOptionWidth }"
                                    >
                                      <span v-if="item.key == 'ideaId'">{{ `${i.code}  ${i.name}` }}</span>

                                      <span v-if="item.key == 'productId'">
                                        {{ i.name }}
                                      <!-- <span v-if="i.echoMap" style="float:right">
                                        <el-tag v-if="i.echoMap.isHost" type="success">
                                          主
                                        </el-tag>
                                        <el-tag v-if="i.echoMap.isHost == false" type="warning">
                                          辅
                                        </el-tag>
                                      </span> -->
                                      </span>
                                    </el-option>
                                  </el-select>
                                <!-- 下拉多选框 -->
                                <!-- <el-select v-else-if="item.type == 'SELECT' && item.multiple&&item.key!='productModuleFunctionId'" v-model="customForm[item.key]" :placeholder="item.placeholder" multiple clearable filterable :disabled="!item.isUpdate" @focus="setOptionWidth">
                                  <el-option v-for="i in item.options" :key="i.id" :label="i.name" :value="i.name" :style="{ width: selectOptionWidth }" />
                                </el-select> -->
                                </template>

                                <!-- 功能模块字段 -->
                                <vone-tree-select
                                  v-else-if="item.type == 'SELECT' && item.key == 'productModuleFunctionId'"
                                  v-model="customForm[item.key]"
                                  :disabled="!item.isUpdate"
                                  search-nested
                                  :tree-data="item.options"
                                  placeholder="请选择"
                                />
                                <!-- 关联类型 -->
                                <dataSelect v-else-if="item.type == 'LINKED'" :disabled="!item.isUpdate" text-info="edit" :model.sync="customForm[item.key]" :config="item" :placeholder="item.placeholder" @change="dataSelectChange($event,item.key)" />
                                <!-- 引用类型 -->
                                <div v-else-if="item.type == 'QUOTE'">
                                  <vone-remote-user
                                    v-if="item.quoteType === 'user'"
                                    v-model="customForm[item.key]"
                                    disabled
                                  />
                                  <el-input v-else v-model="customForm[item.key]" disabled placeholder="" />
                                </div>
                              </el-form-item>
                            </el-col>
                            <el-col v-else :span="24">
                              <el-form-item label="关联系统" prop="systemId">
                                <!-- 远程搜索下拉 -->
                                <template>
                                  <el-select
                                    v-model="customForm.systemId"
                                    remote
                                    placeholder="选择关联系统"
                                    clearable
                                    filterable
                                    :disabled="!item.isUpdate"
                                    :remote-method="e => remoteMethod(e, 'systemId')"
                                    @focus="setOptionWidth"
                                    @change="changeProject('systemId')"
                                  >
                                    <el-option
                                      v-for="i in systemList"
                                      :key="i.id"
                                      :label="i.name"
                                      :value="i.id"
                                      :style="{ width: selectOptionWidth }"
                                    >
                                      {{ i.code }}
                                      {{ i.name }}
                                    </el-option>
                                  </el-select>
                                </template>
                              </el-form-item>

                            </el-col>
                          </div>
                          <el-col v-if="customForm.systemId && tradeProjectId" key="productModuleFunctionId" :span="12">
                            <el-form-item label="关联模块" prop="productModuleFunctionId">
                              <template>
                                <vone-tree-select
                                  v-model="customForm.productModuleFunctionId"
                                  search-nested
                                  :tree-data="productModuleFunctionList"
                                  placeholder="请选择关联模块"
                                />

                              </template>
                            </el-form-item>
                          </el-col>
                          <el-col v-if="customForm.productModuleFunctionId && tradeProjectId" key="functionId" :span="12">
                            <el-form-item label="关联交易" prop="functionId">
                              <template>
                                <vone-tree-select
                                  v-model="customForm.functionId"
                                  search-nested
                                  :tree-data="functionList"
                                  placeholder="请选择关联交易"
                                />

                              </template>
                            </el-form-item>
                          </el-col>
                        </el-row>
                      </el-form>
                    </vone-div-wrapper>
                  </div>
                </div>
              </el-tab-pane>

              <el-tab-pane
                v-for="(tab, index) in leftTabs"
                :key="index"
                class="contentBox"
                :label="tab.label"
                :name="tab.name"
              >
                <component
                  :is="tab.name"
                  v-if="tab.active"
                  :ref="tab.name"
                  :type-code="typeCode"
                  :issue-id="fixedForm.id"
                  :issue-info="fixedForm"
                  :product-id="fixedForm.productId"
                  :info-disabled="infoDisabled"
                  :project-id="fixedForm.projectId"
                  :tab-name="tab.label"
                  :show-popup-for-split="showPopupForSplit"
                  @initList="initList"
                  @add-child="$emit('add-child', $event)"
                />
              </el-tab-pane>
            </el-tabs>
          </el-col>

          <el-col v-loading="infoLoading" :span="8" class="rightBox">
            <el-tabs
              v-model="rightTabActive"
              class="vone-tab-line mintabline"
              @tab-click="handleClick($event, rightTabs)"
            >
              <el-tab-pane v-for="tab in rightTabs" :key="tab.name" :label="tab.label" :name="tab.name">
                <vone-comment
                  v-if="rightTabActive == 'comment' && tab.active"
                  :source-id="commentId"
                  :source-type="typeCode"
                />
                <activeTab
                  v-if="rightTabActive == 'active' && tab.active"
                  :form-id="id"
                  :type="typeCode"
                  :source-type="typeCode"
                  :source-id="commentId"
                />
                <activityRecord
                  v-if="rightTabActive == 'activityRecord' && tab.active"
                  :form-id="id"
                  :type="typeCode"
                  :source-type="typeCode"
                  :source-id="commentId"
                />
                <workTime
                  v-if="rightTabActive == 'workTime' && tab.active"
                  :source-id="commentId"
                  :source-type="typeCode"
                  :project-id="fixedForm.projectId"
                  :row-type-code="rowTypeCode"
                />
              </el-tab-pane>
            </el-tabs>
          </el-col>
        </el-row>
      </div>
      <div slot="footer">
        <el-button @click="onClose">取消</el-button>
        <el-button v-if="!infoDisabled" type="primary" :loading="saveLoading" @click="saveInfo">确定</el-button>
      </div>
    </el-dialog>
    <chat-gpt v-if="chatVisible" :visible.sync="chatVisible" :issue-info="fixedForm" />
  </div>
</template>

<script>
// index.js 的相对路径
import index from './index.js' // 名字可以任取
export default index
</script>

<style lang="scss" scoped>
@import './index.scss';
</style>
