const fileMap = {
  ISSUE: 'ISSUE_FILE_UPLOAD',
  BUG: 'BUG_FILE_UPLOAD',
  TASK: 'TASK_FILE_UPLOAD',
  RISK: 'RISK_FILE_UPLOAD',
  IDEA: 'IDEA_FILE_UPLOAD',
  TESTREQ: 'TESTREQ_FILE_UPLOAD'
}

const iconMap = {
  tagId: 'el-icon-icon-fill-biaoqian',
  planStime: 'el-icon-icon-fill-wanchengshijian',
  startTime: 'el-icon-icon-fill-wanchengshijian',
  planEtime: 'el-icon-icon-fill-wanchengshijian',
  endTime: 'el-icon-icon-fill-wanchengshijian',
  expectedTime: 'el-icon-icon-fill-wanchengshijian',
  stateCode: 'el-icon-icon-fill-zhuangtai'
}

import { apiAlmGetInfo } from '@/api/vone/project/issue'
import {
  apiVaBaseCustomFormField,
  apiVaBaseCustomFormFieldProgram,
  apiVaBaseCustomFormFieldProject
} from '@/api/vone/base/customForm'
import { apiBaseAllUserNoPage } from '@/api/vone/base/user'
import { getSourceById } from '@/api/vone/base/source'

import dataSelect from '@/components/CustomEdit/components/data-select'
// 处理-连接拼接成驼峰命名
function toHump(str) {
  const reg = /-(\w)/g
  return str.replace(reg, function($0, $1) {
    return $1.toUpperCase()
  })
}
// 处理首字母大写
function upperFirst(str) {
  return str.charAt(0).toUpperCase() + str.slice(1)
}
// 读取当前文件夹下components文件夹下.vue文件
const requireComponents = require.context('../CustomEdit/commonTab/', false, /\.vue$/)

const componentsObj = {}
requireComponents.keys().forEach(filePath => {
  const componentName = upperFirst(toHump(filePath.split('/')[1].replace(/\.vue$/, '')))
  const componentConfig = requireComponents(filePath)
  componentsObj[componentName] = componentConfig.default || componentConfig
})
import activeTab from '../CustomEdit/commonTab/active.vue'
import _ from 'lodash'
import workTime from '../CustomEdit/commonTab/work-time.vue'
import activityRecord from '../CustomEdit/commonTab/activity-record.vue'

export default {
  name: 'VoneCustomEdit',
  components: {
    activeTab,
    workTime,
    dataSelect,
    activityRecord,
    ...componentsObj
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: undefined
    },
    id: {
      type: String,
      default: undefined
    },
    infoDisabled: {
      type: Boolean,
      default: false
    },
    sprintId: {
      type: String,
      default: undefined
    },
    tableList: {
      type: Array,
      default: () => []
    },
    typeCode: {
      type: String,
      default: undefined
    },
    leftTabs: {
      type: Array,
      default: () => []
    },
    rightTabs: {
      type: Array,
      default: () => []
    },
    formId: {
      // 平台配置,预览表单接口,需要根据formId查询模板
      type: String,
      default: undefined
    },
    rowTypeCode: {
      // 需求中心/项目里,从列表数据取到的类型
      type: String,
      default: undefined
    },
    stateCode: {
      // 需求中心/项目里,从列表数据取到的状态
      type: String,
      default: undefined
    },
    rowProjectId: {
      // 需求中心,从列表数据取到的关联项目的项目id
      type: String,
      default: undefined
    },
    // 拆分子项是否弹框
    showPopupForSplit: {
      type: Boolean,
      default: false
    },
    isShowCreateBtn: {
      type: Boolean,
      default: true
    }
  },
  data: function() {
    return {
      iconMap,
      drawerLoading: false,
      fileMap,
      tabActive: 'basic',
      rightTabActive: 'comment',
      currentIndex: undefined, // 当前数据的索引
      customForm: {},
      fixedForm: {},
      otherForm: {},
      basicProperty: [],
      fixedProperty: [],
      customList: [],
      infoLoading: true,
      commentId: '',
      userMap: {}
    }
  },
  mounted() {
    this.commentId = this.id || ''
    this.$nextTick(() => {
      this.rightTabActive = this.rightTabs[0]?.name
      this.rightTabs.forEach(v => {
        if (v.name == this.rightTabActive) {
          v.active = true
        } else {
          v.active = false
        }
      })
      this.getFormTemplete()
    })

    this.currentIndex = this.tableList.findIndex(item => item.id === this.id)

    this.getUserList()

    this.leftTabs.forEach(element => {
      element.active = false
    })
  },
  methods: {
    fileClick(link) {
      if (link && (_.startsWith(link, 'http') || _.startsWith(link, 'https'))) {
        window.open(link, '_blank')
      } else {
        window.open('http://' + link, '_blank')
      }
    },
    async getQuoteType() {
      this.customList.forEach((e, index) => {
        if (e.type == 'QUOTE') {
          const config = JSON.parse(e.config)
          this.getTableConfig(config.relationShipsheet, e, item => {
            e.quoteType = item?.type
            this.$nextTick(() => {
              this.$set(this.customList, index, e)
            })
          })
        }
      })
    },
    async getTableConfig(id, e, callback) {
      const config = JSON.parse(e.config)
      const res = await getSourceById(id)
      if (res.isSuccess) {
        const list = res.data.fields.map(v => {
          v.prop = v.id
          const obj = JSON.parse(v.config)
          return {
            ...v,
            ...obj
          }
        })
        const objs = list.find(e => e.id == config.relationField && !e.primary)
        callback(objs)
      }
    },
    async getUserList() {
      const res = await apiBaseAllUserNoPage()
      if (!res.isSuccess) {
        return
      }
      this.userMap = res.data.reduce((acc, cur) => {
        acc[cur.id] = cur
        return acc
      }, {})
    },
    async getFormTemplete(val) {
      this.drawerLoading = true
      // 需求中心，项目，项目集，查询表单项时调不同的接口url，参数格式也不同
      // 需求中心和其它使用：apiVaBaseCustomFormField
      // 项目使用：apiVaBaseCustomFormFieldProject
      // 项目集：apiVaBaseCustomFormFieldProgram
      const activeApp = this.$route.meta.activeApp
      const params = {
        typeCode: this.rowTypeCode,
        stateCode: this.stateCode,
        projectId: this.rowProjectId || null
      }
      const res =
        activeApp == 'projectm'
          ? await apiVaBaseCustomFormFieldProgram(this.$route.params.id, this.typeCode, params)
          : activeApp == 'project'
            ? await apiVaBaseCustomFormFieldProject(this.$route.params.id, this.typeCode, params)
            : await apiVaBaseCustomFormField(this.typeCode, params)

      this.drawerLoading = false
      if (!res.isSuccess) {
        return
      }

      res.data.forEach(element => {
        element.placeholder = JSON.parse(element.config)?.placeholder
        element.multiple = JSON.parse(element.config)?.multiple
        element.message = JSON.parse(element.config)?.message
        element.validator = JSON.parse(element.config)?.validator
        element.defaultTime = JSON.parse(element.config)?.defaultTime
        element.disabled = element.key == 'planId'
        element.options = JSON.parse(element.config)?.options
        element.type =
          element.type && element.type.code != 'CUSTOM' ? element.type.code : JSON.parse(element.config)?.customType
      })

      // 所有基本属性
      const basicAll = res.data && res.data.length ? res.data.filter(r => r.isBasic && r.isShow && r.state) : []

      // 其它基本属性
      const other = basicAll.filter(r => r.key == 'files' || r.key == 'description')

      // 固定基本属性
      const fixed = _.difference(basicAll, other)

      // 固定基本属性
      this.fixedProperty = fixed.sort(function(a, b) {
        return a.sort - b.sort
      })
      // 固定属性[文件和描述]
      this.basicProperty = other.sort(function(a, b) {
        return a.sort - b.sort
      })
      // 排序
      // 自定义属性
      const custom = res.data && res.data.length ? res.data.filter(r => !r.isBasic && r.isShow && r.state) : []
      this.customList = custom.sort(function(a, b) {
        return a.sort - b.sort
      })

      await this.getIssueInfo(val)
      this.getQuoteType()
    },
    initList() {
      this.$emit('initList')
    },

    onClose() {
      this.splitFlag = false
      this.$emit('update:visible', false)
    },
    handleClick(event, list) {
      list.forEach(v => {
        if (v.name == event.name) {
          v.active = true
        } else {
          v.active = false
        }
      })
    },

    // 详情
    async getIssueInfo(val) {
      this.infoLoading = true
      this.drawerLoading = true

      const urlMap = {
        ISSUE: `/api/alm/alm/requirement/${val || this.id}`,
        BUG: `/api/alm/alm/bug/${val || this.id}`,
        TASK: `/api/alm/alm/task/${val || this.id}`,
        RISK: `/api/alm/alm/risk/${val || this.id}`,
        IDEA: `/api/alm/alm/idea/${val || this.id}`,
        TESTREQ: `/api/alm/alm/testreq/${val || this.id}`
      }

      const res = await apiAlmGetInfo(urlMap[this.typeCode])

      if (!res.isSuccess) {
        return
      }

      if (res.data?.tagId?.length && res.data.echoMap?.tagId) {
        this.$set(res.data, 'tagId', res.data.echoMap.tagId.map(r => r.name) || [])
      }
      this.customForm = res.data

      if (this.customForm.failAddBug == '0') {
        this.customForm.failAddBug = '否'
      }
      if (this.customForm.failAddBug == '1') {
        this.customForm.failAddBug = '是'
      }
      this.fixedForm = res.data
      this.otherForm = res.data

      this.infoLoading = false
      this.drawerLoading = false
      // 如果需求没有关联项目,不允许填报工时
      this.$emit('hideTab', res.data.projectId ? 1 : 0)
    },

    // 上一条
    dataPrev() {
      if (this.currentIndex === 0) {
        this.$message.warning('已经是第一条数据啦')
        return
      }
      this.currentIndex--
      this.getFormTemplete(this.tableList[this.currentIndex].id)
      this.commentId = this.tableList[this.currentIndex].id
    },
    // 下一条
    dataNext() {
      if (this.currentIndex === this.tableList.length - 1) {
        this.$message.warning('已经是最后一条数据啦')
        return
      }
      this.currentIndex++

      this.getFormTemplete(this.tableList[this.currentIndex].id)
      this.commentId = this.tableList[this.currentIndex].id
    }
  }
}
