$border-color: #f0f0f0;
$label-background-color: #fafafa;
.vone-desc {
  .vone-desc-item__label{
    text-wrap: auto;
  }
  .vone-desc-row {
    margin-top:12px
  }
  width: 100%;
  table-layout: fixed;
  border-collapse: collapse;
  &__ {
    &bordered {
      table-layout: auto;
      .vone-desc {
        &-item__ {
          &label {
            white-space: nowrap;
            background-color: $label-background-color;
            font-weight: 400;
            
          }
          &label,
          &content {
            border: 1px solid $border-color;
            padding: 16px 24px;
            
          }
        }
      }
    }
  }
  &-item {
    line-height: 32px;
    
    &__ {
      &label {
        margin-top: 12px;
        text-align: left;
        color: var(--font-second-color);
        margin-right: 8px;
        width: 98px;
        // &::after {
        //   content: ":";
        //   margin: 0 8px 0 2px;
        // }
      }
      &content {
        display: inline-block;
        width: calc(100% - 90px);
        margin-top: 12px;
        color: var(--font-main-color);
      }
      &ellipsis {
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
      &empty {
        color: #999;
      }
      &content__text{
        display: inline-block;
        width: 100%;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        vertical-align: middle;
      }
    }
  }
	.vone-desc-item_section{
		display: flex;
	}
}

