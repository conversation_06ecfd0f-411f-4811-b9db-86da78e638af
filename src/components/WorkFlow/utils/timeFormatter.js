/**
 * 时间格式化工具函数
 */
/**
 * 将秒数转换为时分秒格式
 * @param {number} seconds - 秒数
 * @returns {string} 格式化后的时间字符串
 *
 */
export function formatDuration(ms) {
  if (ms < 0) ms = 0

  const minutes = Math.floor(ms / (1000 * 60)) % 60
  const hours = Math.floor(ms / (1000 * 60 * 60)) % 24
  const days = Math.floor(ms / (1000 * 60 * 60 * 24))

  const parts = []
  if (days > 0) parts.push(`${days}天`)
  if (hours > 0 || parts.length > 0) parts.push(`${hours}时`)
  if (minutes > 0 || parts.length > 0) parts.push(`${minutes}分`)

  return parts.join('')
}

/**
 * 将秒数转换为简化的时间格式（用于空间有限的场景）
 * @param {number} seconds - 秒数
 * @returns {string} 简化格式的时间字符串
 *
 * @example
 * formatTimeShort(0) // '0s'
 * formatTimeShort(30) // '30s'
 * formatTimeShort(90) // '1m30s'
 * formatTimeShort(3661) // '1h1m'
 */
export function formatTimeShort(seconds) {
  if (!seconds || seconds <= 0 || !Number.isFinite(seconds)) {
    return '0s'
  }

  const totalSeconds = Math.floor(seconds)
  const hours = Math.floor(totalSeconds / 3600)
  const minutes = Math.floor((totalSeconds % 3600) / 60)
  const remainingSeconds = totalSeconds % 60

  const parts = []

  if (hours > 0) {
    parts.push(`${hours}h`)
    // 如果有小时，只显示分钟，不显示秒
    if (minutes > 0) {
      parts.push(`${minutes}m`)
    }
  } else if (minutes > 0) {
    parts.push(`${minutes}m`)
    if (remainingSeconds > 0) {
      parts.push(`${remainingSeconds}s`)
    }
  } else {
    parts.push(`${remainingSeconds}s`)
  }

  return parts.join('')
}

/**
 * 验证时间数据是否有效
 * @param {any} time - 待验证的时间数据
 * @returns {boolean} 是否为有效的时间数据
 */
export function isValidTime(time) {
  // 支持数字和字符串类型的数字
  const num = typeof time === 'string' ? parseFloat(time) : time
  return typeof num === 'number' && Number.isFinite(num) && num >= 0
}

/**
 * 获取耗时显示文本
 * @param {number} spendTime - 耗时秒数
 * @param {boolean} useShortFormat - 是否使用简化格式
 * @returns {string} 完整的耗时显示文本
 */
export function getSpendTimeText(spendTime, useShortFormat = false) {
  if (!isValidTime(spendTime)) {
    return '耗时: 0秒'
  }

  // 确保转换为数字
  const timeNum = typeof spendTime === 'string' ? parseFloat(spendTime) : spendTime
  const formattedTime = useShortFormat ? formatDuration(timeNum) : formatTime(timeNum)
  return `耗时: ${formattedTime}`
}
