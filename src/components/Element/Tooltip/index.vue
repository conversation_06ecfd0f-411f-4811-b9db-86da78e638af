<script>
import { Tooltip } from 'element-ui'
import debounce from 'throttle-debounce/debounce'
import Vue from 'vue'
export default {
  name: 'ElTooltip',
  extends: Tooltip,
  props: {
    ...Tooltip.props
  },
  beforeCreate() {
    if (this.$isServer) return

    this.popperVM = new Vue({
      data: { node: '' },
      render(h) {
        return this.node
      }
    }).$mount()

    this.debounceClose = debounce(300, () => this.handleClosePopper())
  }
}
</script>
