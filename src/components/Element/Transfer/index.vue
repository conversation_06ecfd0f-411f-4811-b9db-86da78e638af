<template>
  <div class="el-transfer">
    <transfer-panel
      ref="leftPanel"
      v-bind="$props"
      :data="sourceData"
      :title="titles[0] || t('el.transfer.titles.0')"
      :default-checked="leftDefaultChecked"
      :placeholder="filterPlaceholder || t('el.transfer.filterPlaceholder')"
      @checked-change="onSourceCheckedChange"
    >
      <slot name="left-search" />
      <slot name="left-footer" />
    </transfer-panel>
    <div class="el-transfer__buttons">
      <el-button
        type="primary"
        :class="['el-transfer__button', hasButtonTexts ? 'is-with-texts' : '']"
        :disabled="rightChecked.length === 0"
        @click.native="addToLeft"
      >
        <i class="el-icon-arrow-left" />
        <span v-if="buttonTexts[0] !== undefined">{{ buttonTexts[0] }}</span>
      </el-button>
      <el-button
        type="primary"
        :class="['el-transfer__button', hasButtonTexts ? 'is-with-texts' : '']"
        :disabled="leftChecked.length === 0"
        @click.native="addToRight"
      >
        <span v-if="buttonTexts[1] !== undefined">{{ buttonTexts[1] }}</span>
        <i class="el-icon-arrow-right" />
      </el-button>
    </div>
    <transfer-panel
      ref="rightPanel"
      v-bind="$props"
      :data="targetData"
      :title="titles[1] || t('el.transfer.titles.1')"
      :default-checked="rightDefaultChecked"
      :placeholder="filterPlaceholder || t('el.transfer.filterPlaceholder')"
      @checked-change="onTargetCheckedChange"
    >
      <slot name="right-search" />
      <slot name="right-footer" />
    </transfer-panel>
  </div>
</template>

<script>
import TransferPanel from './transfer-panel.vue'
import ElementUI from 'element-ui'
export default {
  name: 'ElTransfer',
  components: {
    TransferPanel
  },

  mixins: [ElementUI.Transfer],
  props: {
    searchSlot: {
      type: Boolean,
      default: false
    },
    footerSlot: {
      type: Boolean,
      default: false
    }
  }
}
</script>
