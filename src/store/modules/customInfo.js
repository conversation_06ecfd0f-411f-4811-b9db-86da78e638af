const state = {
  // 由于没有持久化。所以直接用 localStorage吧。后续再调整。
  // 所有props的字段保留。其他地方有用到的地方。
  visible: false, // 是否显示弹框。目前改造成路由，不需要了
  title: undefined, // 页面标题
  id: undefined, // issue id
  infoDisabled: false, // form表单是否可以编辑
  sprintId: undefined, // 1
  tableList: [], // 需求列表，用来切换上一个和下一个
  typeCode: undefined, // ISSUE
  leftTabs: [], // 左侧Tabs
  rightTabs: [], // 右侧Tabs
  formId: undefined, // 平台配置,预览表单接口,需要根据formId查询模板
  rowTypeCode: undefined, // 需求中心/项目里,从列表数据取到的类型
  stateCode: undefined, // 需求中心/项目里,从列表数据取到的状态
  rowProjectId: undefined, // 需求中心,从列表数据取到的关联项目的项目id
  showPopupForSplit: false, // 拆分子项是否弹框
  isShowCreateBtn: true // 是否显示新建按钮
}
const mutations = {}
const actions = {}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
