FROM harbor.jettech.com/jettechtools/nginx:latest.x86_64
MAINTAINER  <EMAIL>

WORKDIR /opt/jettech

COPY jettech-ui /opt/jettech/jettech-ui
COPY docker-entrypoint.sh  /opt/jettech/
COPY nginx.conf /etc/nginx/nginx.conf
COPY conf.d /etc/nginx/conf.d
RUN rm -rf /etc/nginx/conf.d/default.conf

ENV JETTECH_GATEWAY_IP="127.0.0.1" \
JETTECH_GATEWAY_PORT=80

ENTRYPOINT sh /opt/jettech/docker-entrypoint.sh && nginx -g "daemon off;"
