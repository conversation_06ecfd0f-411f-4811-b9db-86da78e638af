#!/bin/bash

<PERSON><PERSON><PERSON><PERSON>_DEFAULT_IP="*************"
JET<PERSON>CH_DEFAULT_PORT=8770
JETTECH_DEFAULT_PRODUCT="jettong"
JETTECH_DEFAULT_UI_VERSION="develop"

JET<PERSON><PERSON>_GATEWAY_IP=${1:-$JETTECH_DEFAULT_IP}
JETTECH_GATEWAY_PORT=${2:-$JETTECH_DEFAULT_PORT}
JETTECH_PRODUCT=${3:-$JETTECH_DEFAULT_PRODUCT}
JETTECH_UI_VERSION=${4:-$JETTECH_DEFAULT_UI_VERSION}

JETTECH_CONTAINER_NAME="${JETTECH_PRODUCT}-ui"

JETTECH_IMAGE="harbor.jettech.com/${JETTECH_PRODUCT}/${JETTECH_PRODUCT}-ui:$JETTECH_UI_VERSION"

if [ "$(docker ps -aq --filter name=^/${JET<PERSON><PERSON>_CONTAINER_NAME}$)" ]; then
    echo "容器 ${JETTECH_CONTAINER_NAME} 已存在，正在停止并删除...且镜像${JETTECH_IMAGE}也删除"
    docker stop ${JETTECH_CONTAINER_NAME} > /dev/null
    docker rm ${JETTECH_CONTAINER_NAME} > /dev/null
    docker rmi ${JETTECH_IMAGE} > /dev/null
fi

echo "正在启动容器 ${JETTECH_CONTAINER_NAME}..."
docker run  --name ${JETTECH_CONTAINER_NAME} -it --net=host -e JETTECH_GATEWAY_IP=${JETTECH_GATEWAY_IP}  -e JETTECH_GATEWAY_PORT=${JETTECH_GATEWAY_PORT} -d ${JETTECH_IMAGE}
if [ $? -eq 0 ]; then
    echo "容器 ${JETTECH_CONTAINER_NAME} 启动成功！"
    echo "容器ID: $(docker ps -q --filter name=^/${JETTECH_CONTAINER_NAME}$)"
else
    echo "容器 ${JETTECH_CONTAINER_NAME} 启动失败，请检查参数或镜像是否存在。"
    exit 1
fi
