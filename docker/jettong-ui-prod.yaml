apiVersion: v1
kind: ConfigMap
metadata:
  name: jettong-configmap
  namespace: jettong-prod
data:
  JETTECH_NACOS_IP: 'nacos.prod.jettech.cn'
  JETTECH_NACOS_PORT: '80'
  JETTECH_NACOS_USERNAME: 'nacos'
  JETTECH_NACOS_PASSWORD: 'nacos'
  JETTONG_NACOS_NAMESPACE: 'jettong-prod'
  JETTONG_NACOS_GROUP: 'jettong'
  JETTONG_GATEWAY_IP: '*************'
  JETTONG_GATEWAY_PORT: '8770'
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: jettong-ui
  namespace: jettong-prod
  annotations: 
    nginx.ingress.kubernetes.io/proxy-body-size: 3096m
spec:
  rules:
  - host: jettong.prod.jettech.cn
    http:
      paths:
      - backend:
          service:
            name: jettong-ui
            port:
              number: 80
        path: /
        pathType: Prefix
---
apiVersion: v1
kind: Service
metadata:
  labels: {name: jettong-ui}
  name: jettong-ui
  namespace: jettong-prod
spec:
  ports:
  #- {name: t80, nodePort: 80, port: 80, protocol: TCP, targetPort: t80}
  - {name: t80, port: 80, protocol: TCP, targetPort: t80}
  selector: {name: jettong-ui}
  #type: NodePort
  type: ClusterIP
  #clusterIP: None

---
apiVersion: apps/v1
kind: Deployment
metadata:
  namespace: jettong-prod
  name: jettong-ui
  labels: {name: jettong-ui}
spec:
  replicas: 1
  selector:
    matchLabels: {name: jettong-ui}
  template:
    metadata:
      name: jettong-ui
      labels: {name: jettong-ui}
    spec:
      containers:
      - name: jettong-ui
        imagePullPolicy: Always
        image: harbor.jettech.com/jettong/jettong-ui:v0.2
        #command: ["/bin/sh","-c"," sh /opt/docker-entrypoint.sh&&while true;do sleep 1000;done"]
        #command: ["/bin/sh","-c","while true;do sleep 1000;done"]
        #command: ["/bin/sh","-c","sh /opt/docker-entrypoint.sh&&nginx -g 'daemon off';"]
        envFrom:
        - configMapRef:
            name: jettong-configmap
            optional: true
        securityContext:
          privileged: true
        ports:
        - {containerPort: 80, name: t80, protocol: TCP}
      restartPolicy: Always #Never
