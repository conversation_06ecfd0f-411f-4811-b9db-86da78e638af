一:构建镜像 版本号是:tag 和package.js中定义的version保持一致
1.npm install --registry=http://repo.jettech.com/repository/jettech-npm-public
2.npm run build
3.cp -a dist docker/jettong-ui
4.docker build  --no-cache  --compress -t harbor.jettech.com/jettong/jettong-ui:develop -f Dockerfile  .

二:运行
1.涉及到的环境变量a
JETTECH_GATEWAY_IP=127.0.0.1		#后端网关IP   默认值
JETTECH_GATEWAY_PORT=80			#后端网关端口 默认值

2.运行指令
2.1默认值：
docker run --name jettong-ui -it --net=host -d harbor.jettech.com/jettong/jettong-ui:develop
2.2 根据实际情况指定后端网关服务地址和端口
docker run --name jettong-ui -it --net=host -e JETTECH_GATEWAY_IP= -e JETTECH_GATEWAY_PORT=  -d harbor.jettech.com/jettong/jettong-ui:v0.2

=================k8s==============
kubectl apply -f  jettong-ui-dev.yaml 
