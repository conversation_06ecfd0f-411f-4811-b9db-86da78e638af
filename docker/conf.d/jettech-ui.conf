server {
    listen 80;
    server_name _;

    location / {
      root /opt/jettech/jettech-ui;
      try_files $uri $uri/ /index.html;
      index index.html index.htm;
    }
    location /api- {
      add_header 'Access-Control-Allow-Origin' '*';
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header REMOTE-HOST $remote_addr;
      proxy_set_header X-Real-Port $remote_port;
      proxy_set_header X-Forwarded-For $remote_addr;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_headers_hash_max_size 512;
      proxy_headers_hash_bucket_size 128;
      proxy_set_header X-Forwarded-For $http_x_forwarded_for;
      proxy_pass http://*************:8769;
      rewrite  ^/api/(.*)$ /$1 break;
      proxy_connect_timeout 1200000s;
      proxy_read_timeout 360000s;
      proxy_send_timeout 120000s;
    }
    location /api {
      rewrite  ^/api/(.*)$ /$1 break;
      proxy_pass http://JETTECH_GATEWAY_IP:JETTECH_GATEWAY_PORT;
      proxy_connect_timeout 1200000s; 
      proxy_read_timeout 36000000s;
      proxy_send_timeout 12000000s;
    }
#    location /status {
#        vhost_traffic_status_display;
#        vhost_traffic_status_display_format html;
#    }
}
