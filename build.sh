#!/bin/bash

JET<PERSON><PERSON>_DEFAULT_VERSION="develop"
JET<PERSON>CH_DEFAULT_PRODUCT="jettong"
JETTECH_DEFAULT_REPO="harbor.jettech.com"
JETTECH_DEFAULT_DOCKERFILE_PATH="docker/Dockerfile"
JETTECH_DEFAULT_BUILD_CONTEXT="docker"
JETTECH_DEFAULT_SRC_DIR="dist"

usage() {
    echo "使用方法: $0 [选项]"
    echo "选项:"
    echo "  -v, --version    镜像版本号 (默认: ${JETTECH_DEFAULT_VERSION})"
    echo "  -p, --product    产品名称 (默认: ${JETTECH_DEFAULT_PRODUCT})"
    echo "  -u, --username    产品名称 (默认: 空)"
    echo "  -P, --password    产品名称 (默认: 空)"
    echo "  -h, --help       显示帮助信息"
    exit 1
}

while [[ $# -gt 0 ]]; do
    case "$1" in
        -v|--version)
            JETTECH_VERSION="$2"
            shift 2
            ;;
        -p|--product)
            JETTECH_PRODUCT="$2"
            shift 2
            ;;
        -u|--username)
            JETTECH_DOCKER_USER="$2"
            shift 2
            ;;
        -P|--password)
            JETTECH_DOCKER_PWD="$2"
            shift 2
            ;;
        -h|--help)
            usage
            ;;
        *)
            echo "错误: 未知选项 $1"
            usage
            ;;
    esac
done

# 设置默认参数
JETTECH_VERSION=${JETTECH_VERSION:-$JETTECH_DEFAULT_VERSION}
JETTECH_PRODUCT=${JETTECH_PRODUCT:-$JETTECH_DEFAULT_PRODUCT}
JETTECH_REPO=${JETTECH_REPO:-$JETTECH_DEFAULT_REPO}
JETTECH_DOCKERFILE_PATH=${JETTECH_PATH:-$JETTECH_DEFAULT_PATH}
JETTECH_BUILD_CONTEXT=${JETTECH_BUILD_CONTEXT:-$JETTECH_DEFAULT_BUILD_CONTEXT}
JETTECH_SRC_DIR=${JETTECH_SRC_DIR:-$JETTECH_DEFAULT_SRC_DIR}

# 计算镜像标签
JETTECH_IMAGE_TAG="${JETTECH_REPO}/${JETTECH_PRODUCT}/${JETTECH_PRODUCT}-ui:${JETTECH_VERSION}"
JETTECH_DEST_DIR="${JETTECH_BUILD_CONTEXT}/jettech-ui"

# 执行构建流程
echo "===== 开始Docker镜像构建流程 ====="
echo "产品名称: ${JETTECH_PRODUCT}"
echo "镜像版本: ${JETTECH_VERSION}"
echo "目标镜像: ${JETTECH_IMAGE_TAG}"

# 1. 复制构建产物到docker目录
echo -e "\n[1/4] 复制构建产物到${JETTECH_DEST_DIR}"
cp -a "${JETTECH_SRC_DIR}" "${JETTECH_DEST_DIR}" || {
    echo "错误：复制文件失败，请检查${JETTECH_SRC_DIR}是否存在"
    exit 1
}

# 2. 构建Docker镜像
echo -e "\n[2/4] 构建Docker镜像"
docker build -t "${JETTECH_IMAGE_TAG}" -f "${JETTECH_DOCKERFILE_PATH}" "${JETTECH_BUILD_CONTEXT}" || {
    echo "错误：镜像构建失败"
    exit 1
}

# 3. 登录仓库并推送镜像
echo -e "\n[3/4] 推送镜像到仓库"
if [ -z "${JETTECH_DOCKER_USER:-}" ] || [ -z "${JETTECH_DOCKER_PWD:-}" ]; then
    echo "警告：未设置JETTECH_DOCKER_USER和JETTECH_DOCKER_PWD环境变量，将尝试使用已有登录状态"
    docker push "${JETTECH_IMAGE_TAG}" || {
        echo "错误：推送失败，请先设置JETTECH_DOCKER_USER和JETTECH_DOCKER_PWD环境变量"
        exit 1
    }
else
    docker login --username="${JETTECH_DOCKER_USER}" --password="${JETTECH_DOCKER_PWD}" harbor.jettech.com &&
    docker push "${JETTECH_IMAGE_TAG}" || {
        echo "错误：登录或推送失败"
        exit 1
    }
fi

# 4. 清理本地镜像
echo -e "\n[4/4] 清理本地镜像"
docker rmi -f "${JETTECH_IMAGE_TAG}" || {
    echo "警告：清理镜像失败，可能已被其他进程使用"
}

echo -e "\n===== 构建流程完成 ====="
echo "成功推送镜像: ${JETTECH_IMAGE_TAG}"
